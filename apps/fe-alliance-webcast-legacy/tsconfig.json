{"compilerOptions": {"target": "es5", "checkJs": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "paths": {"@src/*": ["./src/*"], "@static/*": ["./src/static/*"], "@lib/*": ["./src/lib/*"], "@kit/*": ["./src/common/kit/*"], "@common/*": ["./src/common/*"], "@components/*": ["./src/components/*"], "components/*": ["./src/components/*"], "common/*": ["./src/common/*"], "types/*": ["./src/types/*"], "static/*": ["./src/static/*"], "@alliance-mobile/utils/*": ["../../libs/global/utils/src/*"], "@alliance-mobile/utils": ["../../libs/global/utils/src/"], "@alliance-mobile/perf": ["../../libs/global/perf/src/"]}, "allowJs": true, "lib": ["es2016", "es2017", "dom"], "esModuleInterop": true, "jsx": "react", "strict": true, "noImplicitAny": false, "types": ["react", "node"], "moduleResolution": "Node", "outDir": "", "module": "es6", "composite": true, "rootDir": "src"}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.spec.ts"], "typeRoots": ["./src/typings", "./node_modules/@types"], "references": [{"path": "../../libs/platform-merchant-common"}, {"path": "../../libs/global/utils"}, {"path": "../../libs/global/event"}, {"path": "../../libs/global/constants"}, {"path": "../../libs/global/perf"}]}