{"name": "fe-alliance-webcast-legacy", "version": "0.0.3", "description": "直播选品", "scripts": {"prod": "NODE_ENV=production eden build", "start": "EDEN_MONOREPO=true eden-start --no-cli-interface --max-old-space-size 4096", "build": "EDEN_MONOREPO=true eden-build --no-cli-interface --max-old-space-size 4096", "proxy": "eden proxy -c eden.proxy.ts", "gen_icon": "node ./scripts/gen_icon.js"}, "dependencies": {"@ad/feelgood-sdk": "^0.11.81", "@alliance-mobile/constants": "workspace: *", "@alliance-mobile/event": "workspace: *", "@alliance-mobile/perf": "workspace: *", "@alliance-mobile/platform-merchant-common": "workspace:*", "@alliance-mobile/utils": "workspace: *", "@babel/runtime-corejs3": "^7.20.6", "@bridge/webcast": "^1.3.19-beta.6", "@byted-flash/utils": "^1.0.52", "@byted/fe-utilx": "^0.6.15-5", "@byted/hooks": "^2.47.0", "@byted/ies-schema": "^1.1.3", "@ecom/auxo-mobile": "0.0.1-beta.36", "@ecom/babel-coverage-plugin": "^1.0.3", "@ecom/coverage-upload-plugin": "^1.0.19-alpha.1", "@ecom/coverage-uploader": "^1.0.6", "@ecom/lite-store": "^0.1.5", "@ecom/number-display": "^1.0.2", "@ecom/smaller-image": "0.1.8", "@ies/common-polyfill": "^1.0.5", "@ies/cora": "^0.5.35", "@ies/create-eden-config": "^0.0.2", "@ies/eden-lint": "^3.17.3", "@ies/viewport_units_polyfill": "0.0.6", "@ies/webcast-via": "^0.7.10", "@sentry/browser": "^5.17.0", "ahooks": "^3.1.9", "byted-via-aweme": "^2.28.2", "classnames": "^2.2.6", "copy-to-clipboard": "^3.3.1", "dayjs": "^1.11.7", "fastclick": "^1.0.6", "globby": "^13.1.3", "intersection-observer": "^0.11.0", "lodash": "4.17.21", "lodash-es": "^4.17.15", "mescroll.js": "^1.4.2", "normalize.css": "^8.0.0", "qs": "^6.9.3", "rc-tooltip": "^4.2.0", "react": "16.14.0", "react-beautiful-dnd": "^13.0.0", "react-dom": "16.14.0", "react-hot-loader": "^4.12.20", "react-modal": "^3.11.2", "react-router-dom": "^4.3.1", "resize-observer-polyfill": "^1.5.1", "swiper": "^6.8.4"}, "devDependencies": {"@babel/core": "7.2.0", "@babel/preset-react": "^7.7.4", "@babel/preset-typescript": "^7.3.3", "@byted/babel-preset-coverage": "^1.1.5", "@commitlint/cli": "^8.1.0", "@commitlint/config-conventional": "^7.1.2", "@ecom/babel-auto-report-exception-plugin": "^0.1.9", "@ies/eden-plugin-sass-webpack": "^1.3.60", "@ies/eden-plugin-semi-webpack": "^1.38.12", "@ies/eden-plugin-vue-webpack": "^1.2.61", "@ies/eden-proxy": "^2.22.3", "@ies/eden-web-build": "^2.0.79", "@ies/filter-xss-webpack-plugin": "^3.1.0", "@slardar/webpack-plugin": "^1.1.2", "@types/lodash-es": "^4.17.5", "@types/node": "^13.13.2", "@types/qs": "^6.9.1", "@types/react": "^16.14.0", "@types/react-dom": "^16.9.9", "@types/react-modal": "^3.10.5", "@types/react-router-dom": "^5.1.4", "babel-loader": "^8.2.2", "core-js": "^3.6.5", "glob": "^7.1.6", "husky": "^1.3.1", "lint-staged": "^9.2.5", "postcss-px-to-viewport": "^1.1.1", "react-hot-loader": "^4.12.20", "react-router-dom": "^4.3.1", "typescript": "^4.9.4", "webpack-merge": "^5.8.0"}}