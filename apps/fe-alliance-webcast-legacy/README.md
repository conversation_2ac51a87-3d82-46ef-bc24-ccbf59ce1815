fe_ecommerce_webcast

# Ecommerce Webcast 开发规范，请注意遵守

## 目录规范

- pages 下的页面文件夹按 业务\_页面名 来命名，将同类业务进行归类划分
- lib/util 收敛公共方法
- common/bridge 收敛跨端调用

## 组件设计规范

- 统一的组件命名空间前缀，在最外层 className 均需加上这个前缀
- 组件 style 和 className, 自定义属性值需要支持透传
- 样式均为 style.scss 文件
- 组件接口封装尽量简洁，并且可以通过方法调用的就不要使用自定义标签再通过各种属性控制，如 Loading, Modal, Toast 。 并且做之前跟 UI 确认下多端的样式规范。
- 公共方法和组件要求都要有符合 JS Doc 的注释, 方便引入提示

## 层级规范

- 层级不得超过 2000 (为 Toast 的当前层级)
- 覆盖全屏(mask 等)的从 1000 开始
- Modal 默认 1500

## 组件引入规范

- 组件命名统一为 index.tsx | index.ts
- 如果复杂的 utils 和组件 index 文件只作为统一对外输出，具体代码拆分到其它文件
- 不要把非通用业务组件放到 commom 或 components 里
- 同类功能实现不要出现多种，保持扩展复用
- 新增或修改公共组件需要群里同步，review 后提交，不要私自修改

## 其它

- 复用的静态资源都放到 static 内，并且后续图片统一用 images, 按归类的形式划分二级文件
- 公共声明文件统一使用 typings
- 之前没做屏幕自适应，目前采用 VW 的形式，之前加的@ies/viewport_units_polyfill 是给 RN 的，并且其实根本没使用...

# 仓库简介

本仓库是直播电商 FE 代码仓库，涉及抖音、火山、西瓜、头条四端的直播电商业务

# 目录规范

```
├── src // 主目录
│   ├── pages // 页面入口
│   │   ├── merch-picking-recommend // 商品推荐选择界面
│   │   ├── merch_picking // 核心商品选择功能
│   │   └── pid_binding // 商品ID绑定界面
│   ├── components // 公共组件
│   │   ├── UI Components: Button, Checkbox, Modal, NavBar, Tabs, Toast
│   │   ├── Form Elements: NumberKeyboard, Radio, Switch
│   │   ├── Layout Components: Layout, Page, WebcastLayout
│   │   ├── Common Features: Loading, Empty, LazyImg, Loadmore
│   │   ├── Icons: Various icon components with platform-specific variations
│   │   └── Promoting: Alliance authentication and confirmation components
│   ├── common
│   │   ├── bridge // 原生桥接通信
│   │   ├── constants // 应用常量和页面定义
│   │   ├── kit // 工具函数、环境检测和API客户端
│   │   ├── styles // 全局样式、混合和商品选择主题
│   │   ├── reportAlog // 分析报告
│   │   └── url // URL处理工具
│   ├── lib
│   │   ├── dom // DOM操作工具
│   │   ├── env_detect // 环境检测
│   │   ├── error // 错误处理
│   │   ├── request // HTTP请求客户端
│   │   ├── scroll // 滚动管理
│   │   ├── storage // 存储工具
│   │   └── util // 各种工具函数
│   ├── types // TypeScript接口
│   │   ├── account.ts
│   │   ├── common.ts
│   │   ├── jsb.ts
│   │   ├── product.ts
│   │   ├── request.ts
│   │   └── schema.ts
│   └── assets // 资源文件
│       ├── fonts
│       └── images
├── test // 单测
├── config // build相关文件
└── build // 构建产物

```

# Git 规范

## 分支

- master 分支：线上稳定分支
- test 分支：测试环境分支
- feature 分支：功能开发分支
- hotfix 分支：紧急修复线上 bug 分支

## 规范

### 同一分支 git pull 使用 rebase

```
git pull --rebase
```

### 项目分支操作流程示例

1. 接到一个新需求开发，首先切换到 master 分支，更新最新代码

```
git checkout master
git pull --rebase
```

2. 新建 feature 分支，开发功能

```
git checkout -b feature/xxx
...
git add <files>
git commit -m "feat(xxx): commit a"
git commit -m "feat(xxx): commit b"
```

开发过程中有其他需求 master 上线，你需要更新到上线的最新代码

切换到 master 分支并更新 master 分支代码

```
git checkout master
git pull --rebase
```

切回 feature 分支

```
git checkout feature/xxx
git rebase master
```

如果需要提交到远端，且之前已经提交到远端，此时需要强推(强推需慎重！)

```
git push --force
```

3. 切换到 test 分支，更新 test 分支最新代码，将你的 feature 分支合并到 test 分支准备测试

```
# 更新test分支代码
git checkout test
git pull --rebase

# 合并feature/xx分支
git merge feature/xxx --no-ff

# 推到远端
git push
```

4. 需求开发完成，准备上线，用 feature/xxx 分支提 MR，主动发起 Review

5. 上线后发现有线上 bug，需要修复

```
# 当前在 master 分支
git checkout master
git pull --rebase

# 切出 hotfix 分支
git checkout -b hotfix/xxx

... 进行 bug fix 提交

# 合并到test分支进行测试，步骤同上
# 提MR合并上线
```

## git commit 格式

```
<type>(<scope>): <subject>
```

- type: 类型，提交的类别

  - feat: 新功能
  - fix: 修复 bug
  - docs: 文档变动
  - style: 格式调整，对代码实际运行没有改动，例如添加空行、格式化等
  - refactor: bug 修复和添加新功能之外的代码改动
  - perf: 提升性能的改动
  - test: 添加或修正测试代码
  - chore: 构建过程或辅助工具和库（如文档生成）的更改

- scope 修改页面: 主要是这次修改涉及到的页面，简单概括，例如 merch_picking

- subject 修改的描述: 具体的修改描述信息

范例

```
feat(detail): 详情页修改样式
fix(login): 登录页面错误处理
test(list): 列表页添加测试代码
```


# 未完成

- 页面使用meta做统一的IphoneX适配
- 统一的方法库，发布NPM
- 统一的方式库，做Reset和原子样式
- 基础组件继续完善，针对抖火西头做适配
- 之前已经存在旧组件和部分重复文件逐步清理掉
- 引入icon-font
- ES Lint目前没做强制校验
