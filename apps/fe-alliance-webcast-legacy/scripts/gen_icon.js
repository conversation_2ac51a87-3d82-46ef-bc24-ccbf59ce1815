const path = require('path')
const fs = require('fs')
const globby = require('globby')

const imageDir = path.resolve(__dirname, '../src/components/icon/assets')

const icons = globby.sync('*.png', {
  cwd: imageDir,
  absolute: true,
})

const iconManifest = {}

icons.forEach(icon => {
  const fileName = path.basename(icon, '.png')
  iconManifest[fileName] = fs.readFileSync(icon).toString('base64')
})

let iconset = {}
Object.keys(iconManifest).forEach(name => {
  let iconName = `Icon_${name}`
    .replace('#', 'c')
    .replace('.', '')
    .replace('_default', '')
    .replace(/[_|-](\w)/g, (all, letter) => letter.toUpperCase())
  if (iconset[iconName]) {
    iconName += iconset[iconName]
  } else {
    iconset[iconName] = iconset[iconName] ? iconset[iconName] + 1 : 1
  }
  let content = `import React from 'react'
import {BaseIcon,IconProps} from './base'
const ${iconName} = (props: IconProps) => (
  <BaseIcon src={'data:image/png;base64,${iconManifest[name]}'} {...props} />
)
export default ${iconName}
`
  fs.writeFileSync(path.resolve(__dirname, `../src/components/icon/${iconName}.tsx`), content)
})
