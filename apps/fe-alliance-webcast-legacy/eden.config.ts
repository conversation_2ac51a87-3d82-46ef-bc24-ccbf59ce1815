import { createEdenConfig } from '@ies/eden-web-build';
import SlardarWebpackPlugin from '@slardar/webpack-plugin';
import CoverageUploadPlugin from '@ecom/coverage-upload-plugin';
import postcssPxToViewport from 'postcss-px-to-viewport';
import { FilterXssPlugin } from '@ies/filter-xss-webpack-plugin';
import path from 'path';
import { merge } from 'webpack-merge';
// const IS_IOS_GECKO = false;
const APP_ENV = 'aweme';
const { CDN_OUTER_CN, CDN_INNER_CN, BUILD_TYPE, NODE_ENV, CDN_PATH_PREFIX, BUILD_VERSION } = process.env;

const isProduction = NODE_ENV === 'production';
const isOnline = BUILD_TYPE === 'online';

const CDN_RESOURCE_PREFIX = `https://${isOnline ? CDN_OUTER_CN : CDN_INNER_CN}/${CDN_PATH_PREFIX}/`;
// const IOS_GECKO_PROTOCOL = 'about:wk?:';

const CUSTOM_PREFIX = 'ecom/alliance_mobile/webcast';
const CHANNEL_NAME = 'alliance_webcast_legacy';
const GECKO_PREFIX = `//lf-ecom-gr-sourcecdn.bytegecko.com/obj/byte-gurd-source-gr/${CUSTOM_PREFIX}/${CHANNEL_NAME}/`;

export default createEdenConfig({
  runtimeTarget: 'MobileLegacy',
  input: {
    preEntry: [require.resolve('./config/polyfill'), './config/btm-init.ts'],
    overrideEntrySearch: true,
  },

  projectType: 'Web',

  dest: {
    root: 'output',
    resource: '',
    template: 'html',
  },

  output: {
    // 自定义 publishPath
    publicPath() {
      if (isProduction) {
        return GECKO_PREFIX;
      }
      return '/';
    },
    // 强制保持 IOS 和 Android 的 hash 一致性，以备离线化失败时降级到线上 CDN 资源使用
    filename: `[name].bundle.[contenthash:8].js`,
    chunkFilename: `[name].chunk.[contenthash:8].js`,
  },

  abilities: {
    maiev: false,
    define: {
      // 组件库多端样式
      APP_NAME: APP_ENV === 'aweme' ? 'douyin' : JSON.stringify(APP_ENV),
      'process.env.COMMIT_HASH': JSON.stringify(process.env.COMMIT_HASH),
      'process.env.BUILD_VERSION': JSON.stringify(process.env.BUILD_VERSION),
      'process.env.DEV_MODE': JSON.stringify(process.env.NODE_ENV === 'production' ? '' : '1'),
    },
    react: {
      hot: true,
    },
    html: {
      crossorigin: true,
      htmlWebpackPlugin: {
        preload: {
          image: ['pre_logo'],
        },
      },
    },
    ts: {
      useBabelPresetTypeScript: true,
      babel: {
        babelOptions: {
          plugins: process.env.COMMIT_HASH ? ['module:@ecom/babel-coverage-plugin'] : [],
        },
      },
    },
    js: {
      babelOptions: {
        sourceType: 'unambiguous',
      },
      transformRuntime: {
        corejs: 3,
      },
      include: [/node_modules\/@byted\/.+/],
    },
    sass: {
      sassLoaderOptions: {
        additionalData: `$platform: ${'h5'};$appname:${APP_ENV === 'aweme' ? 'douyin' : APP_ENV};`,
      },
      postCSSLoaderOptions: {
        postcssOptions: {
          plugins: [
            postcssPxToViewport({
              unitToConvert: 'px',
              viewportWidth: 375,
              unitPrecision: 3,
              propList: ['*', '!font-size'],
              viewportUnit: 'vw',
              fontViewportUnit: 'vw',
              selectorBlackList: ['px'],
              minPixelValue: 1,
              mediaQuery: false,
              replace: true,
              exclude: [/node_modules/],
            }),
          ],
        },
      },
    },
    resolve: {
      alias: {
        react: path.resolve(process.cwd(), 'node_modules/react'),
        'react-dom': 'react-dom',
        '@src': path.resolve(process.cwd(), 'src'),
        '@lib': path.resolve(process.cwd(), 'src/lib'),
        '@kit': path.resolve(process.cwd(), 'src/common/kit'),
        '@styles': path.resolve(process.cwd(), 'src/common/styles'),
        '@components': path.resolve(process.cwd(), 'src/common/components'),
        '@common': path.resolve(process.cwd(), 'src/common'),
        '@static': path.resolve(process.cwd(), 'src/static'),
        types: path.resolve(process.cwd(), 'src/types'),
        components: path.resolve(process.cwd(), 'src/components'),
        common: path.resolve(process.cwd(), 'src/common'),
        static: path.resolve(process.cwd(), 'src/static'),
        '@babel/runtime-corejs3': path.resolve(process.cwd(), 'node_modules/@babel/runtime-corejs3'),
      },
    },
  },
  plugins: process.env.COMMIT_HASH
    ? [
        new CoverageUploadPlugin({
          gitRepo: 'ecom/fe-alliance-mobile', // 必填，代码库的gitRepo，例如ecom/huatuo-fe
          isDiffSend: true, // 可选，开启增量上报
          interval: 2000, // 可选，上报间隔，默认2s
          diffSendFileNum: 50, //  每次上报的文件数量，默认50
          retryLimit: 5, // 上报失败重试次数，默认5次
        }),
      ]
    : [],
  chain(chain) {
    chain.module
      .rule('image')
      .test(/\.(a?png|jpg|webp|jpeg|gif)$/)
      .set('generator', { publicPath: isProduction ? `${CDN_RESOURCE_PREFIX}` : '/' });

    return chain;
  },

  // 原生配置 对应 webpack.config.js
  raw(options) {
    const rules: any[] = [];

    const babelPresets = ['@babel/preset-react', '@babel/preset-typescript'];

    // 注入插桩代码
    rules.push({
      test: /\.[j|t]sx?$/,
      use: [
        {
          loader: 'babel-loader',
          options: {
            presets: babelPresets,
            plugins: [
              // try-catch插件
              [
                'module:@ecom/babel-auto-report-exception-plugin',
                {
                  bid: 'ecommerce_webcast',
                },
              ],
            ],
          },
        },
      ],
      include: [path.resolve(__dirname, 'src')],
    });

    if (isOnline) {
      options.plugins = [
        ...(options?.plugins ?? []),
        new SlardarWebpackPlugin({
          bid: 'ecommerce_webcast', // 站点bid,必传
          include: [`./output`], // sourcemap文件所在的目录,必传,可填多个
          release: BUILD_VERSION, // 可选参数,没有则传空
        }),
      ];
    }

    options?.plugins?.push(
      // xss防护
      new FilterXssPlugin({
        appId: 'ecommerce_webcast',
        bid: 'ecommerce_webcast',
        region: 'cn',
        reportOnly: 'all',
        log: true,
      })
    );

    const mergeOptions = merge(options, {
      module: {
        rules,
      },
      resolve: {
        extensions: [
          `.${APP_ENV}.js`,
          `.${APP_ENV}.tsx`,
          `.${APP_ENV}.ts`,
          `.${APP_ENV}.d.ts`,
          `.${APP_ENV}.css`,
          `.${APP_ENV}.scss`,
          `.${APP_ENV}.json`,
          '.js',
          '.tsx',
          '.ts',
          '.d.ts',
          '.css',
          '.scss',
          '.json',
        ],
      },
    });
    return mergeOptions;
  },

  dev: {
    port: 4000,
    devMiddlewareConfig: {
      writeToDisk: true,
    },
  },
});
