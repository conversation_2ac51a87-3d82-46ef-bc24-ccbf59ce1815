module.exports = {
  // pipeline 上下文, 所有插件的配置注册在这里
  // pipeline context object, all plugins' configuration should be registered here
  context: {},
  scene: {
    gitlab: {},
    scm: {
      build: {
        script: ['REGION=cn npm run build'],
        afterScript: [
          'find ./output -name "*.js.LICENSE.txt" | xargs rm -rf',
          'find ./output -name "*.js.map" | xargs rm -rf',
          'find ./output -name "*.css.map" | xargs rm -rf',
          'mkdir -p output/image',
          'mkdir -p output_resource',
          'mv output/image output_resource/',
        ],
      },
    },
  },
};
