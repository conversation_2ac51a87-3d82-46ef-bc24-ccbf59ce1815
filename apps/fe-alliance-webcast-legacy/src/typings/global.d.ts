declare interface Window {
  reactId: string
  ToutiaoJ<PERSON>ridge: {
    call(eventName: string, params?: any, callback?: (data: any) => void, others?: any)
    on(eventName: string, callback?: () => void)
  }
  Sentry: any
  MeScroll: any
}

declare interface Document {
  webkitVisibilityState: string
}

declare module '*.scss' {
  const content: {[className: string]: string};
  export default content;
}

declare let __ONLINE__: boolean;

declare module '*.png'
declare module '*.gif'