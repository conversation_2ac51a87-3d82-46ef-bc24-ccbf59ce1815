export enum SchemaBoolean {
  YES = 1,
  NO = 0,
}

export enum SchemaProtocol {
  XIGUA = 'sslocal:',
  HUOSHAN = 'sslocal:',
  TOUTIAO = 'sslocal:',
  AWEME = 'aweme:',
}

export enum SwipeMode {
  DISABLED = 0,
  EDGE = 2,
}

export interface SchemaConfig {
  url: string;
  title?: string;
  // 隐藏导航栏
  hide_bar?: SchemaBoolean;
  // 隐藏右上角的关闭按钮
  hide_close_btn?: SchemaBoolean;
  // 隐藏返回按钮
  hide_back_button?: SchemaBoolean;
  hide_back_close?: SchemaBoolean;
  // 是否隐藏状态栏
  hide_status_bar?: SchemaBoolean;
  // 边缘关闭功能
  swipe_mode?: SwipeMode;
  // 状态栏颜色
  status_bar_color?: string;
  // 状态栏文字颜色
  status_font_dark?: SchemaBoolean;
  // 导航栏颜色
  nav_bar_color?: string;
  // 导航栏文字颜色
  title_color?: string;
  // 页面加载默认背景色
  loading_bgcolor?: string;
  // 是否直播容器
  isWebCast?: boolean;
  // 任意额外参数
  [extraParam: string]: unknown;
}
