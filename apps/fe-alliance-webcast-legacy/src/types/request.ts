export enum ApiStatusCode {
    OK = 0,
    ERROR = -1
}

export interface ApiResponse {
    status_msg?: string;
    st?: number;
    status_code?: ApiStatusCode;
    code?: number;
    msg?: string;
    [payload: string]: unknown;
}

export interface UGCApiResponse {
    err_no: number;
    err_msg: string;
    [payload: string]: unknown;
}

export interface LianMengResponse {
    code: number;
    msg: string;
    [payload: string]: unknown;
}
