import { ApiResponse } from './request';

export interface Coupon {
  tag: string;
  coupon_url: string;
  coupon_applet: string;
}

export interface Activity {
  activity_icon: string;
  width: string;
}

export interface Discount {
  campaign_type: string;
  title: number;
}

interface Icon {
  height: number;
  uri: string;
  url_list: string[];
  width: number;
}

interface PrivilegeInfoDetail {
  tag_id: string; // 权益id标识
  title: string; // 权益标题(eg:正品保障)
  content: string; // 详细权益信息(100%正品保障，假一赔十，放心选购)
  icon: Icon; // 详细信息中的权益icon
  small_icon: Icon; // 权益信息小icon
  show_small_icon: boolean; // 是否展示small_icon
  link?: string; // 跳转链接
}

export interface PrivilegeInfo {
  // 权益icon(只有安心购才会有icon)
  icon: Icon;
  platform_public_url: string; // 平台公示
  privilege_id: string; // 权益id(1:安心购，2:其他权益)
  privilege_info_detail: PrivilegeInfoDetail[]; // 权益详情
}

export interface Promotion {
  promotion_id: string;
  product_id: string;
  title: string;
  cover: string;
  price: number;
  activity: Activity;
  full_discount: Discount[];
  min_price: number;
  market_price: number;
  cos_fee: number;
  detail_url: string;
  sales: number;
  item_type: number;
  cos_ratio: number;
  favor: boolean;
  in_shop: boolean;
  short_title: string;
  elastic_title: string;
  platform_label: string;
  coupon_amount: number;
  coupons: Coupon[];
  in_stock: boolean;
  status: number;
  flash_icon: string;
  stock_num: number;
  sale_num: number;
  can_seckill: boolean;
  extra: {
    allow_selling_point: string;
    start_time: string;
    star_label?: string;
    [key: string]: unknown;
  };
  discount_label: DiscountLabel[];
  block_add_reason?: string; // 阻塞商品添加成功的原因
  live_add_enum?: string; // 添加商品提示文案，默认空字符串可添加，非空字符串toast提示并拦截添加
  privilege_info?: PrivilegeInfo; // 安心购等权益详情
  live_room_type?: number; // 直播间类型，0:普通，1:安心购
}

export enum TagType {
  coupon = 1,
  fullDiscountCampaign = 2,
}

export enum ShowTyep {
  allShop = 0, // 0:全网店铺推广
  selfChannel = 1, // 1:自有渠道推广
  newbie = 3, //  3:新客专享
  allianceAuthor = 4, // 4:联盟达人定向券
  singleAd = 5, // 5:单品广告推广券
  commentReback = 6, //  6:评价返券
}

export enum UserCouponType {
  total = 0,
  fans = 1,
  platformAwemeHoston = 2,
}

export interface DiscountLabel {
  tag: string;
  url: string;
  id: string;
  tag_header: string;
  type: TagType; // 标签类型，1 优惠券，2满减活动
  is_show: ShowTyep; //  #展示渠道
  kol_user_tag: UserCouponType; // 用户人群标签  0:全部   1:粉丝  2:平台-抖火电商新用户
}

export interface CampaignInfo {
  campaign_id: string;
  end_time: number;
  start_time: number;
  is_preheat: boolean;
  label: string;
  price: number;
  promotion_id: string;
  time_end_label: string;
  time_start_label: string;
  user_limit: number;
  pic: string;
  sub_type: number;
  campaign_type: number;
}

export interface GroupByInfo {
  group_label: string;
  joined: number;
  group_size: number;
  end_time: number;
  min_price: number;
}

export interface PreSaleData {
  origin_price: number;
  deposit_price: number;
  shop_svalue: number;
}

export interface Product extends Promotion {
  checked: boolean;
  fetching: boolean;
  campaign_info?: CampaignInfo;
  group_by_info?: GroupByInfo;
  pre_sale_data?: PreSaleData;
  rit_tag?: Record<string, any>;
  is_lottery: boolean;
  exclusive_info: string; // xx专享,空表示无专属信息
  formalIdx?: number; // 前端加上的字段，表示原有的位置
  featured?: {
    img: {
      url: string;
      width: number;
    };
    info: string;
  };
}

export interface FetchProductListParams {
  offset: number;
  count?: number;
}

// 商品列表
export interface ListResponse extends ApiResponse {
  has_more: boolean;
  offset: number;
  promotions: Promotion[];
  total: number;
}

// 更新商品信息
export type UpdateResponse = ListResponse;
