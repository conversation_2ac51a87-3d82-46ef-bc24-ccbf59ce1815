/*
 * @Author: xuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-06-20 19:17:39
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-07-12 17:55:56
 * @Description: file content
 */

// 来源
export enum MerchPickingEnterFromType {
  // 直播前的直播商品页面
  WebcastBeforeLiving = 'webcast_before_living',
  // 直播中的直播商品页面
  WebcastLiving = 'webcast_living',
  // 直播中控场景
  Livecontrol = 'live_control_operation',
}

// 页面形式
export enum PageMode {
  FullScreen = 'fullscreen',
  Popup99 = 'popup99',
  Popup73 = 'popup73',
}
