import { ApiResponse } from './request';

export interface Authority {
    // 是否开通了小店
    isXiaoDianAuthed: boolean;
    // 是否绑定了淘宝PID
    isTaobaoPidAuthed: boolean;
    // 是否授权开通了财经账号
    isAllianceAccountAuthed: boolean;
}

export interface AuthorityState {
    authority: Authority;
    isFetchingAuthority: boolean;
    isFetchAuthorityFailed: boolean;
}

// author/v1/author/authority接口原始返回
export interface AuthorityResponse extends ApiResponse {
    authority: {
        // 淘宝pid
        subpid: string;
        // 是否开通了小店
        has_xiaodian: boolean;
        // 是否授权开通了财经账号
        has_alliance_account: boolean;
    };
}
