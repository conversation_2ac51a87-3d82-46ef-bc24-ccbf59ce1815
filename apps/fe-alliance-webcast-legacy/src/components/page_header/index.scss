@import "~common/styles/merch_picking/mixins";

.page-header {
  position: relative;
  padding: 13px 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
  flex-shrink: 0;
  background-color: white;

  &__left {
    left: 12px;
    font-size: 0;
    @include absolute-center(y);
  }

  &__right {
    right: 16px;
    @include absolute-center(y);
  }

  &__back-icon {
    width: 24px;
    height: 24px;
    display: inline-block;
    @include background(
      $size: 9px auto,
            $position: center,
            $image: url("data:image/svg+xml,%3Csvg width='20' height='36' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cpath d='M-36-26h320v88H-36z'/%3E%3Cpath fill='none' d='M-4-6h48v48H-4z'/%3E%3Cpath d='M4.783 18l14.92 14.877a1 1 0 0 1 .003 1.415l-1.412 1.416a1 1 0 0 1-1.414.002L.68 19.558a2.202 2.202 0 0 1 0-3.116L16.88.29a1 1 0 0 1 1.414.002l1.412 1.416a1 1 0 0 1-.002 1.415L4.784 18z' fill='%23161823' fill-rule='nonzero'/%3E%3C/g%3E%3C/svg%3E")
        );
  }

  &__title {

    &-text {
      display: inline-block;
      font-size: 17px;
      line-height: 17px;
      font-weight: bold;
      vertical-align: middle;
    }
  }
}

.douyin .page-header {

  &__left {
    left: 10px;
  }

  &__right {
    right: 16px;
  }
}
