import React, { <PERSON><PERSON><PERSON>Hand<PERSON> } from 'react';

import './index.scss';

export interface Props {
  title?: string;
  titleIcon?: React.ReactNode;
  backIcon?: React.ReactNode;
  onNavBack: MouseEventHandler;
  children?: React.ReactNode;
  titleComponent?: React.ReactNode;
  className?: string;
}

export default React.memo((props: Props) => {
  const { title, backIcon, onNavBack, children, titleIcon, titleComponent, className = '' } = props;
  return (
    <div className={`page-header ${className}`}>
      <span className="page-header__left" onClick={onNavBack}>
        {backIcon || <span className="page-header__back-icon" />}
      </span>
      <div className="page-header__title">
        {titleIcon}
        {!titleComponent ? <span className="page-header__title-text">{title}</span> : titleComponent}
      </div>
      {children ? <span className="page-header__right">{children}</span> : null}
    </div>
  );
});
