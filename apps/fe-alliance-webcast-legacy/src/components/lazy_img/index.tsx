import cx from "classnames";
import React, { useRef, useState, useEffect } from "react";
import { loadImg } from "@src/lib/util/merch_picking";
import { observe, unobserve } from "@src/lib/scroll";

import "./index.scss";

interface Props {
  src: string;
  mask?: string;
  failure?: string;
  className?: string;
  lazy?: boolean;
  children?: React.ReactNode;
  tips?: React.ReactNode;
}

enum LoadStatus {
  LOADING = 1,
  LOADED = 2,
  LOAD_FAILED = 3,
}

export default React.memo((props: Props) => {
  const { src, mask, failure, className, children, lazy = true, tips } = props;

  const [status, setStatus] = useState<LoadStatus>(LoadStatus.LOADING);

  const domRef = useRef<HTMLElement>(null);

  const style: { backgroundImage?: string } = {};

  if (status === LoadStatus.LOADED) {
    style.backgroundImage = `url(${src})`;
  }

  if (failure && status === LoadStatus.LOAD_FAILED) {
    style.backgroundImage = `url(${failure})`;
  }

  const maskStyle = mask ? { backgroundImage: `url(${mask})` } : undefined;

  useEffect(() => {
    const el = domRef.current as HTMLElement;
    const load = () => {
      loadImg(src).then(
        () => setStatus(LoadStatus.LOADED),
        () => setStatus(LoadStatus.LOAD_FAILED)
      );
    };
    if (!lazy) {
      load();
      return;
    }
    const callback = (e: ClientRect | null) => {
      if (e) {
        unobserve(el, callback);
        load();
      }
    };
    observe(el, callback, { margin: [-1000] });
    return () => unobserve(el, callback);
  }, []);

  return (
    <span className={cx("lazy-img", className)} ref={domRef}>
      <span className="lazy-img__img" style={style} data-lzl={status}>
        {mask ? <span className="lazy-img__mask" style={maskStyle} /> : null}
      </span>
      {children}
      {tips && <span className="lazy-img__tips">{tips}</span>}
    </span>
  );
});
