import React, { ReactNode } from "react";
import { createPortal, render, unmountComponentAtNode } from "react-dom";
import classNames from "classnames";
import "./style";
import { isAweme } from "../../lib/env_detect";
import loadingImag from "../../static/images/loading/loading.png";

let loadingRef: React.RefObject<HTMLDivElement>;

export interface LoadingProps {
  tip?: ReactNode;
  className?: string;
}

/**
 * 加载Loading
 * @param {function} show
 * @param {functon} hide
 */
const Loading = (props: LoadingProps) => {
  const { className, tip, ...otherProps } = props;
  loadingRef = React.createRef();

  return createPortal(
    <div className={classNames("webcast-loading", className)} ref={loadingRef} {...otherProps}>
      {isAweme ? (
        <img className="icon-loading" src="https://sf3-cdn-tos.douyinstatic.com/obj/ttfe/ies/ecomstudy/loading.gif" alt="加载中" />
      ) : (
        <div className="loading-no-aweme">
          <img className="icon-loading" src={loadingImag} alt="加载中" />
          <div className="tip">加载中</div>
        </div>
      )}
    </div>,
    document.body
  );
};

let loadingWrapNode: HTMLElement;

function show(): void {
  if (!loadingWrapNode) {
    loadingWrapNode = document.createElement("div");
    document.body.appendChild(loadingWrapNode);
  }
  render(<Loading />, loadingWrapNode);
}

function hide(): void {
  if (loadingWrapNode) {
    unmountComponentAtNode(loadingWrapNode);
    loadingWrapNode.remove();
  }
}

export default {
  show,
  hide,
};
