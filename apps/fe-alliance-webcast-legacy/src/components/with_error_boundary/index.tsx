// import via from '@ies/webcast-via'
import React from 'react';

import Layout from '../layout/index';
import { closeWebview, host } from '@src/common/bridge';
import { isAndroid } from '@src/lib/env_detect';

import './index.scss';

export const ComponentErrorCatch = (
  Component: React.ComponentType<any>,
  { componentName }: { componentName: string }
) => {
  return class extends React.PureComponent<any> {
    [x: string]: any;
    state = {
      hasError: false,
    };

    componentDidCatch(error, info) {
      this.setState({
        hasError: true,
      });
    }

    render() {
      if (this.state.hasError) {
        return null;
      }
      return <Component {...this.props} />;
    }
  };
};

export default function withErrorBoundary(WrapperComponent: React.ComponentType<any>, errorBoundaryConfig) {
  return class extends React.Component {
    showFeedback = false;
    state = {
      isError: false,
    };
    componentDidCatch(error) {
      this.showFeedback = !isAndroid && host === ('aweme' as any);
      this.setState({
        isError: true,
      });
    }

    handleFeedback = () => {
      this.onClose();
    };

    onClose = () => {
      closeWebview({ reactId: window.reactId });
    };

    get layoutProps() {
      return {
        title: errorBoundaryConfig.title,
        leftIconCallback: this.onClose,
      };
    }

    renderErrorTip() {
      const tips1 = '您可以';
      const clip = '截图';
      const tips2 = !this.showFeedback ? '后到设置页面反馈与帮助进行反馈' : '后点击下方按钮进行反馈';
      const tips3 = '清晰地描述有助于更快解决问题哦~';
      return (
        <div className="error-container">
          <div className="tip-text">抱歉,当前页面出现异常</div>
          <div className="desc-content">{`${tips1}${clip}${tips2}${tips3}`}</div>
          {this.showFeedback && (
            <button type="button" onClick={this.handleFeedback} className="feedback-btn">
              反馈与帮助
            </button>
          )}
        </div>
      );
    }

    render() {
      const { isError } = this.state;
      if (isError) {
        return <Layout {...this.layoutProps}>{this.renderErrorTip()}</Layout>;
      }
      return <WrapperComponent {...this.props} />;
    }
  };
}
