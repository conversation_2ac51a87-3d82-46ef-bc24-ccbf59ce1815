@import "@src/common/styles/mixin.scss";

.nav-bar {
  position: relative;
  display: flex;
  height: 44px;
  align-items: center;
  padding: 0 40px;

  &__left-icon {
    position: absolute;
    left: 16px;
  }

  &__left-text {
    position: absolute;
    left: 16px;
    font-size: 14px;
    line-height: 20px;
  }

  &__title {
    flex-grow: 1;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-size: 17px;
    font-weight: 600;
    color: #161823;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  &__right-icons-wrap {
    position: absolute;
    right: 16px;
    display: flex;
  }
}

.border-bottom {
  @include border-bottom-1($color: rgba(22, 24, 35, 0.12));
}
