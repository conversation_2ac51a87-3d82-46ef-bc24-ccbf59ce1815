import classname from 'classnames';
import React, { PureComponent, ReactNode, ReactElement } from 'react';
import { closeWebview } from '@src/common/bridge';
import IconArrowLeftBlack from '@src/components/Icon/IconArrowLeftBlack';
import './style';

export interface NavBarProps {
  hideBorder?: boolean;
  title: string | ReactNode;
  leftIconSize?: number;
  leftText?: string;
  style?: object;
  leftIconCallback?: VoidFunction;
  rightIcons?: ReactNode;
  rightIconCallback?: React.MouseEventHandler<HTMLDivElement>;
}

class Navbar extends PureComponent<NavBarProps> {
  static defaultProps = {
    title: '',
    hideBorder: false,
    leftIconSize: 24,
    leftIconCallback: (): void =>
      closeWebview({
        reactId: window.reactId,
      }),
    rightIconCallback: (): void => {},
  };

  render(): ReactNode {
    const {
      title,
      leftIconCallback,
      leftIconSize,
      hideBorder,
      rightIcons,
      rightIconCallback,
      leftText,
      style = {},
    } = this.props;
    return (
      <div className={classname('nav-bar', { 'border-bottom': !hideBorder })} style={style}>
        {leftText ? (
          <div className="nav-bar__left-text" onClick={leftIconCallback}>{leftText}</div>
        ) : (
          <IconArrowLeftBlack size={leftIconSize} className="nav-bar__left-icon" onClick={leftIconCallback} />
        )}
        <span className="nav-bar__title">{title}</span>
        {rightIcons && (
          <div className="nav-bar__right-icons-wrap" onClick={rightIconCallback}>
            {rightIcons}
          </div>
        )}
      </div>
    );
  }
}

export default Navbar;
