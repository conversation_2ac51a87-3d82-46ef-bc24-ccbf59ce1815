.radio-wrapper {
  @mixin checkedCon() {

    &:before {
      display: inline-block;
      width: 16px;
      height: 16px;
      background: url(../../static/image/flash_purchase_setting/selected.png) no-repeat 0 0;
      background-size: 16px 16px;
      content: " ";
    }
  }
  $duration: .4s;

  .checkbox {
    -webkit-appearance: none;
    position: relative;
    width: 16px;
    height: 16px;
    background-color: #fff;
    border: solid 1px #A6A6A6;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px;
    background-clip: padding-box;
    display: inline-block;

    &:focus {
      outline: 0 none;
      outline-offset: 0px;
    }

    &:checked {
      background-color: #18b4ed;
      border: solid 1px #fff;
      @include checkedCon();
    }

    &:disabled {
      background-color: #F2F2F2;
      border: solid 1px #D9D9D9;
      @include checkedCon();
    }

    &.checkbox-red:checked {
      background-color: transparent;
    }

  }
   
  .checkbox-anim {
    //border等其他元素不做过渡效果，增加视觉差，更有动画效果
    transition: background-color ease $duration/2;
   
  }
  
}
