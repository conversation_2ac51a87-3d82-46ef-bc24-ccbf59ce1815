.radio-wrapper {
  @mixin checkedCon($fs:18px) {

    &:before {
      display: inline-block;
      width: 20px;
      height: 20px;
      background: url(../../static/image/flash_purchase_setting/selected.png) no-repeat 0px 0px;
      background-size: 20px 20px;
      content: "";
    }
  }
  $duration: .4s;

  .checkbox {
    -webkit-appearance: none;
    position: relative;
    width: 20px;
    height: 20px;
    background-color: #fff;
    border: solid 1px rgba(22, 24, 35, 0.12);
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
    background-clip: padding-box;
    display: inline-block;

    &:focus {
      outline: 0 none;
      outline-offset: -2px;
    }

    &:checked {
      background-color: #18b4ed;
      border: solid 1px #fff;
      @include checkedCon();
    }

    &:disabled {
      background-color: rgba(22, 24, 35, 0.06);
      border: solid 1px rgba(22, 24, 35, 0.12);
      @include checkedCon();
    }

    &.checkbox-red:checked {
      background-color: rgba(254,44,85,1);
    }
  }
   
  .checkbox-anim {
    //border等其他元素不做过渡效果，增加视觉差，更有动画效果
    transition: background-color ease $duration/2;
   
  }
  
}
