import React, { PureComponent, ReactNode } from 'react';
import './style';

interface Props {
  isChecked: boolean;
  onChange?: VoidFunction;
}

class Radio extends PureComponent<Props> {
  static defaultProps = {
    isChecked: false,
    onChange: () => null,
  };

  render(): ReactNode {
    const { onChange, isChecked } = this.props;
    return (
      <div className="radio-wrapper">
        <input className="checkbox checkbox-red" type="checkbox" checked={isChecked} onChange={onChange} />
      </div>
    );
  }
}

export default Radio;
