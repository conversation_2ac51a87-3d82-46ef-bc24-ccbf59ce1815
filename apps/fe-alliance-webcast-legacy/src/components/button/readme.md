#### Button

参数 | 说明 | 类型 | 默认值 | 可选值 |
:----: | :----: | :----: | :----: |  :----: |
width | 宽度 | string |  必填 |   - |
height | 宽度 | string |  必填 |   - |
solid | 背景是否是实心 | Boolean | true | - |
radius | 圆弧的尺寸 | string | true | none, sm, lg |
color  | 文字颜色 | string | #ff4466 | - |
bgColor | 背景颜色 | string | #ff4466 | - |
borderColor  | 边框颜色 | string | #ff4466 | - |
disabled |  禁止点击 | Boolean | false |  - |
threshold |  防抖的时间间隔 | 400 |  - |

##### Tips:
 -  solid为true， color固定为白色， 背景色默认#ff4466;  disabled样式固定

##### Demo

1.宽高设置，默认样式
```
 <Button width="146px" height="56px">开播提醒</Button>
```
2.宽高设置，背景非实心
```
 <Button width="146px" height="56px" solid={false}>立即订阅</Button>
```
3.宽高设置，背景非实心，圆角边设置
```
 <Button width="84px" height="40px" solid={false} radius="sm">+订阅</Button>
```
4.宽高设置,背景非实心，字体颜色设置，边框颜色设置
```
 <Button width="120px" height="56px" solid={false} color="#666" borderColor="#666">已关注</Button>

```
5.宽高设置,字体颜色设置，背景颜色设置
```
 <Button width="146px" height="56px" color="#fff"  bgColor="#d2d2d2">已提醒</Button>`
```
6.宽高设置，禁止点击
```
 <Button width="146px" height="56px" disabled>不可领取</Button>
