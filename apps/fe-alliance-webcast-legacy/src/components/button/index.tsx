import React, { CSSProperties, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react";
import "./style";
import classNames from "classnames";
import debounce from "lodash/debounce";

export interface ButtonProps {
  disabled?: boolean;
  onClick?: MouseEventHandler<HTMLButtonElement>;
  className?: string;
  threshold?: number;
  style?: CSSProperties;
  children?: any;
}

const Button = (props: ButtonProps) => {
  const { disabled = false, children, className, onClick, threshold = 400, style, ...otherProps } = props;

  return (
    <button
      className={classNames("webcast-button", className)}
      disabled={disabled}
      onClick={
        onClick &&
        debounce(onClick, threshold, {
          leading: true,
          trailing: false,
        })
      }
      {...otherProps}
    >
      {children}
    </button>
  );
};

export default React.memo(Button);
