import React from 'react';
import './index.scss';

interface Props {
    amount: number;
}

export default React.memo((props: Props) => {
    const { amount } = props;
    if (!(amount > 0)) {
        return null;
    }
    return (
        <span className="coupon">
            <span className="coupon__label">券</span>
            <span className="coupon__value">立减{amount / 100}</span>
        </span>
    );
});
