### Empty

##### 注意

- 最小宽度为180px,高度280px 高度需要合适

参数 | 说明 | 类型 | 默认值 | 可选值 |
:----: | :----: | :----: | :----: |  :----: |
width | 宽度 | String | - |  - |
height | 高度 | String | - |  - |
src | 占位图片地址 | String | - |  - |
type | 可选内置类型 | String | noData |  noData, networkError， noSearch |
tipText | 提示文案 | String | - |  - |
tipColor | 提示文案颜色 | String | #333 |  - |
hasBtn | 是否显示按钮 | Boolean | - |  - |
btnText | 按钮文案 | String | - |  - |
onClick | 点击按钮事件 | Function | - |  - |

#####Demo
1.默认样式
```
<Empty width="514px" height="260px" src="https://s10.mogucdn.com/p2/161213/upload_27e7gegi3f9acl5e05f3951if5855_514x260.png"/>

```
2.更改提示文案,更改内置类型
```
<Empty width="514px" height="260px" src="https://s10.mogucdn.com/p2/161213/upload_27e7gegi3f9acl5e05f3951if5855_514x260.png" tipText="想要的提示文案" />
```
3.显示按钮,更改按钮文案，按钮点击方法
```
<Empty width="514px" height="350px" src="https://s10.mogucdn.com/p2/161213/upload_27e7gegi3f9acl5e05f3951if5855_514x260.png" hasBtn={true}  btnText="想要的按钮文案" onClick={this.btnText.bind(this)}/>
```






