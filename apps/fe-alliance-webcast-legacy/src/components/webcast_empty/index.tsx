import React, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react";
import "./style";
import classNames from "classnames";
import emptyImage from "./images/index";

export interface emptyProps {
  width?: string;
  height?: string;
  tipText?: string;
  actionText?: string;
  onClick?: MouseEventHandler<HTMLElement>;
  imgUrl?: string;
  className?: string;
}

/**
 * 数据为空兜底页
 * @param {string} tipText - 网络超时
 * @param {functon} onClick
 * @param {string} actionText
 * @param {string} imgUrl
 * @param {string} width
 * @param {string} height
 */
const Empty = (props: emptyProps) => {
  const { width, height, tipText = "网络超时", onClick, actionText = "", imgUrl = emptyImage, className, ...otherProps } = props;

  return (
    <div className={classNames("webcast-empty", className)} style={getStyle({ width, height })} {...otherProps}>
      {imgUrl && <img src={imgUrl} className="empty-img" />}
      <div className="empty-tip">{tipText}</div>
      {actionText && (
        <div className="empty-action-btn" onClick={onClick}>
          {actionText}
        </div>
      )}
    </div>
  );
};

function getStyle({ width = "100%", height = "100%" } = {}) {
  let _width = width;
  let _height = height;
  return {
    width: _width,
    height: _height,
  };
}

export default React.memo(Empty);
