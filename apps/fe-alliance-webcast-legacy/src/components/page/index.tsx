import React, { ReactNode } from "react";
import withErrorBoundary from "../with_error_boundary";
import Layout from "../layout";
import "./style";

interface Props {
  title: string;
  hideBorder?: boolean;
  navbarStyle?: object;
  rightIcons?: ReactNode;
  leftIconCallback?: VoidFunction;
}

class Main extends React.Component<Props, {}> {
  render() {
    const { title, hideBorder, navbarStyle, children, rightIcons, ...restProps } = this.props;
    return (
      <Layout title={title} hideBorder={hideBorder} navbarStyle={navbarStyle} rightIcons={rightIcons} {...restProps}>
        {children}
      </Layout>
    );
  }
}

class Page extends React.Component<Props, {}> {
  render() {
    const { title, children } = this.props;
    const ErrorBoundaryPage = withErrorBoundary(Main, { title });
    return <ErrorBoundaryPage {...this.props}>{children}</ErrorBoundaryPage>;
  }
}

export default Page;
