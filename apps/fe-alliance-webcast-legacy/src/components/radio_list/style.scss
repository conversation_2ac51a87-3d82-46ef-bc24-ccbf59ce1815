@import "@src/common/styles/mixin.scss";

.radio-list {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;

  p {
    margin: 0;
  }

  .radio-item {
    padding: 16px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    &__content {
      flex-grow: 1;
      margin-right: 20.75px;

      .main-text {
        font-size: 15px;
        font-family: PingFangSC-Medium,PingFang SC;
        font-weight: 500;
        color: rgba(22,24,35,1);
        line-height: 21px;
      }

      .sub-text {
        margin-top: 6px;
        font-size: 13px;
        font-family: PingFangSC-Regular,PingFang SC;
        font-weight: 400;
        color: rgba(22,24,35,.5);
        line-height: 18px;
      }
    }

    &__radio {
      width: 56px;
      text-align: right;
    }
  }

}

.border-bottom {
  @include border-bottom-1($color: rgba(22,24,35,.12));
}
