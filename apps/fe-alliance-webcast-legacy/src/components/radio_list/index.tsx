import React, { PureComponent, ReactNode } from 'react';
import Radio from '@src/components/radio';
import './style';

interface ListItemType {
  mainText: string;
  subText?: string;
  templateId: string;
}
interface Props {
  dataList: ListItemType[];
  curSelected: number;
  onClick: Function;
}

class RadioList extends PureComponent<Props> {
  static defaultProps = {
    dataList: [],
    curSelected: 0
  };

  renderRadioListItem(item, index) {
    const { curSelected, onClick } = this.props;
    const { mainText, subText } = item;
    return (
      <div
        key={index}
        className="radio-item border-bottom"
        onClick={() => {
          onClick(index);
        }}>
        <div className="radio-item__content">
          <p className="main-text">{mainText}</p>
          <p className="sub-text">{subText}</p>
        </div>
        <div
          className="radio-item__radio">
          <Radio
            isChecked={index === curSelected}
          />
        </div>
      </div>
    );
  }

  render(): ReactNode {
    const { dataList } = this.props;
    return (
      <div className="radio-list">
        {dataList.map((item, index) => this.renderRadioListItem(item, index))}
      </div>
    );
  }
}

export default RadioList;
