.switch-wrapper {
  @mixin borderRadius($radius:20px) {
    border-radius: $radius;
    border-top-left-radius: $radius;
    border-top-right-radius: $radius;
    border-bottom-left-radius: $radius;
    border-bottom-right-radius: $radius;
  }
   
  $duration: .4s;
  $checkedColor: #67DCA0;
  height: 25px;
  .switch {
    width: 43px;
    height: 25px;
    position: relative;
    border: 1px solid #dfdfdf;
    background-color: rgba(22, 24, 35, 0.12);
    box-shadow: #dfdfdf 0 0 0 0 inset;
    @include borderRadius();
    background-clip: content-box;
    display: inline-block;
    -webkit-appearance: none;
    user-select: none;
    outline: none;
    border: none;
    box-sizing: content-box;
  
    &:before {
      content: "";
      width: 21px;
      height: 21px;
      position: absolute;
      top: 2px;
      left: 2px;
      @include borderRadius();
      background-color: #fff;
      // box-shadow: 0 1px 3px rgba(0, 0, 0, .4);
    }
  
    &:checked {
      border-color: $checkedColor;
      box-shadow: $checkedColor 0 0 0 16px inset;
      background-color: $checkedColor;
  
      &:before {
        left: 20px;
      }
    }
  
    &.switch-animbg {
      transition: background-color ease $duration;
  
      &:before {
        transition: left .3s;
      }
  
      &:checked {
        box-shadow: #dfdfdf 0 0 0 0 inset;
        background-color: $checkedColor;
        transition: border-color $duration, background-color ease $duration;
  
        &:before {
          transition: left .3s;
        }
      }
   
    }
  
    &.switch-anim {
      transition: border cubic-bezier(0, 0, 0, 1) $duration, box-shadow cubic-bezier(0, 0, 0, 1) $duration;
  
      &:before {
        transition: left .3s;
      }
  
      &:checked {
        box-shadow: $checkedColor 0 0 0 16px inset;
        background-color: $checkedColor;
        transition: border ease $duration, box-shadow ease $duration, background-color ease $duration*3;
  
        &:before {
          transition: left .3s;
        }
      }
   
    }
  }
  
}
