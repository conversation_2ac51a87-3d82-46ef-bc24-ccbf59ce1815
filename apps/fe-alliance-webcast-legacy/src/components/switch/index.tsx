import React, { PureComponent, ReactNode } from 'react';
import './style';

interface Props {
  isChecked: boolean;
  onChange: VoidFunction;
}

class Switch extends PureComponent<Props> {
  static defaultProps = {
    isChecked: false,
  };

  render(): ReactNode {
    const { onChange, isChecked } = this.props;
    return (
      <div className="switch-wrapper">
        <input className="switch switch-anim" type="checkbox" checked={isChecked} onChange={onChange} />
      </div>
    );
  }
}

export default Switch;
