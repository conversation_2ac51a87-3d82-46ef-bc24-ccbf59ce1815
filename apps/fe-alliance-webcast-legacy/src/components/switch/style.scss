.switch-wrapper {
  @mixin borderRadius($radius:20px) {
    border-radius: $radius;
    border-top-left-radius: $radius;
    border-top-right-radius: $radius;
    border-bottom-left-radius: $radius;
    border-bottom-right-radius: $radius;
  }
   
  $duration: .4s;
  $checkedColor: #FF264A;
  height: 28px;
  
  .switch {
    width: 44px;
    height: 28px;
    position: relative;
    background-color: #D9D9D9;
    box-shadow: #D9D9D9 0 0 0 0 inset;
    @include borderRadius(16px);
    background-clip: content-box;
    display: inline-block;
    -webkit-appearance: none;
    user-select: none;
    outline: none;
    border: none;
  
    &:before {
      content: "";
      width: 24px;
      height: 24px;
      position: absolute;
      top: 2px;
      left: 2px;
      @include borderRadius(24px);
      background-color: #fff;
      box-shadow: 0 1px 3px rgba(0, 0, 0, .4);
      display: block;
    }
  
    &:checked {
      box-shadow: $checkedColor 0 0 0 16px inset;
      background-color: $checkedColor;
  
      &:before {
        left: 18px;
      }
    }
  
    &.switch-animbg {
      transition: background-color ease $duration;
  
      &:before {
        transition: left .3s;
      }
  
      &:checked {
        box-shadow: #dfdfdf 0 0 0 0 inset;
        background-color: $checkedColor;
        transition: border-color $duration, background-color ease $duration;
  
        &:before {
          transition: left .3s;
        }
      }
   
    }
  
    &.switch-anim {
      transition: border cubic-bezier(0, 0, 0, 1) $duration, box-shadow cubic-bezier(0, 0, 0, 1) $duration;
  
      &:before {
        transition: left .3s;
      }
  
      &:checked {
        box-shadow: $checkedColor 0 0 0 16px inset;
        background-color: $checkedColor;
        transition: border ease $duration, box-shadow ease $duration, background-color ease $duration*3;
  
        &:before {
          transition: left .3s;
        }
      }
   
    }
  }
  
}
