import React, { <PERSON>actN<PERSON>, MouseEventHandler } from "react";
import <PERSON><PERSON> from "react-modal";
import { isIPhoneX } from "@src/lib/env_detect";
import "./select.scss";

interface SelectModalOption<T> {
  value: T;
  text: string;
}

export interface SelectModalProps<T> extends ReactModal.Props {
  title?: ReactNode;
  subTitle?: ReactNode;
  onCancel?: MouseEventHandler;
  value: T;
  onChange: (opt: SelectModalOption<T>) => void;
  options: SelectModalOption<T>[];
}

export default function SelectModal<T>(props: SelectModalProps<T>) {
  const { title, onCancel, options, value, onChange, ...modalProps } = props;

  return (
    <Modal
      {...modalProps}
      onRequestClose={onCancel}
      className="select-modal"
      overlayClassName="select-modal__overlay"
    >
      <div className="select-modal-header">
        <div
          className="ic-modal-close select-modal-header__cancel"
          onClick={onCancel}
        ></div>
        <span className="select-modal-header__title">{title}</span>
      </div>
      <div
        className="select-modal-body"
        style={{ paddingBottom: isIPhoneX ? 34 : 0 }}
      >
        {options.map((it) => (
          <div className="select-modal-option" onClick={() => onChange?.(it)}>
            <span>{it.text}</span>
            {it.value === value && <i className="ic-modal-option-correct"></i>}
          </div>
        ))}
      </div>
    </Modal>
  );
}
