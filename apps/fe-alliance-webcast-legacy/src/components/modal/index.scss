@import '~common/styles/merch_picking/mixins';
@import '~common/styles/merch_picking/visual/hairline';
@import '~common/styles/merch_picking/modules/keyframe';

.ReactModal {
    &__Overlay {
        z-index: 100;
        @include mask();
        &--after-open {
            animation: fade-in .2s ease-in-out forwards;
        }
        &--before-close {
            animation: fade-out .2s ease-in-out forwards;
        }
    }
    &__Content {
        &:focus {
            outline: none !important;
        }
    }
}

.confirm {
    &-modal {
        @include absolute-center();
        width: 300px;
        border-radius: 10px;
        text-align: center;
        background-color: #ffffff;
        &__overlay {
            @include mask();
            z-index: 1000;
            background-color: rgba(0,0,0,0.5);
        }
    }
    &__img {
        width: 100%;
        height: 140px;
    }
    &__title {
        margin: 24px 0 14px;
        color: #000;
        font-size: 17px;
        font-weight: normal;
        padding: 0 22px;
    }
    &__body {
        padding: 0 22px;
        margin: 4px 0 22px;
        font-size: 15px;
        line-height: 1.54;
        color: rgba(0,0,0,0.54);
    }
    &__html_body {
        text-align: left;
        margin-top: -15%;
        margin-bottom: 0;
    }
    &__footer {
        display: flex;
        flex-direction: row;
        &::after {
            background-color: rgba(0,0,0,0.1);
        }
    }
    &__cancel, &__confirm {
        flex: 1;
        padding: 15px 0;
        font-size: 17px;
        font-weight: bold;
    }
    &__cancel {
        color: rgba(0,0,0,0.54);
    }
    &__confirm {
        color: #ff0000;
        &::after {
            background-color: rgba(0,0,0,0.1);
        }
    }
}

.toutiao .confirm-modal {
    .confirm__title {
        font-weight: 500;
    }
    .confirm__cancel, .confirm__confirm {
        color: #1a74ff;
    }
}

.douyin .confirm-modal {
    border-radius: 2px;
    width: 280px;
    .confirm {
        &__title {
            font-size: 17px;
            color: #161823;
            font-weight: bold;
        }
        &__img + &__title {
            margin: 24px 0 8px;
        }
        &__cancel, &__confirm {
            padding: 16px 0;
            font-size: 15px;
        }
        &__confirm {
            color: #161823;
        }
    }
}

.confirm-modal {
    &.in-aweme {
        border-radius: 2px;
        width: 280px;
        .confirm {
            &__title {
                font-size: 17px;
                color: #161823;
                font-weight: bold;
            }
            &__img + &__title {
                margin: 24px 0 8px;
            }
            &__cancel, &__confirm {
                padding: 16px 0;
                font-size: 15px;
            }
            &__confirm {
                color: #161823;
            }
        }
    }
    &.in-toutiao {
        border-radius: 8px;
        width: 272px;
        .confirm__title {
            font-weight: 400;
            margin-top: 26px;
            margin-bottom: 16px;
        }
        .confirm__body {
            font-size: 14px;
        }
        .confirm__confirm {
            color: #F04142;
        }
        .confirm__cancel {
            font-weight: 400;
            color: #505050;
        }
    }
    &.in-xigua{
        width: 280px;
        border-radius: 10px;
        .confirm__title {
            font-weight: 400;
            margin-top: 26px;
            margin-bottom: 16px;
        }
        .confirm__confirm {
            font-weight: 500;
            color: #F04142;
        }
        .confirm__body {
            font-size: 14px;
        }
        .confirm__cancel {
            font-weight: 500;
            color: #505050;
        }
    }
}
