import { isInAweme, isInToutiao, isInXigua } from '@src/lib/env_detect';
import cx from 'classnames';
import React, { ReactNode, MouseEventHandler } from 'react';
import Modal from './index';

interface Props extends ReactModal.Props {
    img?: string;
    title?: ReactNode;
    body?: ReactNode;
    htmlStr?: string;
    onCancel?: MouseEventHandler;
    onConfirm: MouseEventHandler;
    cancelText?: string;
    confirmText?: string;
    className?: string;
    keepBody?: boolean;
}

export default React.memo((props: Props) => {
    const {
        img,
        title: _title,
        body: _body,
        htmlStr,
        onConfirm,
        onCancel,
        cancelText = '取消',
        confirmText = '确定',
        className = '',
        keepBody,
        ...modalProps
    } = props;

    let [title, body] = [_title || _body, _title ? _body : null];
    if (keepBody) {
        [title, body] = [_title, _body];
    }

    const confirmHandler: MouseEventHandler = event => {
        onConfirm(event);
    };

    return (
        <Modal
            {...modalProps}
            className={cx('confirm-modal', className, {
                'in-aweme': isInAweme,
                'in-toutiao': isInToutiao,
                'in-xigua': isInXigua
            })}
            trackProps={{
              title,
              img,
              cancelText,
              confirmText,
            }}
            overlayClassName="confirm-modal__overlay">
            {!img ? null : <img src={img} className="confirm__img" />}
            <h3 className={cx('confirm__title', body && htmlStr ? null : 'confirm__title--lg')}>{title}</h3>
            {body ? <p className="confirm__body">{body}</p> : null}
            {htmlStr ? <p className="confirm__html_body" dangerouslySetInnerHTML={{__html: htmlStr}}></p> : null}
            <div className="confirm__footer hairline--t">
                {cancelText && onCancel ? (
                    <span className="confirm__cancel" onClick={onCancel}>
                        {cancelText}
                    </span>
                ) : null}
                <span className="confirm__confirm hairline--l" onClick={confirmHandler}>
                    {confirmText}
                </span>
            </div>
        </Modal>
    );
});
