import React, { <PERSON>actN<PERSON>, <PERSON><PERSON><PERSON><PERSON>andler } from 'react';
import Modal from 'react-modal';
import './confirm_bottom.scss';

interface Props extends ReactModal.Props {
    title?: ReactNode;
    body?: ReactNode;
    onCancel?: MouseEventHandler;
    onConfirm: MouseEventHandler;
    cancelText?: string;
    confirmText?: string;
    cancelPosition?: 'left' | 'right';
}

export default React.memo((props: Props) => {
    const {
        title: _title,
        body: _body,
        onConfirm,
        onCancel,
        cancelText = '取消',
        confirmText = '确认',
        cancelPosition = 'left',
        ...modalProps
    } = props;

    const [title, body] = [_title || _body, _title ? _body : null];

    const confirmHandler: MouseEventHandler = event => {
        onConfirm(event);
    };

    return (
        <Modal
            onRequestClose={onCancel}
            {...modalProps}
            className="confirm-bottom-modal"
            overlayClassName="confirm-bottom-modal__overlay"
        >
            <div className="confirm-bottom-modal-header">
                {cancelPosition === 'left' ? (
                    <span className="confirm-bottom-modal-header__cancel" onClick={onCancel}>
                        取消
                    </span>
                ) : null}
                <span className="confirm-bottom-modal-header__title">{title}</span>
                {cancelPosition === 'right' ? (
                    <img
                        src={require('static/images/merch_picking/close.png')}
                        className="confirm-bottom-modal-header__cancel--right"
                        onClick={onCancel}
                    />
                ) : null}
            </div>
            <div className="confirm-bottom-modal-body">{body}</div>
            <div className="confirm-bottom-modal-footer">
                <div className="confirm-bottom-modal-footer__confirm" onClick={confirmHandler}>
                    {confirmText}
                </div>
            </div>
        </Modal>
    );
});
