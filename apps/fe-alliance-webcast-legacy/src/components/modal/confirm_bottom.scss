@import '~common/styles/merch_picking/mixins';
@import '~common/styles/merch_picking/visual/hairline';
@import '~common/styles/merch_picking/modules/keyframe';

.ReactModalPortal {
    &__Overlay {
        z-index: 100;
        @include mask();
        &--after-open {
            animation: fade-in .2s ease-in-out forwards;
        }
        &--before-close {
            animation: fade-out .4s ease-in-out forwards;
        }
    }
    &__Content {
        &:focus {
            outline: none !important;
        }
    }
    .confirm-bottom-modal {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        border-radius: 0;
        text-align: center;
        background-color: #ffffff;
        border-radius: 8px 8px 0 0;
        &__overlay {
            @include mask();
            z-index: 1000;
            background-color: rgba(0,0,0,0.5);
        }
        &-header {
            height: 44px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            &__cancel {
                position: absolute;
                left: 16px;
                font-size: 14px;
                &--right {
                    position: absolute;
                    right: 16px;
                    width: 16px;
                    height: 16px;
                }
            }
            &__title {
                color: rgba(22, 24, 35, 1);
                font-size: 17px;
                font-weight: 500;
            }
        }
        &-footer {
            height: 76px;
            height: calc(76px + constant(safe-area-inset-bottom));
            height: calc(76px + env(safe-area-inset-bottom));
            padding: 16px;
            padding-bottom: calc(16px + constant(safe-area-inset-bottom));
            padding-bottom: calc(16px + env(safe-area-inset-bottom));
            box-sizing: border-box;
            &__confirm {
                background-color: rgba(254, 44, 85, 1);
                color: white;
                height: 44px;
                line-height: 44px;
                font-size: 15px;
                font-weight: 500;
                border-radius: 2px;
            }
        }
    }
}

.huoshan {
    .ReactModalPortal {
        .confirm-bottom-modal {
            border-radius: 12px 12px 0 0;
            &-footer {
                height: 68px;
                height: calc(68px + constant(safe-area-inset-bottom));
                height: calc(68px + env(safe-area-inset-bottom));
                padding: 12px;
                padding-bottom: calc(12px + constant(safe-area-inset-bottom));
                padding-bottom: calc(12px + env(safe-area-inset-bottom));
                &__confirm {
                    background-color: rgba(255, 78, 51, 1);
                    font-size: 16px;
                    border-radius: 16px;
                }
            }
        }
    }
}