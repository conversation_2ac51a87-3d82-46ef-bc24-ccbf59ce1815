@import "~common/styles/merch_picking/mixins";
@import "~common/styles/merch_picking/visual/hairline";
@import "~common/styles/merch_picking/modules/keyframe";

.ReactModalPortal {
  &__Overlay {
    z-index: 100;
    @include mask();
    &--after-open {
      animation: fade-in 0.2s ease-in-out forwards;
    }
    &--before-close {
      animation: fade-out 0.4s ease-in-out forwards;
    }
  }
  &__Content {
    &:focus {
      outline: none !important;
    }
  }
  .select-modal {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    border-radius: 0;
    text-align: center;
    background-color: #ffffff;
    border-radius: 8px 8px 0 0;
    &__overlay {
      @include mask();
      z-index: 1000;
      background-color: rgba(0, 0, 0, 0.5);
    }
    &-header {
      height: 56px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      &__cancel {
        position: absolute;
        right: 16px;
      }
      &__title {
        color: #161823;
        font-size: 17px;
        font-weight: 500;
      }
    }
    &-option {
      height: 48px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px 0 24px;
    }
  }
  .ic-modal-close {
    width: 24px;
    height: 24px;
    background-size: 100% 100%;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAYAAABV7bNHAAAACXBIWXMAACE4AAAhOAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAJJSURBVHgB7dkxbhNBFMbxN+usQQIpRsLWlguBPiXlpqPkBhzBcILYt4DOdNDBCRA3yAVAW1pQsIUjOVk5k3lOUiRZb+zd92a2+H6N5Z3C1t+2dt6YCAAAAAAAAAAAAADAD0MKBoN00H8cjS2ZzD2d/Zv//kIKnievssjasTVUlLSaFvM8J2F7pCB+FB279h+u62ej5GX6d/5nSoKGycF7Q3bGHzG/Tp96mXt4QcIiUhFlt5+biYt0TEKu4tDszuU0SdKUhKkEMubiR8VVkUgb4rB8rvAT65GCeG//JOqZt8aY5PaKyZ48fUani/+/qIG6OBGtjhaLoiBhKoGWy2LZj/e/SUZ6KI7Gt4epBGKSkULFYWqBmESkkHGYaiDWJlLoOEw9EGsSqQtxmJdAbJdIXYmzfmfkGY8hbqf900U6vL9qJ248ybsSh3kPxOojVQoShwUJxHaIFCwOCxaIbREpaBymNKxupyhyHg1mm9attd9DxmHe7mJV1ncrYz5tWndrb9rMbhKCBaq5ld/RbsBtK0igun2O+1nl0qcAbXgP9NAm8PzMftY4KmnKa6BtdsgaRyVteAu0y/jQpUheAjWZrboSST1Qm8GzC5FUA0lM5aEjqQWSPLIIGUkl0Gh08M4Y+lqx1Hi2ChVJZRazEY0rLrcePHl2K88ujtxm8uT+qpnw8EvCfA2rYlN5fSR5OoFW0Udr6eZPPPEji+pIZnp9OiBK7TxofdYTx2lZlrnGG78xHL4+LHtlUQQ+FgEAAAAAAAAAAAAAAGWX5cyYolPxs2IAAAAASUVORK5CYII=);
  }
  .ic-modal-option-correct {
    width: 24px;
    height: 24px;
    background-size: 100% 100%;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAYAAABV7bNHAAAACXBIWXMAACE4AAAhOAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAIASURBVHgB7du9TsMwGIVhGzExcEEssDMwMcHQld4ACyNLuQBYi2BjYoCZOws5QfxIxT1OYief3fNKlapWadNHrdNYjm/anAq259TWBEQSEElAJAGRBEQSEElAJAGRBEQSEElAJAGRBETaGaDm4cU1R4uv2+oxert9twM1N/fOvX78PvD85prDA+evzum21X+DNnC+e3p3MVUNFMTpUbVADMefHbuYqgTiOCfOXS9cTNUBReHcLl1sVQGlxkHVAOXAQVUA5cJBxQPlxEFFA+XGQcUCTYGDigSaCgcVBzQlDioKaGocVAzQHDioCKC5cFA00NAZubHNidO9fswKs+ZuvTnB1O6Yz7hj3fvOjIPivkH/7WT7WPcBMmUBB40bgzIhWcFBcUAXp+HnEiNZwkFRQH553o05wRIhWcNB0T+xbkDOiGQRB/Uag3IhWcVBvQfp1EiWcdCgo1gqJOs4aPBhfixSCTho1P+goUil4KDRJ6t9kUrCQUlWdwCpO6ELfXAg/bkffB1jOMinvBxqzGIBizgo6XwQ/bmFtjOKg5JPmPVFsoyDsswoxiJZx0HZplwZUgk4KOucdAipFBzkp7iot1mtf45u/rKdW1ryxZNW8rrqeXtaSE4SEElAJAGRBEQSEElAJAGRBEQSEElAJAGRBEQSEElAJAGRrF4WbmYS7xM16BTkfzkKwAAAAABJRU5ErkJggg==);
  }
}

.huoshan {
  .ReactModalPortal {
    .select-modal {
      border-radius: 12px 12px 0 0;
      &-footer {
        height: 68px;
        height: calc(68px + constant(safe-area-inset-bottom));
        height: calc(68px + env(safe-area-inset-bottom));
        padding: 12px;
        padding-bottom: calc(12px + constant(safe-area-inset-bottom));
        padding-bottom: calc(12px + env(safe-area-inset-bottom));
        &__confirm {
          background-color: rgba(255, 78, 51, 1);
          font-size: 16px;
          border-radius: 16px;
        }
      }
    }
  }
}
