import React, { ReactNode } from 'react';
import Modal from 'react-modal';
import './index.scss';
import { MODAL_CLASSNAME, useTrackDialogShow } from '@alliance-mobile/event';
interface Props extends ReactModal.Props {
  children: ReactNode;
  trackProps?: {
    title?: ReactNode | string;
    img?: string;
    cancelText?: string;
    confirmText?: string;
  };
}

export default React.memo((props: Props) => {
  const { closeTimeoutMS = 400, ariaHideApp = false } = props;

  useTrackDialogShow(props?.isOpen, props?.trackProps);

  return (
    <Modal
      closeTimeoutMS={closeTimeoutMS}
      ariaHideApp={ariaHideApp}
      {...props}
      className={`${props?.className || ''} ${MODAL_CLASSNAME}`}
    />
  );
});
