import * as React from 'react'


interface BaseIconProps extends React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> {
  src: string
  size?: number | { width: number; height: number }
  opacity?: number
}

export class BaseIcon extends React.Component<BaseIconProps> {
  static defaultProps = {
    size: 16,
  }
  render() {
    const { src, opacity, size, onClick, ...rest } = this.props
    let width = 16
    let height = 16
    if (typeof size === 'number') {
      width = size
      height = size
    } else if (typeof size === 'object') {
      width = size.width
      height = size.height
    }
    const imageStyle = {
      display: 'block',
      width,
      height,
      opacity: opacity,
    }
 
    return (
      <div {...rest} onClick={onClick}>
        <img src={src} style={imageStyle} alt=''/>
      </div>
    )
  }
}

export type IconProps = Omit<BaseIconProps, 'src'>
