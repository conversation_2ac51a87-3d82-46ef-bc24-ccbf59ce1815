@import "./style.scss";

.webcast-modal {
  width: 280px;
  border-radius: 31px;
  .contain {
    .content-text {
      color: rgba(0, 0, 0, 0.75);
      font-size: 14px;
    }
  }
  .footer {
    justify-content: center;
    align-items: center;
    border-radius: 0 0 31px 31px;
    padding-bottom: 16px;
    .btn {
      max-width: 123px;
      height: 40px;
      background-color: #f8f8f8;
      font-size: 16px;
      color: #404040;
    }
    .btn-cancel {
      border-top-left-radius: 16px;
      border-bottom-left-radius: 16px;
      border-right: 2px solid #fff;
    }
    .btn-confirm {
      border-top-right-radius: 16px;
      border-bottom-right-radius: 16px;
      font-weight: 600;
    }
    .btn-confirm-single {
      max-width: 247px;
      height: 40px;
      background: #ff4e33;
      border-radius: 16px;
      font-weight: 500;
      font-size: 15px;
      color: #fff;
    }
  }
}
