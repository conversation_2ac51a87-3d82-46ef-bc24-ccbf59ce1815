import React, { Component } from "react";
import { createPortal, render } from "react-dom";
import "./style";
import classNames from "classnames";
import Confirm from "./confirm";
import { showModal } from "../../common/bridge";
import { isInApp } from "../../lib/env_detect";

export interface ModalProps {
  className?: string;
  title?: string;
  cancelText?: string;
  cancelColor?: string;
  confirmText?: string;
  confirmColor?: string;
  content?: string;
  onCancel?: () => void;
  onConfirm: () => void;
  contentHeight?: string;
  visible: boolean;
  children?: any;
}

interface Options {
  content: string;
  title?: string;
  cancelText?: string;
  confirmText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  useNative?: boolean;
}

/**
 * 确认弹窗
 * @param {string} title
 * @param {number} content
 * @param {function} onConfirm
 * @param {function} onCancel
 * @param {string} confirmText
 * @param {string} cancelText
 * @param {string} confirmColor
 * @param {string} cancelColor
 * @param {string} contentHeight
 */
export default class Modal extends Component<ModalProps, any> {
  static modalWrapNode: null | HTMLDivElement = null;

  static options(options: Options = { content: "" }): void {
    const { content, title, cancelText, confirmText, onConfirm, onCancel, useNative = false } = options;
    if (isInApp && useNative) {
      showModal({ content, title, confirmText, cancelText, showCancel: Boolean(cancelText) }).then(({ confirm, cancel }) => {
        if (confirm) {
          onConfirm && onConfirm();
        }
        if (cancel) {
          onCancel && onCancel();
        }
      });
    } else {
      if (!this.modalWrapNode) {
        this.modalWrapNode = document.createElement("div");
        document.body.appendChild(this.modalWrapNode);
      }
      render(<Confirm {...options} key={Date.now()} />, this.modalWrapNode);
    }
  }

  render() {
    const { className, title, cancelText = "取消", cancelColor, confirmText = "确认", confirmColor, content, onCancel, onConfirm, contentHeight, children, visible, ...otherProps } = this.props;

    return createPortal(
      visible && (
        <div className="webcast-modal-mask">
          <div className={classNames("webcast-modal", className)} {...otherProps}>
            <div className="contain">
              {title && <div className="title">{title}</div>}
              <div className="content" style={{ height: contentHeight }}>
                {content && <div className="content-text">{content}</div>}
                {children}
              </div>
            </div>
            <div className="footer">
              {cancelText && (
                <div className="btn btn-cancel" style={{ color: cancelColor }} onClick={onCancel}>
                  {cancelText}
                </div>
              )}
              <div className={classNames("btn", "btn-confirm", { "btn-confirm-single": !cancelText })} style={{ color: confirmColor }} onClick={onConfirm}>
                {confirmText}
              </div>
            </div>
          </div>
        </div>
      ),
      document.body
    );
  }
}
