import React, { useState, ReactNode } from "react";
import Modal from "./index";

export interface IConfirm {
  onCancel?: () => void;
  onConfirm?: () => void;
}

export default (props: IConfirm): JSX.Element => {
  const { onCancel, onConfirm, ...otherProps } = props;
  const [visible, setVisible] = useState<boolean>(true);

  function handleCancel(): void {
    setVisible(false);
    onCancel && onCancel();
  }

  function handleConfirm(): void {
    setVisible(false);
    onConfirm && onConfirm();
  }

  return <Modal visible={visible} onCancel={handleCancel} onConfirm={handleConfirm} {...otherProps} />;
};
