@import "@src/common/styles/mixin.scss";

.webcast-modal-mask {
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: absolute;
    z-index: 1000;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    background-color: rgba(0, 0, 0, 0.5);
}

.webcast-modal {
    font-family: PingFangSC;
    border-radius: 2px;
    text-align: center;
    background-color: #fff;
    width: 280px;

    .contain {
        .title {
            justify-content: center;
            align-items: center;
            margin-top: 24px;
            display: flex;
            height: 24px;
            font-size: 17px;
            font-weight: 500;
            line-height: 1;
        }
        @include border-bottom-1($color: rgba(22, 24, 35, 0.12));
        .content {
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 8px 20px 24px;
            display: flex;
            font-size: 14px;
        }
        .content-text {
            color: rgba(22, 24, 35, 0.75);
            font-size: 14px;
        }
    }

    .footer {
        display: flex;
        height: 48px;
        border-radius: 0 0 2px 2px;
        font-size: 15px;
        .btn {
            flex: 1;
            justify-content: center;
            align-items: center;
            display: flex;
            color: #161823;
        }
        .btn-cancel {
            color: rgba(22, 24, 35, 0.75);
            border-right: 1px solid rgba(22, 24, 35, 0.12);
        }
        .btn-confirm {
            font-weight: 500;
        }
    }
}
