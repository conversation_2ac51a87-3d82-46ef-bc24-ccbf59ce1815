import React from "react";
import cx from "classnames";

import "./index.scss";

interface IProps {
  isChecked: boolean;
  onChange: (value: boolean) => void;
  label?: string;
}

const Checkbox = React.memo<IProps>((props) => {
  const { isChecked, onChange, label } = props;

  const handleClick = () => {
    onChange(!isChecked);
  };

  return (
    <div className="ecommerce_webcast-checkoutbox-wrap">
      <div
        className={cx("ecommerce_webcast-checkoutbox", {
          "ecommerce_webcast-checkoutbox-checked": isChecked,
        })}
        onClick={handleClick}
      ></div>
      <span className="ecommerce_webcast-checkoutbox-label">{label}</span>
    </div>
  );
});

export default Checkbox;
