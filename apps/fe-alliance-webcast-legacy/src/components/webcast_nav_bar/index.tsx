import React, {ReactNode} from 'react';
import classname from 'classnames';
import {closeWebview} from '@kit/bridge';
import IconArrowLeftBlack from '@components/Icon/IconArrowLeftBlack';
import './style';

export interface NavbarProps {
    hideBorder?: boolean;
    title: string;
    style?: object;
    rightIcons?: ReactNode;
}

export default function Navbar(props: NavbarProps) {
    const {title, hideBorder, rightIcons, style = {}} = props;
    return (
        <div className={classname('nav-bar', {'border-bottom': !hideBorder})} style={style}>
            <IconArrowLeftBlack size={24} className="nav-bar-left-icon" onClick={handleClose} />
            <div className="nav-bar-title">{title}</div>
            <div className="nav-bar-right-icon">{rightIcons}</div>
        </div>
    );
}

function handleClose() {
    closeWebview();
}
