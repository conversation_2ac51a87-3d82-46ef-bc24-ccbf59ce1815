@import "@styles/mixin.scss";

.nav-bar {
    justify-content: center;
    align-items: center;
    padding-right: 16px;
    padding-left: 16px;
    position: relative;
    display: flex;
    height: 100%;

    .nav-bar-left-icon {
        flex: 1;
    }

    .nav-bar-right-icon {
        flex: 1;
        justify-content: flex-end;
        display: flex;
    }

    .nav-bar-title {
        max-width: 240px;
        overflow: hidden;
        color: #161823;
        font-size: 17px;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    &.border-bottom {
        @include border-bottom-1($color: rgba(22, 24, 35, 0.12));
    }
}
