import React from 'react';
import './style';
import classNames from 'classnames';

interface Props {
    size?: string;
    isChecked: boolean;
    onChange?: () => void;
    className?: string;
    checkedImg?: string;
}

const iconCheckedImg = 'https://sf1-cdn-tos.douyinstatic.com/obj/eden-cn/nulwbozuloj/ecommerce_webcast/common/icon_checked.png';
const iconCheckImg = 'https://sf1-cdn-tos.douyinstatic.com/obj/eden-cn/nulwbozuloj/ecommerce_webcast/common/icon_check.png';

function CheckBox(props: Props) {
    const {size, isChecked, onChange, checkedImg, className, ...otherProps} = props;

    function getStyle({size = '16px'} = {}) {
        let _width = size;
        let _height = size;
        return {
            width: _width,
            height: _height,
            backgroundImage: isChecked ? (checkedImg ? `url(${checkedImg})` : `url(${iconCheckedImg})`) : `url(${iconCheckImg})`,
        };
    }

    return (
        <div className={classNames('webcast-checkbox', {'webcast-checkbox-checked': isChecked}, className)}
             style={getStyle({size})} onClick={onChange} {...otherProps}>
    </div>
    );
}

function getCheckedImgStyle(size: string) {
    const sizeNum = parseInt(size);
    return {
        width: `${sizeNum * 0.7}px`,
        height: 'auto',
    };
}

export default CheckBox;
