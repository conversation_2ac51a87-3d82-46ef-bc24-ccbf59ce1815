.webcast-tabs {
  .tabs-header {
    display: flex;
    position: relative;
    text-align: center;
  }

  .tab-item {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: #737373;
  }

  .tab-item-active {
    color: #1a1a1a;
  }

  .px-tab-active-slider {
    position: absolute;
    left: 0;
    bottom: 0;
    height: 3px;
    background-color: #ff264a;
    will-change: transform;
  }

  .transition-active-slider {
    transition: transform 400ms ease-out;
  }

  .tab-content-wrap {
    display: none;
  }

  .tab-content-active {
    display: block;
  }
}
