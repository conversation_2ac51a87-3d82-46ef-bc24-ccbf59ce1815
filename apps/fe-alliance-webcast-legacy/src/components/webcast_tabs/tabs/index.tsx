import React, { useEffect, useLayoutEffect, useState } from "react";
import classNames from "classnames";
import "./style";
import { isAweme } from "../../../lib/env_detect";

export interface TabsProps {
  tabs: Array<{ key: string; tab: string }>;
  width?: string;
  height?: string;
  activeKey?: string;
  defaultActiveKey?: string;
  type?: string;
  onTabChange?: (activeKey) => void;
  onTabClick?: (tabKey) => void;
  children: any;
  className?: string;
}

const Tabs = (props: TabsProps) => {
  const { tabs = [], width = "100%", height = "38px", activeKey, defaultActiveKey, type = "line", onTabChange, onTabClick, children, className, ...otherProps } = props;
  const [tabsHeaderRef, setTabsHeaderRef] = useState<HTMLDivElement | null>(null);
  const [visibleTas, setVisibleTas] = useState<string[]>([]);
  const [currentActiveKey, setCurrentActiveKey] = useState<string>();
  const [activeSliderRef, setActiveSliderRef] = useState<HTMLDivElement | null>(null);
  const [isTransition, setIsTransition] = useState(false);

  useLayoutEffect(() => {
    setDefaultActiveTab();
  }, []);

  useEffect(() => {
    if (activeSliderRef && onTabChange) {
      activeSliderRef.addEventListener("transitionend", handleTabChange, true);
      return () => {
        activeSliderRef.removeEventListener("transitionend", handleTabChange, true);
      };
    }
  });

  function handleTabChange() {
    onTabChange && onTabChange(currentActiveKey);
  }

  function setTabItemActivity(itemKey) {
    return { "tab-item-active": currentActiveKey === itemKey };
  }

  function setDefaultActiveTab() {
    const defaultKey = activeKey || defaultActiveKey || tabs[0].key;
    setCurrentActiveKey(defaultKey);
    setVisibleTas([defaultKey]);
  }

  function handleTabClick(tabKey) {
    if (!isTransition) {
      setIsTransition(true);
    }
    setCurrentActiveKey(tabKey);
    if (!isVisibleTab(tabKey)) {
      setVisibleTas([tabKey, ...visibleTas]);
    }
    onTabClick && onTabClick(tabKey);
  }

  function getActiveSliderStyle() {
    if (tabsHeaderRef) {
      const tabWidth: any = (tabsHeaderRef.offsetWidth / tabs.length).toFixed(3);
      const activeIndex = findActiveIndex();
      if (isAweme) {
        return {
          width: `${tabWidth}px`,
          transform: `translateX(${tabWidth * activeIndex}px)`,
        };
      } else {
        return {
          width: `${Math.max(tabWidth * 0.15, 24)}px`,
          transform: `translateX(${tabWidth * activeIndex + (tabWidth - Math.max(tabWidth * 0.15, 12)) / 2}px)`,
        };
      }
    }
  }

  function findActiveIndex() {
    let activeIndex = 0;
    tabs.forEach((item, index) => {
      if (item.key === currentActiveKey) {
        activeIndex = index;
      }
    });
    return activeIndex;
  }

  function isVisibleTab(tabKey) {
    return visibleTas.includes(tabKey);
  }

  function isTabContentActive(index) {
    return index === findActiveIndex();
  }

  return (
    <div className={classNames("webcast-tabs", className)} {...otherProps}>
      <div className="tabs-header" ref={(el) => setTabsHeaderRef(el)} style={getTabsHeaderStyle({ width, height })}>
        {tabs.map((item) => {
          return (
            <div
              className={classNames("tab-item", setTabItemActivity(item.key))}
              onClick={() => {
                handleTabClick(item.key);
              }}
              key={item.key}
            >
              {item.tab}
            </div>
          );
        })}
        {type === "line" && (
          <div className={classNames("px-tab-active-slider", { "transition-active-slider": isTransition })} ref={(el) => setActiveSliderRef(el)} style={getActiveSliderStyle()}></div>
        )}
      </div>
      <div className="tabs-content-wrap">
        {React.Children.map(children, (child, index) => (
          <div className={classNames("tab-content-wrap", { "tab-content-active": isTabContentActive(index) })}>{child}</div>
        ))}
      </div>
    </div>
  );
};

function getTabsHeaderStyle({ width, height }): { width: string; height: string } {
  return {
    width,
    height,
  };
}

export default React.memo(Tabs);
