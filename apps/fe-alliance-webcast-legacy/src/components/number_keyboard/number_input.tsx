import React, { useState, ReactNode, useEffect, useRef } from "react";
import NumberKeyboard from "./index";
import cx from "classnames";

import "./number_input.scss";

export interface NumberInputProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onBlur?: () => void;
  className?: string;
  prefix?: ReactNode;
  affix?: ReactNode;
  hideAffixWhenActive?: boolean;
  disableDot?: boolean;
  autoFocus?: boolean;
  style?: any;
  maxLength?: number;
  price?: number;
}

const NumberInput = React.forwardRef((props: NumberInputProps, boxRef: any) => {
  const [active, setActive] = useState(false);
  const {
    style,
    className,
    placeholder = "　",
    value = "",
    onChange,
    prefix,
    affix,
    disableDot,
    onBlur,
    maxLength,
    autoFocus,
    hideAffixWhenActive,
  } = props;
  boxRef = boxRef || useRef<any>(null);
  const handleWindowClick = (e) => {
    if (!active) {
      return;
    }
    const target = e.target;
    const box = boxRef.current;
    const keyboard = document.querySelector(".number-keyboard");
    if (
      document.contains(target) &&
      box &&
      box !== target &&
      !box.contains(target) &&
      keyboard &&
      keyboard !== target &&
      !keyboard.contains(target)
    ) {
      onBlur?.();
      setActive(false);
    }
  };
  useEffect(() => {
    if (autoFocus) {
      setTimeout(() => {
        setActive(true);
      }, 50);
    }
  }, []);
  useEffect(() => {
    window.addEventListener("click", handleWindowClick);
    return () => {
      window.removeEventListener("click", handleWindowClick);
    };
  });
  return (
    <>
      <div
        className={cx("number-input", { active }, className)}
        style={style}
        onClick={() => setActive(true)}
        ref={boxRef}
      >
        {prefix}
        <div className={cx("content", { placeholder: !value })}>
          {value || placeholder}
        </div>
        <div style={{ flex: 1 }}></div>
        {!!props.value && active && (
          <div
            style={{
              paddingTop: 8,
              paddingBottom: 8,
              paddingRight: 8,
              display: !!props.value && active ? "block" : "none",
            }}
            onClick={() => props.onChange?.("")}
          >
            <div className="ic-close"></div>
          </div>
        )}
        {(!hideAffixWhenActive || (hideAffixWhenActive && !active)) && affix}
      </div>
      <NumberKeyboard
        disableDot={disableDot}
        isOpen={active}
        onHide={() => {
          setActive(false);
          onBlur?.();
        }}
        onInput={(char) => {
          if (maxLength !== undefined && value.length >= maxLength) {
            return;
          }
          onChange?.(value + char);
        }}
        onDel={() => {
          onChange?.(value.slice(0, -1));
        }}
      />
    </>
  );
});
export default NumberInput;
