import React, { ReactNode, useEffect } from "react";
import <PERSON><PERSON> from "react-modal";
import { isIPhoneX } from "@src/lib/env_detect";
import cx from "classnames";
import "./styles.scss";

export interface KeyboardProps extends ReactModal.Props {
  onInput?: (char: string) => void;
  onDel?: () => void;
  onHide?: () => void;
  disableDot?: boolean;
}

export default React.memo((props: KeyboardProps) => {
  const keyboardMap: Array<Array<{ node?: ReactNode; action?: string }>> = [
    [
      { node: "1", action: "1" },
      { node: "4", action: "4" },
      { node: "7", action: "7" },
      { node: ".", action: "." },
    ],
    [
      { node: "2", action: "2" },
      { node: "5", action: "5" },
      { node: "8", action: "8" },
      { node: "0", action: "0" },
    ],
    [
      { node: "3", action: "3" },
      { node: "6", action: "6" },
      { node: "9", action: "9" },
      { node: <i className="ic-keyboard-down"></i>, action: "hide" },
    ],
    [
      { node: <i className="ic-keyboard-del"></i>, action: "del" },
      { node: "确定", action: "hide" },
    ],
  ];
  const {
    closeTimeoutMS = 400,
    ariaHideApp = false,
    onInput,
    onHide,
    onDel,
    disableDot,
    isOpen,
  } = props;
  if (disableDot) {
    keyboardMap[0][3] = {};
  }
  const onAction = (action: string) => {
    switch (action) {
      case "hide":
        onHide?.();
        break;
      case "del":
        onDel?.();
        break;
      default:
        onInput?.(action);
    }
  };
  // useEffect(() => {
  //   const container: any = document.querySelector(".keyboard-avoid-container");
  //   if (container) {
  //     container.style.paddingBottom = isOpen
  //       ? `${207 + (isIPhoneX ? 34 : 0)}px`
  //       : `${isIPhoneX ? 34 : 0}px`;
  //   }
  // });
  return (
    <Modal
      closeTimeoutMS={closeTimeoutMS}
      ariaHideApp={ariaHideApp}
      {...props}
      style={{
        overlay: {
          paddingBottom: isIPhoneX ? 34 : 0,
        },
      }}
      overlayClassName="number-keyboard-overlay"
      className="number-keyboard"
    >
      {keyboardMap.map((col, index) => (
        <>
          {index !== 0 && <div className="col-line"></div>}
          <div className="col">
            {col.map((row, index2) => (
              <>
                <div className="row-line"></div>
                <div
                  className={cx("row", { red: index === 3 && index2 === 1 })}
                  onClick={() => row.action && onAction(row.action)}
                >
                  {row.node}
                </div>
                {index2 === col.length - 1 && <div className="row-line"></div>}
              </>
            ))}
          </div>
        </>
      ))}
    </Modal>
  );
});
