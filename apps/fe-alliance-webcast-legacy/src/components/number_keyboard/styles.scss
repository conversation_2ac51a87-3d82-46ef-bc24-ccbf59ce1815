@import "~common/styles/merch_picking/mixins";
@import "~common/styles/merch_picking/visual/hairline";
@import "~common/styles/merch_picking/modules/keyframe";

.ReactModalPortal {
  .number-keyboard-overlay {
    z-index: 1003;
    top: auto;
    width: 100%;
    height: 200px;
    background-color: #ffffff;
    &.ReactModal__Overlay--after-open {
      animation: bottom-trans-in 0.2s ease-in-out;
    }
    &.ReactModal__Overlay--before-close {
      animation: bottom-trans-out 0.4s ease-in-out;
    }
  }
  .number-keyboard {
    display: flex;
    flex-direction: row;
    height: 100%;
    font-size: 25px;
    color: #2a2b2c;
    .col {
      flex: 1;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
    }
    .col-line {
      width: 1px;
      height: 100%;
      transform: scaleX(0.5);
      background-color: #ccc;
    }
    .row-line {
      width: 100%;
      height: 1px;
      transform: scaleY(0.5);
      background-color: #ccc;
    }
    .row {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      user-select: none;
        &.red {
          background-color: #ff264a;
          color: #ffffff;
        }
    }
  }
  .ic-keyboard-down {
    width: 22px;
    height: 16.35px;
    background-size: 100% 100%;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEIAAAAyCAYAAADodg0pAAAACXBIWXMAACE4AAAhOAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAIZSURBVHgB7Zr9bcIwEMUfbf8v3cCdgLIBGzSdoDAB6gR1J6ATlLJAUScANmCDZoNmgtIc2MrZwhbfX76fdNJZD5H4KZc7EgAgK+O3jFmi8UMe1EyikDYFGTEzixrSZL7/KwhzxAiDGGGIGTHC4kZqQzFt6GkPEe2RaT1P6zKt62nvTMs8rc+0VkRTnjZCgBuEUQh3k7qn1SPanZevqt1GvpPnWEMLIqVhECMMsTlCeeuc5XW45VCYOJTmn4/aUCPm+5eBSgYql1jXoDak2LqD6rKi1tZg2ksZ04BG62+TU4vMmPZZxsDkz2W0mTYpQ5u8VcYr06bmmAS17l5Ao3IaMo1K7QlLiBnRQrj1NIwOdsCQNmB509MmLL/3tJzlytPgHXtVLQ98TkrDIkYYYl2j5a3HLKe65OVAdVlsoOWoLlcFtxRjWoHqnlSHO+KvqlmkfRqkfXIO0T73wVm2z30j7XOXiBGGWGlQnfm/Bi1vcCfGPKLtA34udE/oBLQiojlI+zT751dEqkbM4UZ8IHG+yvjDab+f3GfQ3nu2HDK4M3lK5Fg8FxEEQRCEE6CN6nH8Lmju+PsOAs0kdmDR2B4ywf7hLcOZobEbM7gJfZwpGtuZcREmWDQ2M+OiTLBorGfGRZpg0VjNjIs2waIRNyMJEyway81IygSLhmtGkiZYNCozkjXBolGZcVQTrnFcxlg8Pc/hvn8QjsU/EjQ29kENy5cAAAAASUVORK5CYII=);
  }
  .ic-keyboard-del {
    width: 25.5px;
    height: 18.55px;
    background-size: 100% 100%;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAE0AAAA5CAYAAABzuqZnAAAACXBIWXMAACE4AAAhOAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAMzSURBVHgB7ZvtUdwwEEBfZvI/lCA6uFQQSqADu4PQgTcVQAXhUgEl0EHSAVBBSAVcrOE83AD2rqT11w1vZsd/LCM9dNJKluGDZD4xLWcsl39tPOyvi+Gyjd0K4rGNmzYqZqZhHcJexx0zyWtYp7AunvZtmIyKdQs7jJ9MwIbn8WFNYmbtcaGNe2B3ZBHFnY2RcoQ2bvfXPh7a+MGy+NLG1za+MVz3W5w5Qe9h90ql5uaU55RjqLe58od1C+uIv8DYo/ra4YaWvMZJYcN6iD1uVGkN+iBasy6GelsxFmHC+ojStowgrcJfWECffXNpsOdao0jboAu7Io3Ay+zrPWkIL7OfRZy7tICe7d+QRuBtuuIlTnibNmjiXKUF9Fwsph4nTs+8o0yc0J9vDYlzkxYYJ3m9UJ6ZK04Mz+3757pJGzN5FXzFSeHzXKRdK5V4pHz8EXzEicNziqU1SiVinOODUNbg0vIdRdIswi7wRchreG6598iW9h1dmDAOQpqA1Ps1sqSdM5+wDsEmwnpfCsnSAnryumUaRKmHVs/cdCVJWsA/eS1F0Hu9p7CIWVpguTuvwnTCIiZpsecsfedVmEZYxCTtBn3sCMxLbIhWz7im9NghVqVZZsrUXYsxqLH1tN+Uj7mmniaGylwyHzVpY1qpOPNEIIbKNExPTZowD3FJKYewLHE1+lg7hrjk5PaKZYirlDpYVwQ54rKWUVt0cd4L9UOswjoEX3HZC/YturgKf1KFRYYamSMuW5ol4fUWpwn7S38e5imuaD/NIs7ryEGJsA4vccU7twF9TVoqzkNYh4c4l3cEAZu4QDqewjpKxS3+Fd7QUdMcYR2auOvMsskEphPnMVb2Nf46s1yWtIjlEHKpOM/zbK8FaMLeK1MsLWIVl5qJb/blvA8AdhIswg7vd5UWOWNYWoypt8a9GE1apEYXd8v6GPUkZKRGF2f9WSyFU/qHHzeE4xIn9Ldjsj/URcPyEfrr//QZX2R/bQz3/GJZxMkqztgVwx/zPjASgt7j1hjxTVfFiGyB3ZHFJJ8vboHdkcTQUVNXrJuYS4+4GxKYkLWLE2bC8knjkiImtUJP7/oPs8VbIAxk5wIAAAAASUVORK5CYII=);
  }
}

@keyframes bottom-trans-in {
  0% {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes bottom-trans-out {
  0% {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}
