import React, { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Handler } from 'react';
import { EMPTY_CONTENT_IMG } from 'common/constants';

import './index.scss';

export interface Props {
    imgUrl?: string | null;
    hint?: ReactNode | null;
    children?: ReactNode;
    action?: ReactNode;
    onActionClick?: <PERSON><PERSON><PERSON>Handler;
}

export default React.memo((props: Props) => {
    const { imgUrl = EMPTY_CONTENT_IMG, hint, children, action, onActionClick } = props;
    return (
        <div className="empty-view">
            {imgUrl ? <img src={imgUrl} className="empty-view__img" /> : null}
            {hint ? <p className="empty-view__hint">{hint}</p> : null}
            {!action || !onActionClick ? null : (
                <span className="empty-view__action" onClick={onActionClick}>
                    {action}
                </span>
            )}
            {children}
        </div>
    );
});
