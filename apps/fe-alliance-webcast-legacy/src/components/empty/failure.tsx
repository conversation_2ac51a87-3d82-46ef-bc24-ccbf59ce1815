import React, { FC, memo } from 'react';
import { NETWORK_ERROR_IMG_DOUYIN } from 'constants/index';
import EmptyView, { Props } from './index';
import './failure.scss';

const EmbeddedFailure: FC<Props> = memo(props => {
    const {
        hint = '网络超时',
        action = '重新加载',
        imgUrl = NETWORK_ERROR_IMG_DOUYIN,
        onActionClick = () => location.reload()
    } = props;
    return (
        <div className="embedded-failure fcol f-ai-c">
            <EmptyView hint={hint} action={action} imgUrl={imgUrl} onActionClick={onActionClick} />
        </div>
    );
});

export default EmbeddedFailure;
