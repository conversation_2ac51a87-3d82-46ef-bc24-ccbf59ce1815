.embedded-failure {
    height: 100vh;
    background: white;
    .empty-view {
        padding: 108px 0 0;
        &__img {
            margin: 0;
            width: 240px;
            height: 160px;
        }
        &__hint {
            margin: 32px 0 102px;
            height: 17px;
            font-weight: 400;
            line-height: 17px;
        }
        &__action {
            position: relative;
            padding: 0;
            width: 231px;
            height: 44px;
            border: none;
            font-size: 15px;
            color: rgba(22, 24, 35, 0.9);
            line-height: 44px;
            border-radius: 2px;
            background: white;
            &::after {
                content: '';
                display: block;
                position: absolute;
                top: 0;
                left: 0;
                width: 200%;
                height: 200%;
                border: 1px solid rgba(22, 24, 35, 0.12);
                transform: scale(0.5);
                transform-origin: 0 0;
            }
        }
    }
}
