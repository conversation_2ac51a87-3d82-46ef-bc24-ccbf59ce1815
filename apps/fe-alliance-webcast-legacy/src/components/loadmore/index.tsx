import React, { useRef, useEffect } from 'react';
import { observe, unobserve, digestTarget } from '@src/lib/scroll';

import './index.scss';

interface Props {
    hasMore: boolean;
    isFetching: boolean;
    isFetchFailed: boolean;
    errorText?: string;
    noMoreText?: string;
    fetchingText?: string;
    onRetry?: Function;
    onActive: Function;
}

interface State {
    props: Props;
    prevProps: Props | null;
}

export default React.memo((props: Props) => {
    const {
        hasMore,
        isFetching,
        isFetchFailed,
        onRetry,
        errorText = '加载失败，点击重试',
        noMoreText = '没有更多内容啦',
        fetchingText = '正在加载'
    } = props;

    const stateRef = useRef<State>({
        props,
        prevProps: null
    });
    stateRef.current.props = props;

    const elRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handler = (bounding: ClientRect | null) => {
            const { props: s, prevProps } = stateRef.current;

            const visible = Boolean(bounding);

            if (visible && s.hasMore && !s.isFetching && !s.isFetchFailed && s !== prevProps) {
                s.onActive();
                stateRef.current.prevProps = props;
            }
        };
        const el = elRef.current as HTMLDivElement;
        observe(el, handler);
        return () => unobserve(el, handler);
    }, []);

    useEffect(() => {
        if (!isFetching && hasMore && !isFetchFailed) {
            digestTarget(elRef.current as HTMLDivElement);
        }
    }, [isFetching]);

    const contentText = !hasMore ? noMoreText : isFetchFailed ? errorText : fetchingText;

    const onClick = () => {
        if (isFetchFailed && !isFetching && hasMore && typeof onRetry === 'function') {
            onRetry();
        }
    };

    return (
        <div className="loadmore" ref={elRef} onClick={onClick}>
            {hasMore && !isFetchFailed ? <span className="loadmore__spinner" /> : null}
            <span className="loadmore__text">{contentText}</span>
        </div>
    );
});
