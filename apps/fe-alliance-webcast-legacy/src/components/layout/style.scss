@import "@src/common/styles/base.scss";
@import "./variables.scss";

.app {
  font-family: "PingFangSC-Semibold", "PingFang SC";
  font-size: 17px;
  position: relative;
  background-color: #fff;
  min-height: 100vh;

  .navbar-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    z-index: 99;
  }

  .content-wrapper {
    padding-top: 44px;
  }
}

@each $appname in (toutiao, xigua, douyin) {
  .#{$appname} {
    .ecom-border-radius {
      border-radius: map-get($prefix-border-radius, $appname);
    }

    .ecom-desc {
      color: map-get($prefix-desc-color, $appname);
    }

    .ecom-title {
      color: map-get($prefix-title-color, $appname);;
    }

    .ecom-title-color {
      color: map-get($prefix-title-color, $appname);;
    }
  }
}
