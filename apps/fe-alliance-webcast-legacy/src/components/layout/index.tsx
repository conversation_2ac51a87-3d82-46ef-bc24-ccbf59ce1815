import React, { ReactNode } from 'react';
import Navbar from '@src/components/nav_bar';
import { getAppnameFromEnv } from '@lib/env_detect';
import cx from 'classnames';

import './style.scss';

interface Props {
  title: string;
  navbarStyle?: object;
  rightIcons?: ReactNode;
  leftIconCallback?: VoidFunction;
  className?: string;
}

/**
 * 页面组件
 * @param {string} title
 * @param {ReactNode} rightIcons
 * @param {object} navbarStyle
 */
class Layout extends React.PureComponent<Props> {
  static defaultProps = {
    title: '',
    hideBorder: false,
  };

  render(): ReactNode {
    const { children, title, navbarStyle = {}, rightIcons, className, ...rest } = this.props;

    return (
      <div className={cx('app', getAppnameFromEnv(), className || '')}>
        <div className="navbar-wrapper">
          <Navbar style={navbarStyle} title={title} rightIcons={rightIcons} {...rest} />
        </div>
        <div className="content-wrapper">{children}</div>
      </div>
    );
  }
}

export default Layout;
