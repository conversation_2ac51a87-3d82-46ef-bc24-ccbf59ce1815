import React, { <PERSON>EventHandler } from 'react';
import Confirm from 'components/modal/confirm';

interface Props {
    isOpen: boolean;
    onConfirm: MouseEventHandler;
    onCancel: MouseEventHandler;
}

export default React.memo((props: Props) => {
    return (
        <Confirm
            title="还未绑定淘宝客PID"
            body="需要绑定后才可以添加淘宝商品"
            confirmText="绑定PID"
            {...props}
        />
    );
});
