import React, { MouseEventHandler } from 'react';
import Confirm from 'components/modal/confirm';
import { genSchema } from '@src/lib/util/merch_picking';
interface Props {
    isOpen: boolean;
    onConfirm: MouseEventHandler;
    onCancel: MouseEventHandler;
}

export default React.memo((props: Props) => {
    const onLinkClick = () => {
        window.location.href = genSchema({
            url:
                'https://automan.jinritemai.com/docpage/views/9e380069-77bc-443d-b020-ccdf3fe7f746',
            title: '精选联盟服务协议',
            use_ui: 1,
            hide_more: 1,
            show_more_button: 1,
            copy_link_action: 0
        });
    };

    return (
        <Confirm
            title="还未开通联盟收款账户"
            body={
                <span style={{ display: 'block', textAlign: 'left' }}>
                    {
                        "点击'同意'将授权精选联盟获取你的账号信息（昵称、头像、手机号）并进入账户开通流程，使用前请仔细阅读并同意"
                    }
                    <a style={{ color: 'red' }} onClick={onLinkClick}>
                        《精选联盟服务协议》
                    </a>
                </span>
            }
            confirmText="同意"
            {...props}
        />
    );
});
