import React, {ReactNode} from 'react';
import Navbar from '../webcast_nav_bar';
import './style.scss';

interface LayoutProps {
    title?: string;
    rightIcons?: ReactNode;
    hideBorder?: boolean;
    hideNavBar?: boolean
    children?: ReactNode;
    navbarStyle?: object;
}

export default function Layout(props: LayoutProps) {
    const {children, title = '', hideNavBar = false, navbarStyle = {}, rightIcons, hideBorder = false, ...otherProps} = props;
    return (
        <div className="app" {...otherProps}>
            {
                !hideNavBar &&
                <div className="navbar-wrapper">
                    <Navbar title={title} rightIcons={rightIcons} hideBorder={hideBorder} style={navbarStyle} />
                </div>
            }
            <div className="content-wrapper">{children}</div>
        </div>
    );
}

