/* eslint-disable react/destructuring-assignment */
/* eslint-disable @typescript-eslint/ban-types */
import cx from 'classnames';
import React, { useRef, useEffect, MouseEventHandler, useImperativeHandle } from 'react';

import { NETWORK_ERROR_IMG } from 'common/constants';
import { useListState, ListState, ListFetcher, ListFetchParams } from './state';

import DefaultEmptyView from '../empty';
import Loadmore from 'components/loadmore';
import './index.scss';

export interface Props<Item> {
  session?: {
    searchText?: string;
    sortingOption?: object;
  };
  className?: string;
  interactive?: boolean;
  fetcher: ListFetcher<Item> | null;
  isHiddenLoadMore?: boolean;
  ItemSkeleton: React.MemoExoticComponent<React.FunctionComponent>;
  renderItem(data: Item, index: number): React.ReactNode;
  EmptyView?(state: ListState<Item>, onRetry: MouseEventHandler): React.ReactNode;
  /** 列表更新时调用 */
  onListUpdate?(list: Item[]): void;
  onStateChange?(state: ListState<Item>): void;
  changeShowAddRecommend?(show: boolean): void;
}

export type ListRef<Item> = {
  listData?: Item[];
  setListFetchParams(params: ListFetchParams): void;
  fetchListData(options?: { reset?: boolean }): void;
};

function ListEmptyView<Item>(state: ListState<Item>, onRetry: MouseEventHandler) {
  const EMPTY_IMG = 'https://sf3-cdn-tos.douyinstatic.com/obj/temai/FovqrhG7EkuCA8MWDqm1LaVmYA4Swww672-267';
  return (
    <DefaultEmptyView
      hint={state.isFetchFailed ? '加载失败' : '暂无商品'}
      imgUrl={state.isFetchFailed ? NETWORK_ERROR_IMG : EMPTY_IMG}
      action="点击重试"
      onActionClick={onRetry}
    />
  );
}

function List<Item>(props: Props<Item>, ref: React.ForwardedRef<ListRef<Item>>) {
  const {
    session,
    className,
    fetcher,
    renderItem,
    ItemSkeleton,
    interactive = true,
    onListUpdate,
    EmptyView = ListEmptyView,
    isHiddenLoadMore = false,
    onStateChange,
    changeShowAddRecommend,
  } = props;
  const [state, fetchListData, setListFetchParams, firstState] = useListState<Item>();

  useEffect(() => {
    onStateChange?.(state);
  }, [state, onStateChange]);

  const listRef = useRef<HTMLDivElement>(null);

  const onEndReached = () => fetchListData(fetcher);

  useEffect(() => {
    fetchListData(fetcher, true);
    if (listRef.current) {
      listRef.current.scrollTop = 0;
    }
  }, [session]);

  useEffect(() => {
    onListUpdate?.(state.items);
  }, [state.items]);

  useEffect(() => {
    const isShow = Boolean(
      !Object.keys(firstState.commonFilter || {})?.length &&
        !firstState.items?.length &&
        firstState.cursor === 0 &&
        !firstState.hasMore
    );
    changeShowAddRecommend?.(isShow);
  }, [firstState.items, firstState.cursor, firstState.hasMore, firstState.commonFilter]);

  useEffect(() => {
    if (state.items.length < 10 && state.hasMore && !state.isFetching && !state.isFetchFailed) {
      fetchListData(fetcher);
    }
  }, [state.items.length, state.hasMore, state.isFetchFailed, state.isFetching]);

  useImperativeHandle(ref, () => ({
    setListFetchParams,
    fetchListData: ({ reset = false } = {}) => fetchListData(fetcher, reset),
    listData: state?.items,
  }));

  let content;
  if (state.items.length > 0 && interactive) {
    content = state.items.map(renderItem);
  }

  if (!state.isFetching && state.items.length === 0) {
    content = EmptyView({ ...state, searchText: session?.searchText }, () => fetchListData(fetcher, true));
  }

  if (!interactive || (!content && state.hasMore && state.items.length === 0)) {
    const skeleton = [...Array(8)];
    content = skeleton.map((_, index) => <ItemSkeleton key={index} />);
  }
  const loadmore =
    state.items.length === 0 || !interactive || isHiddenLoadMore ? null : (
      <Loadmore
        onRetry={onEndReached}
        onActive={onEndReached}
        hasMore={state.hasMore}
        isFetching={state.isFetching}
        isFetchFailed={state.isFetchFailed}
      />
    );

  return (
    <div className={cx('list', className)} ref={listRef}>
      {content}
      {loadmore}
    </div>
  );
}

function createList<Item>() {
  return React.memo(React.forwardRef<ListRef<Item>, Props<Item>>(List));
}

export default createList;
