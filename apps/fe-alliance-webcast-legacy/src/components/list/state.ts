/* eslint-disable no-multi-assign */
import { useRef, useState } from 'react';
import { isNil } from 'lodash';

export interface ListState<Item> {
  page: number;
  cursor: number;
  pageSize: number;
  commonFilter: Record<string, unknown>;
  total: number;
  items: Item[];
  nextOffset: number;
  hasMore: boolean;
  isFetching: boolean;
  isFetchFailed: boolean;
  fetchFailedCode: number | null;
  fetchFailedReason: string | null;
  feedId?: string;
  searchText?: string; // 搜索词
}

export interface ListFetchResponse<Item> {
  items: Item[];
  has_more: boolean;
  total: number;
  feed_id?: string;
}

export type ListFetcher<Item> = (s: ListState<Item>) => Promise<ListFetchResponse<Item>>;

export type ListFetchController<Item> = (
  fetcher: ListFetcher<Item> | null,
  reset?: boolean,
  fromState?: ListState<Item>
) => void;

export type ListFetchParams = Partial<
  Pick<ListState<never>, 'page' | 'pageSize' | 'total' | 'cursor' | 'commonFilter'>
>;

export type SetListFetchParams = (params: ListFetchParams) => void;

export function useListState<Item>(): [
  ListState<Item>,
  ListFetchController<Item>,
  SetListFetchParams,
  ListState<Item>
] {
  const initialState: ListState<Item> = {
    items: [],
    total: 0,
    pageSize: 20,
    page: 0,
    cursor: 0,
    commonFilter: {},
    nextOffset: 0,
    hasMore: true,
    isFetching: true,
    isFetchFailed: false,
    fetchFailedCode: null,
    fetchFailedReason: '',
    feedId: '',
  };
  const [state, setState] = useState(initialState);
  const [firstState, setFirstState] = useState(initialState);
  const fetchParams = useRef<ListFetchParams>({});

  const fetchIdentifier = useRef({});

  const fetchListData = (fetcher: ListFetcher<Item> | null, reset = false, fromState = state) => {
    if (typeof fetcher !== 'function') {
      return;
    }

    const identifier = (fetchIdentifier.current = {});

    let forState = { ...fromState, ...fetchParams.current };
    if (reset) {
      forState = { ...initialState, ...fetchParams.current };
    }
    fetchParams.current = {};

    setState({
      ...forState,
      hasMore: true,
      isFetching: true,
      isFetchFailed: false,
      fetchFailedCode: null,
      fetchFailedReason: '',
    });

    setFirstState({
      ...firstState,
      isFetching: true,
      isFetchFailed: false,
    });

    fetcher(forState)
      .then(res => {
        if (identifier !== fetchIdentifier.current) {
          return;
        }
        // 线上问题：由于/selection_api/selection_square/get_tab_pmt和/selection_api/selection_square/tab_promotion_search返回的结构不一致，前端先做一下兼容，后续推服务端合并api
        // /get_tab_pmt 使用cursor和count 有返回has_more，tab_promotion_search使用page（从0开始）和size，没有返回has_more
        // 注意自己计算的时候不要用累计的promotions的长度和total比，因为选品车和我的橱窗有过滤逻辑，实际返回的商品promotions数量小于total；因此使用page和pageSize来判断
        const hasMoreCompatible = isNil(res?.has_more)
          ? (forState?.page + 1) * forState?.pageSize < (res?.total || 0)
          : res.has_more;
        const nextState = {
          ...forState,
          isFetching: false,
          isFetchFailed: false,
          items: [...forState.items, ...res.items],
          hasMore: hasMoreCompatible,
          nextOffset: forState.nextOffset + forState.pageSize,
          page: forState.page + 1,
          cursor: forState.cursor + forState.pageSize,
          total: res.total,
          feedId: res.feed_id || '',
        };
        setState(nextState);
        setFirstState({
          ...initialState,
          isFetching: false,
          isFetchFailed: false,
          hasMore: res?.has_more,
          commonFilter: forState?.commonFilter || {},
          items: [...forState.items, ...res.items],
        });
        if (nextState.items.length === 0 && nextState.hasMore) {
          fetchListData(fetcher, false, nextState);
        }
      })
      .catch(error => {
        if (identifier !== fetchIdentifier.current) {
          return;
        }
        setState({
          ...forState,
          isFetching: false,
          isFetchFailed: true,
          fetchFailedCode: error.status_code,
          fetchFailedReason: error.status_msg,
        });
      });
  };

  const setListFetchParams = (params: ListFetchParams) => {
    fetchParams.current = params;
  };

  return [state, fetchListData, setListFetchParams, firstState];
}
