/* eslint-disable function-paren-newline */
import React, { useEffect } from "react";
import { setGlobalProps } from "@ecom/auxo-mobile/es/_util/index.lepus";
import { usePersistCallback } from "@byted/hooks";
import { getAppnameFromEnv } from "@lib/env_detect";
import { getSingleQueryValue } from "@lib/util/url";

function getComponentName(comp: any) {
  return comp?.name || comp?.displayName || "component";
}

interface IProps {
  children: React.ReactElement<any, string | React.JSXElementConstructor<any>>;
  getAppname?: () => string;
  defaultAppname?: string;
}

function MobileComponentWrapper(props: IProps) {
  const {
    children,
    getAppname = getAppnameFromEnv,
    defaultAppname = "douyin",
  } = props;
  const getName = usePersistCallback(() => {
    if (
      process.env.NODE_ENV !== "production" &&
      getSingleQueryValue("appname")
    ) {
      return getSingleQueryValue("appname");
    }
    if (typeof getAppname === "function") {
      return getAppname() || defaultAppname;
    }
    return defaultAppname;
  });
  useEffect(() => {
    const appname = getName();
    setGlobalProps({ appname });
  }, []);

  return children;
}

// eslint-disable-next-line space-before-function-paren
export function mobileComponentDecorate<
  T extends (new (...args: any[]) => any) | ((props: any) => any)
>(
  Comp: T,
  config: { getAppname?: () => string; defaultAppname?: string } = {}
) {
  function DecorateComponent(props: any) {
    return (
      <MobileComponentWrapper {...config}>
       <Comp {...props} />
      </MobileComponentWrapper>
    );
  }
  DecorateComponent.displayName = `${getComponentName(
    Comp
  )}-mobileComponentDecorate`;
  return DecorateComponent;
}

export default MobileComponentWrapper;
