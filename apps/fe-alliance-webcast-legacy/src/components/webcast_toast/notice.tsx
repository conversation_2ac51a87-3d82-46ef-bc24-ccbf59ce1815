import React from "react";
import classNames from "classnames";

export interface INotice {
  content: string;
  type: string;
  className?: string;
}

const Notice = (props: INotice) => {
  const { content, className, ...otherProps } = props;

  return (
    <div className={classNames("webcast-toast", className)} {...otherProps}>
      <div className="toast-content">{<p>{content}</p>}</div>
    </div>
  );
};

export default React.memo(Notice);
