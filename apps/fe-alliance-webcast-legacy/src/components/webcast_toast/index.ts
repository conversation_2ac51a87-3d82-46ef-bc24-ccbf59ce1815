import './style';
import notification from './notification';
import {isInApp} from '../../lib/env_detect';
import {showToast} from '../../common/bridge';

let singleNotification;

const addNotice = (type: string, content: string, duration = 2000, onClose?: () => void): Promise<any> => {
    if (!singleNotification) {
        singleNotification = notification;
    }
    return singleNotification.addNotice({type, content, duration, onClose});
};

interface Toast {
    content: string;
    duration?: number;
    onClose?: () => void;
    useWeb?: boolean;
}

// 后续可以扩展 sucess，error等类型
// 客户端via没有提供调用结束回调，所以暂时不加入

/**
 * 消息提示
 * @param {function} info - {content: string, duration: 2000, onClose: function}
 * @param {functon} destroy
 */
export default {
    info({content, duration, onClose, useWeb = true}: Toast) {
        if (!useWeb && isInApp) {
            return new Promise((resolve) => {
                showToast(content);
                setTimeout(resolve, 2000);
            });
        } else {
            return addNotice('info', content, duration, onClose);
        }
    },
    destroy(): void {
        if (singleNotification) {
            singleNotification.destroy();
        }
    },
};
