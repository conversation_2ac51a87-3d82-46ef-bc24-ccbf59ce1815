import React, { Component } from "react";
import { render } from "react-dom";
import Notice, { INotice } from "./notice";

export interface NotificationState {
  notice: AddNotice;
}

interface AddNotice {
  type: string;
  content: string;
  duration: number;
  onClose?: () => void;
}

class Notification extends Component<any, NotificationState> {
  constructor(props) {
    super(props);
    this.state = { notice: {} as AddNotice };
    this.removeNotice = this.removeNotice.bind(this);
  }

  noticeTimer;

  addNotice(notice: AddNotice): Promise<any> {
    return new Promise((resolve) => {
      this.setState({ notice });
      let { duration } = notice;

      clearInterval(this.noticeTimer);
      if (duration > 0) {
        this.noticeTimer = setTimeout(() => {
          this.removeNotice();
          resolve();
        }, duration);
      } else {
        resolve();
      }
    });
  }

  removeNotice() {
    const { notice } = this.state;
    if (notice && notice.onClose) {
      try {
        notice.onClose();
      } catch (error) {
        console.error(error);
      }
    }
    this.setState({ notice: {} as AddNotice });
  }

  render() {
    const { notice } = this.state;
    return isFullObejct(notice) && <Notice {...(notice as INotice)}></Notice>;
  }
}

function isFullObejct(val: object) {
  return typeof val === "object" && Object.keys(val).length > 0;
}

function createNotification() {
  const noticeWrapNode = document.createElement("div");
  document.body.appendChild(noticeWrapNode);

  let notificationRef: any;
  render(<Notification ref={(el) => (notificationRef = el)} />, noticeWrapNode);

  return {
    addNotice(notice) {
      return notificationRef.addNotice(notice);
    },
    destroy() {
      return notificationRef.removeNotice();
    },
  };
}

export default createNotification();
