import React, { useState, useCallback, useEffect } from 'react';
import ConfirmModal from 'components/modal/confirm';
import cx from 'classnames';
import { sendlogV3 as sendLog, getUserInfo } from "@src/common/bridge";
import { NoviceShopTagProps, UserInfo } from "./types";
import { genSchema } from "@lib/util/merch_picking";
import { isAndroid } from '@src/lib/env_detect';
import './index.scss';

const COMMON_PARAMS = {
    EVENT_ORIGIN_FEATURE: 'TEMAI',
    label_for: '新手店铺',
};

const NoviceShopTag: React.FC<NoviceShopTagProps> = props => {
    const { shopInfo = [], tag, className, logParams } = props;
    const [visible, setVisible] = useState(false);
    const [uid, setUid] = useState('');
    const openNoviceShopModal = useCallback(() => {
        setVisible(true);
        sendLog({
            eventName: 'click_button',
            params: {
                ...COMMON_PARAMS,
                ...logParams,
                author_id: uid,
                button_for: '新手店铺'
            }
        });
    }, [uid, status, logParams]);
    const closeNoviceShopModal = useCallback(() => setVisible(false), []);
    const jumpRulePage = useCallback((link: string) => {
        setVisible(false);
        location.href = genSchema({
            url: link,
            use_ui: 1,
            hide_more: 1,
            show_more_button: 1,
            copy_link_action: 0
        })
    }, []);
    useEffect(() => {
        const { page_name: pageName, commodity_id: productId } = logParams;
        if (pageName && productId) {
            getUserInfo().then((res: UserInfo) => {
                const {code, user_id: uid} = res;
                if (code === 1) {
                    setUid(uid);
                    sendLog({
                        eventName: 'show_button',
                        params: {
                            ...COMMON_PARAMS,
                            ...logParams,
                            author_id: uid
                        }
                    });
                }
            });
        }
    }, [logParams]);
    return (
        <>
            <div className={cx('novice-shop-tag', className)} onClick={openNoviceShopModal}>
                {tag}
                <span className="novice-shop-tag__icon"/>
            </div>
            <ConfirmModal
                isOpen={visible}
                onConfirm={closeNoviceShopModal}
                confirmText="知道了"
                className="novice-shop-modal"
                body={
                    <span className="novice-shop-modal__body">
                        {shopInfo.map(({ link, text }) => {
                            return link ? (
                                <span
                                    className={cx('novice-shop-modal__body-link', { 'in-android': isAndroid })}
                                    onClick={() => jumpRulePage(link)}
                                >
                                    {text}
                                </span>
                            ) : (
                                text
                            );
                        })}
                    </span>
                }
            />
        </>
    );
};

NoviceShopTag.displayName = 'NoviceShopTag';
export default NoviceShopTag;
