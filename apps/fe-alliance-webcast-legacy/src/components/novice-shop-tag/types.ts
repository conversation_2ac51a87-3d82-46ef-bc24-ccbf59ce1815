export interface NoviceShopTagParams {
    tab_name?: string;
    commodity_id?: string;
    page_name?: string;
}

export interface NoviceShopTagProps {
    shopInfo: PromotionShopLabel['shop_info'];
    tag: string;
    className?: string;
    logParams: NoviceShopTagParams;
}

export interface UserInfo {
    code: number;
    user_id: string;
    sec_user_id: string;
}

export enum EShopLabelStatus {
    RISK = 1, // 有风险
    LIMITED = 2 // 已限制
}

// 商家新手店铺标志
export interface PromotionShopLabel {
    tag: string;
    status: EShopLabelStatus;
    shop_info: Array<{
        text: string;
        link?: string;
    }>;
}
