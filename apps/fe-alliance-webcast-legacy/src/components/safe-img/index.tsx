import React, { useCallback, useState } from 'react';
import './index.scss';

interface Iprops {
  url: string;
  height: number | string | undefined;
  width?: number | string | undefined;
}

export default (props: Iprops) => {
  const { url, height = '', width = '' } = props;
  const [hasError, setHasError] = useState(false);

  const handleOnError = useCallback(() => {
    setHasError(true);
  }, []);

  return url && !hasError ? (
    <img src={url} style={{ height: height, width: width }} alt="" className="safe-img" onError={handleOnError} />
  ) : null;
};
