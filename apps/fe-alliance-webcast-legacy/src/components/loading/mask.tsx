import React from 'react';
import Modal from '../modal';

import './index';

interface Props extends ReactModal.Props {
    content?: React.ReactNode;
}

export default React.memo((props: Props) => {
    const { content, ...modalProps } = props;
    return (
        <Modal overlayClassName="loading-mask__overlay" className="loading-mask" {...modalProps}>
            <span className="loading-mask__spinner" />
            {content ? <p className="loading-mask__content">{content}</p> : null}
        </Modal>
    );
});
