import './index.scss';

let notification: HTMLDivElement;

const createNotification = () => {
    const el = document.createElement('div');
    el.classList.add('toast');
    document.body.appendChild(el);
    return el;
};

let timerId: number | null;
const hiddenClass = 'toast--hidden';

export default function toast(content: string, duration: number = 2000) {
    // single instance
    if (!notification) {
        notification = createNotification();
    }

    notification.classList.remove(hiddenClass);
    notification.innerHTML = '';
    const toastInnerEl = document.createElement('span');
    toastInnerEl.innerText = content;
    notification.appendChild(toastInnerEl);

    if (timerId) {
        window.clearTimeout(timerId);
    }

    timerId = window.setTimeout(() => {
        notification.classList.add(hiddenClass);
        timerId = null;
    }, duration);
}

export function hideToast() {
    if (timerId) {
        window.clearTimeout(timerId);
        timerId = null;
    }
    if (notification) {
        notification.classList.add(hiddenClass);
    }
}