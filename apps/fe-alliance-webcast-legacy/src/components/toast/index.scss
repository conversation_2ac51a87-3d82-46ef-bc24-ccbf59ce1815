.toast {
    position: fixed;
    display: flex;
    align-items: center;
    top: 50%;
    left: 50%;
    z-index: 1000;
    padding: 12px 16px;
    max-width: 256px;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    text-align: center;
    color: rgba(255, 255, 255, 1);
    background: rgba(0, 0, 0, 0.75);
    border-radius: 6px;
    transform: translate(-50%, -50%);
    &--hidden {
        display: none;
    }
}

.douyin .toast {
    border-radius: 2px;
    font-size: 13px;
    padding: 12px 20px;
    background: rgba(0, 0, 0, 0.85);
}
