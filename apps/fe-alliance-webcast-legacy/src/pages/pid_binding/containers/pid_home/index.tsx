import React, { useState, useEffect, useCallback, FunctionComponent } from 'react'
import Navbar from '@src/components/nav_bar'
import IconArrowRightC161823 from '@src/components/Icon/IconArrowRightC161823'
import { openSchema } from '@src/common/bridge'
import { isAndroid, isInToutiao, isInXigua } from '@src/lib/env_detect'
import { fetchPidState } from '../../api'
import { PID_TYPE, BIND_PLATFORM } from '../../constants'
import { genSchema, CommonConfig } from '../../lib/schema'
import './style.scss'

const PLATFORMS = [
  {
    icon: <img src={require('static/images/pid_binding/taobao_icon.png')} />,
    name: '淘宝',
    pidType: PID_TYPE.TAOBAO,
    bindPlatform: BIND_PLATFORM.TAOBAO
  },
  {
    icon: <img src={require('static/images/pid_binding/jd_icon.png')} />,
    name: '京东',
    pidType: PID_TYPE.JD,
    bindPlatform: BIND_PLATFORM.JD
  },
  {
    icon: <img src={require('static/images/pid_binding/ymt_icon.png')} />,
    name: '洋码头',
    pidType: PID_TYPE.YMT,
    bindPlatform: BIND_PLATFORM.YMT
  },
  {
    icon: <img src={require('static/images/pid_binding/vip_icon.png')} />,
    name: '唯品会',
    pidType: PID_TYPE.VIP,
    bindPlatform: BIND_PLATFORM.VIP
  },
  {
    icon: <img src={require('static/images/pid_binding/sn_icon.png')} />,
    name: '苏宁',
    pidType: PID_TYPE.SN,
    bindPlatform: BIND_PLATFORM.SN
  }
]

const getPlatfoems = () => {
  if (isInToutiao || isInXigua) {
    return PLATFORMS.slice(0, 2)
  }
  return PLATFORMS
}

type IPlatform = typeof PLATFORMS[0]

type ItemProps = IPlatform & {
  isLast: boolean
  onNavigate?: (type: PID_TYPE) => void
}

const Item: FunctionComponent<ItemProps> = p => {
  const { pidType, bindPlatform } = p

  const [isFetching, setIsFetching] = useState(true)
  const [isBind, setIsBind] = useState(false)
  const fetch = useCallback(() => {
    setIsFetching(true)
    fetchPidState({
      pid_type: pidType,
      bind_platform: bindPlatform
    }).then(res => {
      setIsBind(res.isBind)
      setIsFetching(false)
    })
  }, [
    pidType, bindPlatform
  ])

  useEffect(() => {
    fetch()
    const onVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        fetch()
      }
    }
    document.addEventListener('visibilitychange', onVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', onVisibilityChange)
    }
  }, [fetch])

  const openBindPage = useCallback(() => {
    const url = isBind ?
      `https://lm.jinritemai.com/views/pid_bind_new/home?platform=${pidType}` :
      `https://lm.jinritemai.com/views/pid_bind_new/pid_edit?platform=${pidType}`
    
      openSchema({
        schema: genSchema({ url, ...CommonConfig })
      }) 
  }, [pidType, isBind])

  return (
    <div
      className={`item ${!p.isLast ? 'border-bottom': ''}`}
      key={pidType}
      onClick={openBindPage}
    >
      <div className="icon">{p.icon}</div>
      <div className="name">{`${p.name}PID`}</div>
      {
        !isFetching ? isBind ? (
          <div className="status">已绑定</div>
        ) : (
          <div className="status unbind">未绑定</div>
        ): null
      }
      <IconArrowRightC161823 size={14} style={isAndroid ? {
        position: 'relative',
        top: -1,
      } : {}} />
    </div>
  )
}

export default () => {
  const openDditSchema = () => {
    const schema = genSchema({
      url: 'https://lm.jinritemai.com/views/pid_bind_new/pid_record',
      ...CommonConfig
    })
    openSchema({ schema })
  }
  const platforms = getPlatfoems()
  return (
    <div className="pid-binding-home">
      <Navbar
        title={
          <span style={
            isAndroid ?
              { fontWeight: 'bold' } :
              { fontWeight: 500 }
          }>账号绑定</span>
        }
        rightIcons={
          <div onClick={openDditSchema} style={{ fontSize: 15 }}>PID记录</div>
        }
      />
      <div className="pid-binding-home-content">
        <div className="enter-list border-bottom">
        {platforms.map((p, index) => (
          <Item
            key={p.pidType}
            isLast={index === platforms.length - 1}
            {...p} 
          />
        ))}
        </div>
        <div className="rules">
          <div className="title" style={
            isAndroid ?
              { fontWeight: 'bold' } :
              { fontWeight: 500 }
          }>【PID绑定规则】</div>
          <div className="rule">
            <div className="index">1.</div>
            每个平台24小时可修改一次PID，请谨慎修改；变更PID将影响您推广所得佣金的收款账户，变更后新添加的商品推广所产生的佣金将计算到新的PID账户下
          </div>
          <div className="rule">
            <div className="index">2.</div>
            您的账号绑定上述平台PID后，您推广上述平台的商品产生的订单和佣金将不会在本平台订单明细中展示；需到上述平台的联盟服务中查看订单明细和进行收入提现
          </div>
        </div>
      </div>
    </div>
  )
}