.pid-binding-home {
  height: 100%;

  &-content {
    height: 100%;
    background-color: #F8F8F8;

    .enter-list {
      background-color: #fff;
      padding: 0 16px;

      .item {
        display: flex;
        align-items: center;
        height: 60px;

        .icon {
          font-size: 0;

          img {
            width: 28px;
            height: 28px;
          }
        }

        .name {
          padding-left: 10px;
          flex: 1;
          color: #182613;
          font-size: 15px;
        }

        .status {
          color: rgba(22, 24, 35, 0.5);
          font-size: 14px;
          margin-right: 2px;

          &.unbind {
            color: #FE2C55;
          }
        }
      }
    }

    .rules {
      margin-top: 16px;
      padding: 16px;
      line-height: 18px;
      font-size: 13px;
      color: rgba(22, 24, 35, 0.34);

      .rule {
        position: relative;
        padding-left: 14px;
        margin-top: 8px;

        .index {
          position: absolute;
          top: 0;
          left: 0;
        }
      }
    }
  }
}