import qs from 'qs'
import { isInAweme } from '@src/lib/env_detect'

export enum SchemaBoolean {
  YES = 1,
  NO = 0
}

export enum SwipeMode {
  DISABLED = 0,
  EDGE = 2
}


export interface SchemaConfig {
  url: string;
  title?: string;
  // 隐藏导航栏
  hide_bar?: SchemaBoolean;
  // 隐藏右上角的关闭按钮
  hide_close_btn?: SchemaBoolean;
  // 隐藏返回按钮
  hide_back_button?: SchemaBoolean;
  hide_back_close?: SchemaBoolean;
  // 是否隐藏状态栏
  hide_status_bar?: SchemaBoolean;
  // 边缘关闭功能
  swipe_mode?: SwipeMode;
  // 状态栏颜色
  status_bar_color?: string;
  // 状态栏文字颜色
  status_font_dark?: SchemaBoolean;
  // 导航栏颜色
  nav_bar_color?: string;
  // 导航栏文字颜色
  title_color?: string;
  // 页面加载默认背景色
  loading_bgcolor?: string;
  // 任意额外参数
  [extraParam: string]: any;
}

type Params = Record<string, number | string | boolean>;
function appendQuery(url: string, params: Params, override = true) {
  // 把params追加到url中作为参数
  const hash = (url.match(/#(.*)$/) || [''])[0];
  const matches = url.match(/^([^?#]*)\??([^#]*)/);
  if (!matches) return url
  const [_, mainURL, _query] = matches
  const query = qs.parse(_query);
  Object.assign(query, params, override ? null : query);
  const _qs = qs.stringify(query);
  return `${mainURL}${_qs ? '?' + _qs : ''}${hash}`;
}

export const CommonConfig = {
  status_bar_color: 'ffffff',
  status_font_dark: SchemaBoolean.YES,
  hide_bar: SchemaBoolean.YES,
  hide_nav_bar: SchemaBoolean.YES,
  show_more_button: SchemaBoolean.NO,
  copy_link_action: SchemaBoolean.NO,
  loading_bgcolor: 'ffffff',
  enable_three_dots: SchemaBoolean.NO,
  bounce_disable: SchemaBoolean.YES,
  showMore: false
}

export const genSchema = (config: SchemaConfig) => {
  const {
    url,
    title,
    hide_bar,
    hide_back_button = hide_bar,
    hide_back_close = hide_bar,
    hide_close_btn = 1,
    hide_status_bar,
    status_bar_color,
    status_font_dark,
    bounce_disable,
    nav_bar_color,
    title_color,
    loading_bgcolor,
    swipe_mode = SwipeMode.EDGE,
    ...params
  } = config
  const webviewParams: SchemaConfig = {
    url: appendQuery(url, params),
    title,
    hide_bar,
    hide_status_bar,
    status_bar_color,
    status_font_dark,
    hide_back_button,
    bounce_disable,
    hide_back_close,
    hide_close_btn,
    nav_bar_color,
    title_color,
    loading_bgcolor,
    swipe_mode,
    ...params
  }
  const schemaProtocol = isInAweme ? 'aweme:' : 'sslocal:'
  return `${schemaProtocol}//webview?${qs.stringify(webviewParams)}`;
}