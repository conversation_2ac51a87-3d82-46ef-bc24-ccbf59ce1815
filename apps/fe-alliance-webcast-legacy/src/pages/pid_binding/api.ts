import { mFetch, ApiWrapper } from '@src/lib/request';
import { isIOS, isAndroid } from '@src/lib/env_detect';
import { PID_TYPE, BIND_PLATFORM } from './constants';
import toast from '@src/components/toast';

export const L_API_BASE = 'https://lianmeng.snssdk.com';

export interface BindInfo {
  sub_pid: string;
  default_pid?: string;
  app_url?: string;
  anonymous_id?: string;
  is_lock: 0 | 1;
  callback_code?: number;
  callback_msg?: string;
  institution_info?: any;
}

export interface FetchPidStateReq {
  pid_type: PID_TYPE;
  bind_platform: BIND_PLATFORM;
}
export interface FetchPidStateResp {
  isBind: boolean;
}
export const fetchPidState = async (data: FetchPidStateReq): Promise<FetchPidStateResp> => {
  const url = `${L_API_BASE}/user/subpid/getUserPidInfo`;
  let os = 'UNKNOWN';
  if (isIOS) {
    os = 'iOS';
  } else if (isAndroid) {
    os = 'android';
  }
  const params = {
    ...data,
    os,
  };
  try {
    const res = await mFetch(url, { params });
    return {
      isBind: res && res.data && Boolean(res.data.sub_pid),
    };
  } catch (e: unknown) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    toast(e?.msg || e?.status_msg || '请求失败，请稍后重试');
    return { isBind: false };
  }
};
