<!DOCTYPE html>
<html lang="en">

<head>
    <script>window.__remew_performance_data = { T_HTML_START: new Date().getTime() };</script>
    {% block dnsPreFetch %}
        <link rel="dns-prefetch" href="//lf1-fe.ecombdstatic.com/obj/cdn-static-resource/">
    {% endblock %}

    {% block metaInHead %}
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no">
        <meta http-equiv="X-UA-Compatible" content="IE=Edge;chrome=1">
        <meta http-equiv="cache-control" content="no-cache">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="default">
        <meta name="screen-orientation" content="portrait">
        <meta name="format-detection" content="telephone=no">
        <meta name="x5-orientation" content="portrait">
    {% endblock %}

    <title>{% block title %}{% endblock %}</title>
    {% block slardar %}{% endblock %}
    {% block inlineStyleInHead %}
        <style>
            {% include paths.staticDir + '/css/normalize.css' %}
        </style>
    {% endblock %}

    <script>
        try {
            var isSimulatorMode = window.location.href.indexOf('__h5__simulator__') > -1;
            var isInIframe = window.self !== window.top;

            if (isSimulatorMode && isInIframe) {
                var script = document.createElement("SCRIPT");

                script.src = '//lf3-static.bytednsdoc.com/obj/eden-cn/lm-ljuhpssh/ljhwZthlaukjlkulzlp/mobile-simulator/runtime.js';
                script.crossOrigin = "anonymous";

                document.head.append(script);
            }
        } catch (error) {}
    </script>
    <script>!function(r,n){var t="error",e="unhandledrejection",i="iesJsBridgeTransferMonitor",o="getInfo";if(!r[n]&&r[i]){function u(r){var n=r.indexOf("-");return(-1<n?r.slice(0,n):r).split(".")}try{if(r[i][o]){var f=r[i][o](),c="string"==typeof f?JSON.parse(f):f;if(!c||!c.sdk_version)return;if(!function(r,n){for(var t=u(r),e=u(n),i=0;i<t.length;i++)if(+t[i]!=+e[i])return+t[i]>+e[i];return null}(c.sdk_version,"1.5.7"))return}}catch(r){return}function a(){v.push(arguments)}var v=[],d=a;Object.defineProperty(r,n,{set:function(r){!d.r&&r&&r.r&&(d=r,setTimeout(function(){g(t,s),g(e,s);for(var r=0;r<v.length;r++)try{d.apply(void 0,v[r])}catch(r){}v=[]},2))},get:function(){return d}});var s=function(r){r=r.error||r.reason||r.detail&&r.detail.reason;r&&a("captureException",r)},n=r.addEventListener,g=r.removeEventListener;n(t,s),n(e,s)}}(window,"SlardarHybrid");</script>
    {% block scriptInHead %}{# script 形式的引入 #}{% endblock %}
    {% block inlineJsInHead %}
        {% if env.NODE_ENV !== 'production' %}
            <script src="//sf-unpkg-src.bytedance.net/vconsole@latest/dist/vconsole.min.js" />
            <script>
                var vConsole = new VConsole();
            </script>
        {% endif %}
        <script>
            {# vw单位兼容 #}
            {% include paths.appNodeModules + '/@ies/viewport_units_polyfill/src/index.js' %}
        </script>
    {% endblock %}

    {% block styleInHead %}{# link 形式的引入 #}{% endblock %}
    <script>
      window.CHUANG_ZUO_ZHE_BID = 'ecommerce_webcast'
    </script>
</head>

<body>
    <div id="root"></div>
    {% if env.NODE_ENV === 'production' %}
         {% if (branchName !== 'master') and branchName.indexOf('release_train') !== 0 %}
            <style>
                .top-tips{
                    position: absolute;
                    bottom: 0;
                    font-size: 12px;
                    right: 30px;
                    z-index: 100;
                    padding: 2px;
                    background-color: rgba(133,0,0,0.6);
                    color: #fff;
                }
            </style>
            <div class="top-tips">{{scmName}}</div>
        {% endif %}
    {% endif %}
    {% block scriptInBody %}{% endblock %}

</body>

</html>
