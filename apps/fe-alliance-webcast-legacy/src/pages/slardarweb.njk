{% macro marco_sw(pid, sample_rate, releaseVersion = '') %}
<script>
  ;(function (w, d, u, b, n, pc, ga, ae, po, s, p, e, t, pp) {pc = 'precollect';ga = 'getAttribute';ae = 'addEventListener';po = 'PerformanceObserver';s = function (m) {p = [].slice.call(arguments);p.push(Date.now(), location.href);(m == pc ? s.p.a : s.q).push(p)};s.q = [];s.p = { a: [] };w[n] = s;e = document.createElement('script');e.src = u + '?bid=' + b + '&globalName=' + n;e.crossOrigin = u.indexOf('sdk-web') > 0 ? 'anonymous' : 'use-credentials';d.getElementsByTagName('head')[0].appendChild(e);if (ae in w) {s.pcErr = function (e) {e = e || w.event;t = e.target || e.srcElement;if (t instanceof Element || t instanceof HTMLElement) {if (t[ga]('integrity')) {w[n](pc, 'sri', t[ga]('href') || t[ga]('src'))} else {w[n](pc, 'st', { tagName: t.tagName, url: t[ga]('href') || t[ga]('src') })}} else {w[n](pc, 'err', e.error|| e.message)}};s.pcRej = function (e) {e = e || w.event;w[n](pc, 'err',e.reason || (e.detail && e.detail.reason))};w[ae]('error', s.pcErr, true );w[ae]('unhandledrejection', s.pcRej, true);};if('PerformanceLongTaskTiming' in w) {pp = s.pp = { entries: [] };pp.observer = new PerformanceObserver(function (l) {pp.entries = pp.entries.concat(l.getEntries())});pp.observer.observe({ entryTypes: ['longtask', 'largest-contentful-paint','layout-shift'] })}})(window,document,'https://lf3-short.ibytedapm.com/slardar/fe/sdk-web/browser.cn.js','ecommerce_webcast','SlardarWeb');
  (function () {
    var qs = (function(a) {
      if (a == "") return {};
      var b = {};
      for (var i = 0; i < a.length; ++i)
      {
        var p=a[i].split('=', 2);
        if (p.length == 1)
          b[p[0]] = "";
        else
          b[p[0]] = decodeURIComponent(p[1].replace(/\+/g, " "));
      }
      return b;
    })(window.location.search.substr(1).split('&'));
    //- 获取页面路由
    function getCurrentPath() {
        if (!window) {
          return '';
        }
        var path = (window.location.hash ? window.location.hash.slice(1) : window.location.pathname).split('?')[0].replace(/\/\d+$/, '');
        return path;
    };
    window.SlardarWeb('init', {
      bid: 'ecommerce_webcast',
      pid: '{{ pid }}' || getCurrentPath(),
      userId: qs['user_id'] || (void 0),
      sample: {
        sample_rate: {{ sample_rate }}, // 总采样率
      },
      release: '{{ releaseVersion }}',
      plugins: {
        resourceError: {
          // 忽略特定资源错误
          ignoreUrls: [/\.google-analytics\.com\//],
        },
        blankScreen: {
          rootSelector: '#root', // 开始计算得分的根元素选择器， 默认从body开始，针对检测部分组件白屏设置此参数
          autoDetect: true, // 是否自动检测
          threshold: 1, // 得分阈值，小于阈值则判断为白屏
          screenshot: true, // 是否在判断为白屏的时候截屏上报
        }
      },
    });
    window.SlardarWeb('on','beforeSend',function(e) {
      // - console.log('```',e);
      return e;
      })
    window.SlardarWeb('start');
  })();
</script>
{% endmacro %}
