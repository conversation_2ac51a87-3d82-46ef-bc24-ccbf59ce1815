import React, { useRef, useState, useEffect, useMemo, useCallback } from 'react';
import PageHeader from './components/page_header';
import HeaderSelection from './components/header_selection';
import webcast from '@bridge/webcast';
import via from '@ies/webcast-via';
import { getQuery, isInAweme } from '@src/lib/env_detect';
import { isLiving } from '@src/lib/util/merch_picking';
import HeaderFilter from '../merch_picking/components/header-filter';
import MerchSkeleton from '../merch_picking/components/merch/skeleton';
import MerchItem from '../merch_picking/components/merch_item';
import SelectAll from './components/select_all';
import { shopWindowProductListEmptyView } from '../merch_picking/containers/portal_main/emptyview';
import createList, { ListRef } from 'components/list';
import { useAppendedList } from '../merch_picking/modules/append_list';
import { Merch, TOTAL_MAX_SELECT_COUNT, MAX_SELECT_COUNT } from '../merch_picking/types';
import { genLiveTabDetailFetcher, liveBatchBind } from '../merch_picking/services/api';
import { myCoreLinkClient } from '@src/lib/util/mera/core-link';
import { canSelect, isAppended as getIsAppended } from '../merch_picking/components/merch/helper';
import showToast, { hideToast } from 'components/toast/ecom_toast';
import {
  defaultTab,
  CLOSE_MERCH_PAGE_DELAY_TIME,
  COMMON_STAT_PARAMS,
  DEFAULT_BATCH_RESULT,
  DEFAULT_SINGLE_RESULT,
} from './constant';
import { RouteQuery, batchResultType, LiveBindOperatePromotionParams, singleAddResult } from './constant/types';
import {
  computeNeedInvoiceShow,
  InvoiceDrawerShow,
  makeNoticeItems,
} from '@src/pages/merch_picking/components/invoice_notice_modal/invoiceNoticeDrawer';
import { PublishContinuePushStream } from '@src/lib/util/event';
import { onClickJumpToMerchPickingPage } from './utils';
import { sendLog, userActionRecord } from '../merch_picking/services/utils';
import CommonAlert from './components/common-alert';
import RecommendAddResultModal from './components/recommendAddResultModal';
import SingleAddResultModal from './components/singleAddResultModal';
import { UseJumpMerchPromoting } from '@alliance-mobile/platform-merchant-common';
import { getPriceLevel } from '@alliance-mobile/platform-merchant-common/src/utils/price-level';

const query = getQuery<RouteQuery>();
const List = createList<Merch>();

// 注：推荐页面无同款替换逻辑&商达联盟权限豁免申请交互
const App = () => {
  // 当前已选列表
  const [selectedList, setSelectedList] = useState<Merch[]>([]);
  // 批量添加校验弹窗控制信息
  const [batchResult, setBatchResult] = useState<batchResultType>(DEFAULT_BATCH_RESULT);
  // 单个商品添加校验弹窗控制信息
  const [addResult, setResult] = useState<singleAddResult>(DEFAULT_SINGLE_RESULT);
  const [commonLogs, setCommonLogs] = useState({});
  const [selectAll, setSelectAll] = useState(false);
  const [cardList, setCardList] = useState<Merch[]>([]);
  const [autoSelectAll, changeAutoSelectAll] = useState(true);
  const listCompRef = useRef<ListRef<Merch> | null>(null);
  const listEmptyRef = useRef<boolean | null>(null);
  const selectedListRef = useRef(selectedList);
  selectedListRef.current = selectedList;
  // 用户行为记录
  const userActionProps = userActionRecord.getUserActionMap();
  // 获取已添加过的商品
  const [pickedPromotionsListRef, appendMerchToAppendedList, removeMerchFromAppendedList, appendedListState] =
    useAppendedList();
  // 剩余最大可添品数
  const maxSelectCount = Math.min(Math.max(TOTAL_MAX_SELECT_COUNT - appendedListState.length, 0), MAX_SELECT_COUNT);
  // 不能选中更多商品
  const cantSelectMore = useMemo(() => selectedList.length >= maxSelectCount, [selectedList, maxSelectCount]);

  // 筛选出商品列表中的可选商品
  const getCanSelectList = (list: Merch[]) =>
    list?.filter(merch => canSelect(merch) && !getIsAppended(appendedListState, merch));

  const changeShowAddRecommend = () => {
    listCompRef.current && setCardList(listCompRef.current.listData || []);
  };

  useEffect(() => {
    setCommonLogs(p => ({
      ...p,
      pick_first_source: 'live',
      pick_second_source: 'feed',
      pick_third_source: query?.pick_third_source,
    }));
  }, []);

  // 跳转决策页
  const onMerchTitleClick = (merch: Merch, index: number) => {
    // 非抖音端禁止跳转
    if (!isInAweme) {
      return;
    }

    sendLog('enter_product_inner_detail', {
      ...userActionProps,
      product_id: merch.product_id,
      commodity_id: merch.promotion_id,
      commodity_location: index,
      recall_source: merch.recall_source,
      log_pb: merch.logPb,
      highprice_warning_show: merch?.price_good_same_style?.text_right?.text,
      pick_third_source: query?.pick_third_source,
    });
    // 直播中 跳转全屏页需要主动发送 continue_push_stream 事件，否则直播画面会黑屏
    // PublishContinuePushStream里做了新版本和直播中状态的判断
    PublishContinuePushStream();
    UseJumpMerchPromoting({
      hide_nav_bar: 1,
      nav_bar_color: 'ffffff',
      status_bar_color: 'ffffff',
      title_color: '161823',
      status_font_dark: 1,
      promotion_id: merch.promotion_id,
      loading_bgcolor: 'ffffff',
      bg_theme: 'ffffff',
      pick_first_source: 'live',
      pick_second_source: 'feed',
      pick_third_source: '移动添品页',
      pick_source_id: '移动添品页',
      page_name: '移动添品页',
    });
  };

  const handleFilterSubmit = (result: Record<string, unknown>) => {
    listCompRef.current?.setListFetchParams({ commonFilter: result });
    listCompRef.current?.fetchListData({ reset: true });
  };

  const sendRecommendLog = async (event: string, params?: Record<string, unknown>) => {
    const hasRoomId = query?.roomId && Number(query?.roomId);
    sendLog(event, {
      ...commonLogs,
      live_type: hasRoomId ? 'on' : 'off',
      platform: 'mobile',
      enter_from: hasRoomId ? '直播中添品' : '直播添品',
      ...params,
    });
  };

  const handleSelectChange = (merch, status) => {
    // 如果有商品取消选中，则取消全选状态
    if (!status) {
      setSelectAll(false);
    } else {
      myCoreLinkClient.sendCoreLinkEvent('live_add_promotion_checkbox_click');
    }

    // 超过最大可选数量则不让选中
    const limit = Math.min(TOTAL_MAX_SELECT_COUNT - pickedPromotionsListRef.current.length, MAX_SELECT_COUNT);
    const newSelectedList = selectedListRef.current;

    if (status && newSelectedList.length >= limit) {
      return;
    }
    const selectedIndex = newSelectedList.findIndex(item => item.promotion_id === merch.promotion_id);
    if (status) {
      if (!(selectedIndex > -1)) {
        // 如果已选列表里面没有，则直接添加
        newSelectedList.push(merch);
      }
    } else if (!status && selectedIndex > -1) {
      newSelectedList.splice(selectedIndex, 1);
    }

    setSelectedList([...newSelectedList]);
  };

  // 如果是全选状态，那么翻页的时候需要把新加载出来的内容加入选中
  const handleListUpdate = (list: Merch[]) => {
    if (!listEmptyRef.current) {
      listEmptyRef.current = Boolean(list?.length);
      if (listEmptyRef.current) {
        myCoreLinkClient.sendCoreLinkEvent('live_add_promotion_merchcart_tab_notempty');
      }
    }

    if (listCompRef.current) {
      listCompRef.current.listData = list;
    }

    if (!selectAll) {
      return;
    }
    const canSelectList = getCanSelectList(list);
    canSelectList.forEach(item => handleSelectChange(item, true));
  };

  // 当全选状态变更时，需要批量处理商品选中态
  const handleJumpToMerchPickingPage = () => {
    sendLog('xpgc_click', {
      ...commonLogs,
      page_name: 'recommend_product',
    });

    onClickJumpToMerchPickingPage();
  };

  // 当全选状态变更时，需要批量处理商品选中态
  const handleSelectAll = () => {
    const newStatus = !selectAll;
    setSelectAll(newStatus);

    if (!newStatus) {
      // 取消全选
      return setSelectedList([]);
    }

    const canSelectList = getCanSelectList(listCompRef.current?.listData || []);
    canSelectList.forEach(item => handleSelectChange(item, true));
  };

  // 添加商品绑定直播间
  const bindGoodsToLiveNew = useCallback(
    ({ promotion_id, product_id, ...rest }: LiveBindOperatePromotionParams, callback?: unknown) => {
      const baseParams = { promotion_id, product_id, ...rest };
      liveBatchBind([baseParams])
        .then(res => {
          if (isLiving) {
            via.business.refreshPromotions({ need_refresh: true });
          } else {
            const currentLength = pickedPromotionsListRef.current.length;
            const length = currentLength + 1;
            via.business.modifiedPromotions({
              promotion_num: length,
            });
          }

          webcast.app.publishEvent({
            eventName: 'ecom.anchor.refreshPickedView',
            params: {},
          });

          const { data } = res || {};
          const guideInfo = data && data.guide_info;
          const isGuideInfo = Boolean(guideInfo);
          const isToast = data?.is_toast;
          if (!isToast && guideInfo) {
            setResult({
              isShowResultModal: true,
              commonModal: guideInfo,
            });
          }
          if (isToast) {
            // 添加走新接口，toast内容取guideInfo?.content
            const toastContent = guideInfo?.content;
            showToast(toastContent || '添加或取消绑定商品到直播间失败');
          }
          if (callback) {
            callback(isGuideInfo || isToast, data && data.bind_msg);
          }
        })
        .catch(error => {
          const { data } = error;
          const guideInfo = data && data.guide_info;
          const isToast = data?.is_toast;
          if (isToast) {
            showToast(data?.bind_reason || '添加或取消绑定商品到直播间失败');
            return;
          }
          if (!isToast && guideInfo) {
            setResult({
              isShowResultModal: true,
              commonModal: guideInfo,
            });
            return;
          }
          showToast(error?.status_msg || '添加或取消绑定商品到直播间失败');
        });
    },
    []
  );

  const onBatchAdd = () => {
    if (!selectedList?.length) {
      return;
    }
    // 先定义一个添加商品的方法，它在下面会被用到
    const addMerch = () => {
      // 智能直播货盘仅取出 promotionId 即可
      if (query.pick_mode === 'id') {
        const pickedPromotionIds = selectedList.map(merch => merch.promotion_id);
        webcast.app.publishEvent(
          {
            eventName: 'submit_webcast_merchpicking',
            params: {
              pickedPromotionIds,
            },
          },
          () => {
            webcast.app.close();
          }
        );
        return;
      }

      const batchList = selectedList.map(merch => {
        return {
          promotion_id: merch.promotion_id,
          product_id: merch.product_id,
          item_type: merch.item_type,
          bind_source: merch.bind_source,
          tab_title: merch.tab_title, // 该参数后端不需要，只是埋点用
          tag_codes: merch.tag_codes,
        };
      });

      if (selectedList.length === 1) {
        // 防止上一个Toast未消失时，出现toast重叠的情况
        hideToast();

        const merch = selectedList[0];
        bindGoodsToLiveNew(batchList[0], (isGuideInfo: boolean, msg: string) => {
          if (isGuideInfo) {
            return;
          }
          sendLog('pick_product', {
            ...userActionProps,
            ...commonLogs,
            recommend_product_number: 1,
            select_all: selectAll,
            first_source: 'index',
            pick_second_source: merch.tab_title || '',
            commodity_id: merch.promotion_id || '',
            product_id: merch.product_id || '',
            commodity_type: merch.item_type,
            click_type: 'add',
            edit_type: 'add_product',
            page_name: 'live_select_product',
            price_level: getPriceLevel(merch.tag_codes || []),
          });

          appendMerchToAppendedList(merch.promotion_id);
          const toastMsg = '商品已添加到购物袋和橱窗';

          // 清空选中商品列表
          setSelectedList([]);

          setTimeout(() => {
            showToast(toastMsg || '添加成功');
          }, 300);
          // 添加成功后关闭当前添品页，回到前置页
          setTimeout(() => {
            webcast?.app?.close();
          }, CLOSE_MERCH_PAGE_DELAY_TIME);
        });

        return;
      }

      const sendPickProduct = (merch: typeof batchList[0]) => {
        sendLog('pick_product', {
          ...userActionProps,
          ...commonLogs,
          recommend_product_number: 1,
          select_all: selectAll,
          first_source: 'index',
          pick_second_source: merch.tab_title || '',
          commodity_id: merch.promotion_id || '',
          product_id: merch.product_id || '',
          commodity_type: merch.item_type,
          click_type: 'add',
          edit_type: 'add_product',
          page_name: 'live_select_product',
          price_level: getPriceLevel(merch.tag_codes || []),
        });
      };

      liveBatchBind(batchList)
        .then(res => {
          const { data } = res || {};
          const {
            success_count: successCount = 0,
            failure_count: failureCount = 0,
            failure_list: failureList = [],
            oversold_remaind_result: oversoldList = [],
            success_pmt_info: successList = [],
            is_toast: isToast,
            guide_info: guideInfo,
            partial_failure_count,
          } = data || {};

          // 将添加成功的商品推入appendedList中
          (successList || []).forEach(item => item.promotion_id && appendMerchToAppendedList(item.promotion_id));
          // 同步商品列表信息
          if (isLiving) {
            via.business.refreshPromotions({ need_refresh: true });
          } else {
            const currentLength = pickedPromotionsListRef.current.length;
            via.business.modifiedPromotions({ promotion_num: currentLength });
          }
          webcast.app.publishEvent({
            eventName: 'ecom.anchor.refreshPickedView',
            params: {},
          });
          // 清空选中商品列表
          setSelectedList([]);
          setSelectAll(false);

          const hasSuccess = Boolean(successList?.length || 0);
          const isBatchBindSuccess = failureCount === 0 && !partial_failure_count;
          if (!isBatchBindSuccess && hasSuccess) {
            // 如果有成功但没全部
            const successListInBatch = batchList.filter(item =>
              Boolean(successList?.find?.(si => si?.promotion_id === item?.promotion_id))
            );
            successListInBatch.forEach(merch => {
              sendPickProduct(merch);
            });
          }

          if (failureCount === 0) {
            // 如果全部商品都添加成功
            // 新版添品页 非链接和商品搜索场景 添加成功后关闭当前添品页 回到前置页
            showToast(`${successCount}个商品添加成功`);

            setTimeout(() => {
              webcast?.app?.close();
            }, CLOSE_MERCH_PAGE_DELAY_TIME);

            // 算法侧要求单个商品上报pick_product
            batchList.forEach(merch => {
              sendPickProduct(merch);
            });
            return;
          }

          // 如果判定为用toast显示添加错误信息，则直接弹toast
          if (isToast) {
            return showToast(guideInfo?.content || '添加或取消绑定商品到直播间失败');
          }

          if (guideInfo) {
            // 如果有guideInfo，且不用toast提示，则显示通用弹窗
            setResult({
              isShowResultModal: true,
              commonModal: guideInfo,
            });
            return;
          }

          // 如果有添加失败的商品，显示批量绑定结果信息弹窗
          setBatchResult({
            isShowResultModal: true,
            batchResultModal: {
              successCount,
              failureCount,
              failureList: failureList || [],
              oversoldList: oversoldList || [],
            },
          });
        })
        .catch(res => {
          showToast(res?.msg || '网络错误，请重试');
        });
    };
    // 计算是否存在需要弹窗的商品
    const invoiceNoticeList = makeNoticeItems(selectedList);
    // 当存在需弹窗商品且不在七天限制之内
    // 中断后续流程
    if (invoiceNoticeList.length !== 0 && computeNeedInvoiceShow()) {
      InvoiceDrawerShow({ noticeItems: invoiceNoticeList, confirmModal: addMerch });
      return;
    }
    addMerch();
  };

  const renderMerchItem = (data: Merch, index: number) => {
    const hasAppendedMerch = appendedListState.length > 0;
    const isAppended = hasAppendedMerch && Boolean(appendedListState.find(m => m === data.promotion_id));

    const isSelected = !isAppended && (selectedList || []).some(item => item.promotion_id === data.promotion_id);
    const cantSelect = !isSelected && cantSelectMore;

    return (
      <MerchItem
        key={data.promotion_id + index}
        isMulti
        data={data}
        index={index}
        isAppended={isAppended}
        isSelected={isSelected}
        cosFeeMode={false}
        cantSelect={cantSelect}
        needImpression
        isMerchV2
        showRecommend
        isShowSales
        onSelectChange={handleSelectChange}
        onMerchTitleClick={onMerchTitleClick}
      />
    );
  };

  useEffect(() => {
    via.app.getUserInfo().then(res => {
      setCommonLogs(p => ({
        ...p,
        ...COMMON_STAT_PARAMS,
        user_id: res.user_id,
        anchor_id: res?.user_id,
        room_id: query.room_id,
        live_status: !isLiving ? 'live_before' : 'live_on',
      }));
    });
  }, []);

  return (
    <div>
      <div className="page fcol f-fw-nw f-ai-s">
        <PageHeader
          title="添加商品"
          onNavBack={() => {
            webcast.app.close();
          }}>
          {/* 非抖音端禁止跳转 */}
          {isInAweme ? <HeaderSelection onLinkToggleClick={handleJumpToMerchPickingPage} /> : null}
        </PageHeader>
        <HeaderFilter onFilterSubmit={handleFilterSubmit} isMerchV2 sendRecommendLog={sendRecommendLog} />
        <List
          ref={listCompRef}
          ItemSkeleton={MerchSkeleton}
          className="merch-list"
          EmptyView={shopWindowProductListEmptyView}
          renderItem={renderMerchItem}
          onListUpdate={handleListUpdate}
          changeShowAddRecommend={changeShowAddRecommend}
          fetcher={genLiveTabDetailFetcher(defaultTab.type)}
        />
        <SelectAll
          autoSelectAll={autoSelectAll}
          selectAll={selectAll}
          maxSelectCount={maxSelectCount}
          selectedList={selectedList || []}
          listData={cardList}
          onBatchSelectAllChange={handleSelectAll}
          changeAutoSelectAll={changeAutoSelectAll}
          onBatchAdd={onBatchAdd}
        />
      </div>
      {/* 单个添加商品失败结果弹窗 */}
      <SingleAddResultModal addResult={addResult} setResult={setResult} />
      {/* 批量添加商品失败结果弹窗 */}
      <RecommendAddResultModal batchResult={batchResult} setBatchResult={setBatchResult} />
      {/* 前置校验弹窗 */}
      <CommonAlert />
    </div>
  );
};

export default App;
