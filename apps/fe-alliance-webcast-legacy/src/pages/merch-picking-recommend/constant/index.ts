export const defaultTab = { key: 'recommended_product', title: '推荐商品', isRendered: true, type: 21 };

// 关闭当前添品页的延时
export const CLOSE_MERCH_PAGE_DELAY_TIME = 2400;

export const COMMON_STAT_PARAMS = {
  EVENT_ORIGIN_FEATURE: 'TEMAI',
  page_name: 'shoppingWindow',
  data_type: 'commerce_data',
  pick_source_id: '',
};

export const DEFAULT_BATCH_RESULT = {
  isShowResultModal: false,
  batchResultModal: {
    successCount: 0,
    failureCount: 0,
    failureList: [],
    oversoldList: [],
  },
}

export const DEFAULT_SINGLE_RESULT = {
  isShowResultModal: false,
  commonModal: {},
}
