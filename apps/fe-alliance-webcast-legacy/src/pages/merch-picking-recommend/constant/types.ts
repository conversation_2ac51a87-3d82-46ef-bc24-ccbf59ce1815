import type { SchemaConfig } from '@alliance-mobile/constants';

export interface RouteQuery extends SchemaConfig {
  enter_from: 'live_control_operation' | 'webcast_living' | 'auto_live_picking';
  from_picked?: '1';
  /**
   * 直播间 或者 准备直播(roomId=0) id
   */
  roomId?: string | number;
  /**
   * pick_node 流程图见：https://bytedance.larkoffice.com/docx/MoxqdouGDolwlhxqLQxcLC7anEb#O02rdkVJgolDvPxXuclcsPO1nWd
   * room 读写直播间或准备直播中控
   * id 仅传入传出 id，无数据存储
   */
  pick_mode?: 'room' | 'id';
}

export interface GoodsOversoldRemaindResult {
  main_img: string; // 商品主图
  product_id: string; // 商品id
  title: string; // 商品名称
}

export interface LiveGuideInfo {
  title?: string;
  content?: string;
  confirm_text?: string; // 确认按钮 文本
  cancel_text?: string; // 取消按钮  文本
  guide_link?: string; // 引导页url
}

export interface BindResult {
  bind_status: number; // 绑定状态
  bind_msg?: string; // 绑定状态文案
  bind_reason?: string; // 绑定状态详情
  product_id: string; // 商品id
  title: string; // 商品名称
  guide_info?: LiveGuideInfo; // 弹窗
  is_toast?: boolean; // 是否是弹窗的形式
  main_img?: string; // 商品主图
  promotion_id?: string; // 推广id
}

export interface singleAddResult {
  isShowResultModal: boolean;
  commonModal: LiveGuideInfo;
}

export interface batchResultType {
  isShowResultModal: boolean;
  batchResultModal: {
    successCount: number;
    failureCount: number;
    failureList: BindResult[];
    oversoldList?: GoodsOversoldRemaindResult[];
  };
}

// 直播选品绑定api重构
export interface LiveBindOperatePromotionParams {
  promotion_id: string;
  product_id: string;
  item_type: number;
  bind_source?: number;
  third_source?: number;
}

export interface ModalProps {
  visible: boolean;
  guideInfo?: {
    title?: string;
    text?: string;
    confirm_text?: string;
    cancel_text?: string;
    can_del?: boolean;
    guide_link?: string;
  };
  onDismiss?: () => void; // 点击蒙层回调
  onCancel?: () => void; // 点击取消回调，如未传则兜底使用蒙层回调
  onConfirm?: () => void; // 点击确认回调
}

export enum SceneType {
  Square = 0, // 选品广场
  Media = 1, // 短视频
  Live = 2, // 直播
  Decision = 3, // 决策页
}

export enum ShowType {
  Tip = 0, // tip展示
  PopWindow = 1, // 弹窗提示
}

export enum ESelectRecommendSceneType {
  APPLiveControlPayAndSelection = 'APPLiveControlPayAndSelection',
  APPLiveControlPeerHot = 'APPLiveControlPeerHot',
  APPLiveControlFansLike = 'APPLiveControlFansLike',
  APPLiveControlCooperShop = 'APPLiveControlCooperShop',
  APPLiveAttract = 'APPLiveAttract',
}
