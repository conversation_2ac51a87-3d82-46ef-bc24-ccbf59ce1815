.select-all {
  display: flex;
  justify-content: space-between;
  flex-shrink: 0;
  padding: 20px 16px;
  padding-bottom: calc(20px + constant(safe-area-inset-bottom));
  padding-bottom: calc(20px + env(safe-area-inset-bottom));
  font-weight: 400;
  font-size: 13px;
  line-height: 16px;

  &-toggle {

    .rc-tooltip-content {
      margin-left: 12px;
      font-size: 14px;
      line-height: 20px;
    }
  }

  &-checkbox {
    display: inline-flex;
    align-items: center;

    &__text {
      margin-left: 12px;
      color: #161823;
    }
  }

  &-btn {
    display: inline-flex;
    align-items: center;
    color: rgba(22, 24, 35, .5);

    &__text {
      margin-right: 16px;

      &-highlight {
        margin-left: 2px;
        font-weight: 500;
        color: #fe2c55;
      }
    }

    &__add {
      font-weight: 500;
      padding: 10px 12px;
      min-width: 110px;
      border-radius: 8px;
    }
  }
}

.select-all-v2 {
  padding: 12px 16px;
  box-shadow: 0 -4px 8px 0 rgba(22, 24, 35, .04);
  // 因为没有加viewport-fit=cover，所以这个不生效，先在dom的style里设置安全paddingBottom了
  padding-bottom: calc(12px + constant(safe-area-inset-bottom));
  padding-bottom: calc(12px + env(safe-area-inset-bottom));
}
