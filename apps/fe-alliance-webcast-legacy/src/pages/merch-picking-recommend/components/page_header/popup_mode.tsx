import React from 'react';
import { getPageMode } from '@lib/util/scene';
import PageHeader, { Props } from 'components/page_header';
import cx from 'classnames';
import './index.scss';

export default React.memo((props: Props) => {
  const { title, onNavBack, children, ...rest } = props || {};
  const pageMode = getPageMode();
  return (
    <PageHeader
      className={`page-header--${pageMode || ''}`}
      title={title}
      onNavBack={onNavBack}
      backIcon={
        <span
          className={cx('page-header__back-icon', {
            [`page-header__back-icon--${pageMode}`]: pageMode,
          })}
        />
      }
      {...rest}>
      {children}
    </PageHeader>
  );
});
