import React from 'react';
import ModalConfirm from '../../../merch_picking/components/confirm';
import { PublishContinuePushStream } from '@src/lib/util/event';
import { singleAddResult } from '../../constant/types';
import { DEFAULT_SINGLE_RESULT } from '../../constant';
import { openSchema } from '@src/common/bridge';
import { genSchema } from '@src/lib/util/merch_picking';

interface IProps {
  addResult: singleAddResult;
  setResult: (addResult: singleAddResult) => void;
}

export default React.memo((props: IProps) => {
  const { addResult, setResult } = props;

  const onCancel = () => setResult(DEFAULT_SINGLE_RESULT);

  // 添加商品失败结果弹窗
  return (
    <div className="merch-picking__add-result-modal">
      <ModalConfirm
        title={addResult.commonModal?.title}
        content={addResult.commonModal?.content}
        confirmText={addResult.commonModal?.confirm_text}
        cancelText={addResult.commonModal?.cancel_text}
        onConfirm={() => {
          if (addResult.commonModal.guide_link) {
            // 直播中 跳转全屏页需要主动发送 continue_push_stream 事件，否则直播画面会黑屏
            // PublishContinuePushStream里做了新版本和直播中状态的判断
            PublishContinuePushStream();
            openSchema({
              schema: genSchema({
                url: addResult.commonModal.guide_link,
              }),
            });
          }

          onCancel()
        }}
        onCancel={onCancel}
        isOpen={addResult.isShowResultModal}
      />
    </div>
  );
});
