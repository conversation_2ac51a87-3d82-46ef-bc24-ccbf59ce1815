import React, { useEffect, useState } from 'react';
import { Dialog } from '@ecom/auxo-mobile';
import { ModalProps, SceneType, ShowType } from '../../constant/types';
import { genCheckModalProps } from '../../utils';
import { checkCommonAlert } from '../../../merch_picking/services/api';
import { hookDialog } from "@alliance-mobile/event";

hookDialog(Dialog);

export default React.memo(() => {
  const [checkModalProps, setCheckModalProps] = useState<ModalProps>({ visible: false });

  useEffect(() => {
    checkCommonAlert({ scene_type: SceneType.Live, show_type: ShowType.PopWindow }).then(res => {
      const { data } = res || {};
      data?.text &&
        setCheckModalProps(
          genCheckModalProps({
            visible: true,
            onDismiss: () => {
              setCheckModalProps({ visible: false });
            },
            guideInfo: data,
          })
        );
    });
  }, []);

  /* alert接口新拦截弹窗 */
  return <Dialog {...checkModalProps} />;
});
