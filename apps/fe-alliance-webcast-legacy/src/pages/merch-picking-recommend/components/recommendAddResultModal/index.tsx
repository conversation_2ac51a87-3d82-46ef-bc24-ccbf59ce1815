import React from 'react';
import AddResultModal from '../../../merch_picking/components/add_result_modal';
import { batchResultType } from '../../constant/types';
import { DEFAULT_BATCH_RESULT } from '../../constant';

interface IProps {
  batchResult: batchResultType;
  setBatchResult: (batchResult: batchResultType) => void;
}

export default React.memo((props: IProps) => {
  const { batchResult, setBatchResult } = props;

  /* 添加商品失败结果弹窗 */
  return (
    <div className="merch-picking__add-result-modal">
      <AddResultModal
        show={batchResult.isShowResultModal}
        info={batchResult.batchResultModal}
        setBatchResult={setBatchResult}
        onClose={() => {
          setBatchResult({
            ...batchResult,
            isShowResultModal: false,
          });
        }}
        isMerchV2
      />
    </div>
  );
});
