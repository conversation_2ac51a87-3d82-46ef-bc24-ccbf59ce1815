@import "~common/styles/merch_picking/mixins";

.rc-tooltip-placement-bottomRight {

  .rc-tooltip-content {

    .rc-tooltip-arrow {
      right: 12px;
      top: 3px;
      width: 12px;
      height: 6px;
      border-width: 0;
      border-color: transparent;
      background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAMCAYAAAB4MH11AAAAAXNSR0IArs4c6QAAAI5JREFUOBFjYKAxYCTBfC6gWlWo+ttA+hsxeom1wAtoWDYQgywBAZDhU4F4G4iDDzDjk4TKJQDpLCBmhfJBFIhtDcQgB14AYpyAkAUJQJ3xOHUzMBgA5fBags8CQobD7MVrCS4LiDWcoCXYLCDVcLyWoFtAruE4LUG2gFLDsVoCs4BahmNYArKA2oajWAIAoRsULoHWufoAAAAASUVORK5CYII=");
      background-size: contain;
    }
  }
}

.search-toggle {

  &--link {
    flex: 1;
    display: inline-block;
    // padding-top: 24px;
    // border-radius: 6px;
    color: #262626;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    // @include background($color: transparent, $size: 18px auto, $position: top 4px center);
  }

  &-v2 {
    display: flex;
    flex-direction: column;
    align-items: center;

    &--link {
      font-size: 11px;
      color: rgba(22, 24, 35, .80);
      line-height: 16px;

      &:not(:nth-last-child(1)) {
        margin-right: 12px;
      }
    }

    &-icon {
      width: 16px;
      height: 16px;
      margin-top: 2px;
      display: inline-block;
      background-size: cover;
      background-image: url(~static/images/merch_picking/link-icon.svg);
    }

    &-search-icon {
      background-image: url(~static/images/merch_picking/search-icon.svg);
    }

    &-merch-icon {
      background-image: url(~static/images/merch_picking/merch-icon.svg);
    }
  }

  &--skeleton#{&}--link {
    color: transparent;
    background-image: none;
  }

  &__overlay {
    width: 136px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(255, 255, 255, 1);
    line-height: 20px;

    .rc-tooltip-inner {
      padding: 12px;
      font-size: 14px;
      background: rgba(0, 0, 0, .75);
      border-radius: 6px;
    }
  }
}
