.render-tab-filter {
  position: relative;
  display: flex;
  align-items: center;
  overflow-x: auto;

  &-btn {
    border-radius: 8px;
    background-color: #f6f7fb;
    padding: 4px 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: rgba(22, 24, 35, .6);
    margin-right: 8px;
    border: .5px solid  transparent;
    transition: all .3s ease;
    transform: rotateZ(0);

    &.active {
      border-color: #ff3b52;
      background-color: rgba(255, 59, 82, .05);
      color: #ff3b52;
    }
  }
}
