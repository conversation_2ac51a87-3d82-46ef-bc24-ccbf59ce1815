import { QuickFilterInfo } from '@src/pages/merch_picking/services/api';
import './index.scss';
import React from 'react';
import cs from 'classnames';

interface IProps {
  filters?: QuickFilterInfo[];
  activeTabKey: string;
  onChange: (tabKey: string) => void;
  sendRecommendLog: (event: string, params?: Record<string, unknown>) => Promise<void>;
}

export const RenderTabFilter = (props: IProps) => {
  const { filters, activeTabKey, onChange, sendRecommendLog } = props;
  return (
    <div className="render-tab-filter">
      {filters?.map(item => {
        const tabValue = item?.values?.[0] || '';
        const isActive = (tabValue || '') === (activeTabKey || '');
        return (
          <div
            onClick={e => {
              e.stopPropagation();
              e.preventDefault();
              onChange?.(tabValue);
              sendRecommendLog?.('product_suggestion_model_click', {
                type: '添品页推荐tab',
                button_for: tabValue || '',
              });
            }}
            key={tabValue}
            className={cs('render-tab-filter-btn', {
              active: isActive,
            })}>
            {item.name}
          </div>
        );
      })}
    </div>
  );
};
