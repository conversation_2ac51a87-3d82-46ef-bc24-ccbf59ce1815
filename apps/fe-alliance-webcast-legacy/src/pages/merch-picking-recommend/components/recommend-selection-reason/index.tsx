import React from 'react';
import styles from './index.module.scss';
import { PmtCard4Kol } from '@src/pages/merch_picking/types';
import { ESelectRecommendSceneType } from '../../constant/types';
import { productCardAmount, secretAmount } from '@ecom/number-display';
import cx from 'classnames';

export const RecommendReasonWrapItem = (props: {
  item: PmtCard4Kol;
  /** 当前筛选scene的值 */
  itemScene?: string;
}) => {
  const { item, itemScene } = props;
  const { selection_reason } = item;
  // 粉丝喜爱,itemScene === ESelectRecommendSceneType.APPLiveControlFansLike
  {
    const reason102005002 = selection_reason?.find(i => i?.sub_rec_type === 102005002);
    const reason102005003 = selection_reason?.find(i => i?.sub_rec_type === 102005003);
    if (reason102005002) {
      const { extra } = reason102005002 || {};
      const fansPaySaleObj = productCardAmount(Number(extra?.fans_pay_sale || 0));
      return (
        <div className={styles.recommendReasonWrapItem}>
          粉丝购买
          {fansPaySaleObj.int}
          {fansPaySaleObj.dec && `.${fansPaySaleObj.dec}`}
          {fansPaySaleObj.unit}件
        </div>
      );
    }
    if (reason102005003) {
      const { extra } = reason102005003 || {};
      const fansBrowseSaleObj = productCardAmount(Number(extra?.fans_browse_sale || 0));
      return (
        <div className={styles.recommendReasonWrapItem}>
          {fansBrowseSaleObj.int}
          {fansBrowseSaleObj.dec && `.${fansBrowseSaleObj.dec}`}
          {fansBrowseSaleObj.unit}粉丝浏览过
        </div>
      );
    }
  }
  // 同行爆品,itemScene === ESelectRecommendSceneType.APPLiveControlPeerHot
  {
    const reason103003001 = selection_reason?.find(i => i?.sub_rec_type === 103003001);
    if (reason103003001) {
      const { extra } = reason103003001 || {};
      const hotSaleObj = secretAmount(Number(extra?.hot_sale || 0));
      return (
        <div className={styles.recommendReasonWrapItem}>
          来自达人{extra?.author_name || '-'}，热销{hotSaleObj.int}
          {hotSaleObj.dec && `.${hotSaleObj.dec}`}
          {hotSaleObj.unit}件
        </div>
      );
    }
  }
  // 买过带过,itemScene === ESelectRecommendSceneType.APPLiveControlPayAndSelection

  {
    const reason104003002 = selection_reason?.find(i => i?.sub_rec_type === 104003002);
    const reason104003001 = selection_reason?.find(i => i?.sub_rec_type === 104003001);
    if (reason104003001) {
      const { extra } = reason104003001 || {};
      const sevenDaysSaleObj = productCardAmount(Number(extra?.seven_days_sale || 0));
      return (
        <div className={styles.recommendReasonWrapItem}>
          近期有带货，近7日累计售出{sevenDaysSaleObj.int}
          {sevenDaysSaleObj.dec && `.${sevenDaysSaleObj.dec}`}
          {sevenDaysSaleObj.unit}件
        </div>
      );
    }
    if (reason104003002) {
      const { extra } = reason104003002 || {};
      const monthSaleObj = productCardAmount(Number(extra?.month_sale || 0));
      return (
        <div className={styles.recommendReasonWrapItem}>
          近期有购买，大盘月销高达{monthSaleObj.int}
          {monthSaleObj.dec && `.${monthSaleObj.dec}`}
          {monthSaleObj.unit}
        </div>
      );
    }
  }
  // 引流品,itemScene === ESelectRecommendSceneType.APPLiveAttract
  {
    const reason104004001 = selection_reason?.find(i => i?.sub_rec_type === 104004001);
    if (reason104004001) {
      const { reason } = reason104004001 || {};
      return <div className={styles.recommendReasonWrapItem}>直播引流{reason && `，${reason}`}</div>;
    }
  }
  // 平台推荐,itemScene === ESelectRecommendSceneType.APPLiveSelectFeed
  {
    const reason103003003 = selection_reason?.find(i => i?.sub_rec_type === 103003003);
    if (reason103003003) {
      const { extra } = reason103003003 || {};
      const authorCntObj = productCardAmount(Number(extra?.author_cnt || 0));
      return (
        <div className={styles.recommendReasonWrapItem}>
          {authorCntObj.int}
          {authorCntObj.dec && `.${authorCntObj.dec}`}
          {authorCntObj.unit}人带过
        </div>
      );
    }
  }
  // 合作店铺,itemScene === ESelectRecommendSceneType.APPLiveControlCooperShop
  {
    const reasonCooperation = selection_reason?.[0];
    if (reasonCooperation?.reason) {
      return <div className={styles.recommendReasonWrapItem}>{reasonCooperation?.reason}</div>;
    }
  }

  return (
    <>
      {selection_reason?.[0] ? (
        <div className={styles.recommendReasonWrapItem}>{selection_reason?.[0]?.reason}</div>
      ) : null}
    </>
  );
};
