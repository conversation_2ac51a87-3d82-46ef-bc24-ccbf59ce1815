import { genSchema } from '@src/lib/util/merch_picking';
import { openSchema } from '@src/common/bridge';
import { ModalProps } from '../constant/types';

export function genCheckModalProps(props: ModalProps) {
  const { visible = false, guideInfo, onDismiss, onCancel } = props || {};
  const { title, text, confirm_text, cancel_text = '取消', can_del, guide_link } = guideInfo || {};
  const cancelFn = onCancel ? onCancel : onDismiss;
  return {
    visible,
    title,
    renderBody: text,
    okText: confirm_text,
    cancelText: cancel_text,
    closable: can_del,
    onDismiss,
    onCancel: cancelFn,
    onConfirm: () => {
      if (guide_link) {
        openSchema({
          schema: genSchema({
            url: guide_link,
            hide_close_btn: 0,
            use_webview_title: 1,
          }),
        });
        cancelFn?.();
      }
    },
  };
}

export const onClickJumpToMerchPickingPage = () => {
  openSchema({
    schema: genSchema({
      url: `https://lf-ecom-gr-sourcecdn.bytegecko.com/obj/byte-gurd-source-gr/merchPicking/gecko/h5Resource/alliance_merch_picking_h5/cn/html/merch-picking/index.html?_pia_=1&mode=rich`,
      isWebCast: true,
      hide_nav_bar: 1,
      trans_status_bar: 1,
      status_bar_color: 'black',
      hide_loading: 1,
      web_bg_color: 'ffffff',
      loader_name: 'forest',
      disable_thread_spread: 1,
      disable_host_jsb_method: 1,
    }),
  });
};
