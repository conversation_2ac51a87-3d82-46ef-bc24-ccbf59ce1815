const path = require('path');
const fs = require('fs');

// Make sure any symlinks in the project folder are resolved:
// https://github.com/facebookincubator/create-react-app/issues/637
// 项目 root 文件夹
const appDirectory = fs.realpathSync(path.resolve(__dirname, '../..'));
const resolveApp = relativePath => path.resolve(appDirectory, relativePath);

const paths = {
  // appDirectory
  appDirectory,
  config: resolveApp('config'),
  // 环境变量
  dotenv: resolveApp('config/env'),
  // package.json
  appPackageJson: resolveApp('package.json'),
  // 源代码
  appSrc: resolveApp('src'),
  // 编译目标
  appBuild: resolveApp('output'),
  // 模版文件夹
  templateDir: resolveApp('src/pages'),
  // 静态资源文件夹
  staticDir: resolveApp('src/static'),
  // 组件文件夹
  componentsDir: resolveApp('src/components'),
  // node_modules
  appNodeModules: resolveApp('node_modules'),
  // 编译临时文件夹
  hiddenTempDir: resolveApp('.build_temp'),
};

module.exports = {
  env: process.env,
  paths,
  releaseVersion: process.env.BUILD_VERSION || '',
  branchName: process.env.BUILD_BRANCH,
  scmName: process.env.BUILD_REPO_PACKAGE_NAME,
};
