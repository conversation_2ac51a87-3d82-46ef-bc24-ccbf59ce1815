import { isInAweme, isInH<PERSON>shan, queryParamsFromGlobal as query } from '@src/lib/env_detect';
import { genSchema } from '@src/lib/util/merch_picking';
import { VendorEntry, VendorId, ItemType, LinkSearchGuideOptions } from '../types';

import { SchemaBoolean } from 'types/schema';

// 根据app版本判断是否进入编辑页面
export const isAbleEntherEditor = Number(query.custom) === 1;
// 商品搜索历史记录
export const SEARCH_HISTORY_LS_KEY = 'combo_search';

// 淘宝商品链接搜索，如果未绑定PID，服务端返回特定的 status_code，根据这个展示淘宝PID绑定的跳转组件
export const TAOBAO_UNAUTHED_STATUS_CODE = 10242;

// 用户是否有小店权限，只用于判断获取用户权限接口时是否展示Tab骨架，减少视觉跳变，优化体验
export const PORTAL_TAB_LIST_SKELETON_KEY = 'tablist_skeleton_flag';

// 轮播，骨架占位 减少视觉跳变，优化体验
export const BANNER_CAROUSEL_SKELETON_KEY = 'banner_carousel_skeleton_flag';

// 淘宝物料tab，骨架占位 减少视觉跳变，优化体验
export const TAOBAO_TAB_LIST_SKELETON_KEY = 'taobao_tablist_skeleton_flag';

// 搜索帮助外链
export const EXTERNAL_HELP_SCHEMA = genSchema({
  url: 'https://tbk.bbs.taobao.com/detail.html?postId=9179720',
  title: '关于内容场景推广内容商品库的通知',
});

// 搜索无结果提示图
export const SEARCH_EMPTY_IMG = 'https://sf3-cdn-tos.douyinstatic.com/obj/temai/FiFY2u31jwLr_hqMV0unIOdrCAEVwww448-182';

// 页面来源
export const ENTER_FROM = {
  SOTRE_PAGE: 'store_page',
  IM_PAGE: 'privatemsg',
};

// tab标识符: 我的店铺、选品库
export const TAB_KEYS = {
  /** 最近添加 */
  HISTORY_ADD: 'history_add',
  /** 我的小店 */
  STORE: 'my_shop',
  /** 选品库 */
  ALLIANCE: 'selective_product',
  /** 我的橱窗 */
  SHOPWINDOW: 'shop_window',
  /** 专属商品 */
  EXCLUSIVE: 'exclusive_goods',
  /** 定向合作 */
  ORIENTATION: 'orientation_goods',
  /** 星图平台 */
  STAR_ATLAS: 'star_atlas',
  /** 协议（合作订单）商品 */
  PROTOCOL: 'protocol_goods',
  /** 鹊桥商品 */
  MAGPIE_BRIDGE: 'magpie_bridge',
  /** 选品车 */
  PICKING_CART: 'picking_cart',
  /** 推荐商品 */
  RECOMMENDED_PRODUCT: 'recommended_product',
};

export const MACHINE_AUDIT_REQUEST_FROM = {
  SHOP_WINDOW: 201,
  STAR: 202,
  OTHER: 203,
};

// 三方选品入口配置
export const VENDOR_MAP: Record<VendorId, VendorEntry> = {
  [VendorId.TAOBAO]: {
    id: VendorId.TAOBAO,
    title: '淘宝',
    pageTitle: '淘宝商品',
    itemType: ItemType.TAOBAO,
    linkSearchGuide: {
      linkType: '淘口令或商品链接',
      linkSource: '手机淘宝',
    },
    icon: 'https://sf3-cdn-tos.douyinstatic.com/obj/temai/FscOF4NZGsAI_EXm2IDQ_sDB67Smwww120-120',
  },
  [VendorId.JD]: {
    id: VendorId.JD,
    title: '京东',
    pageTitle: '京东商品',
    itemType: ItemType.JD,
    linkSearchGuide: {
      linkType: '京东商品链接',
      linkSource: '手机京东',
    },
    icon: 'https://sf3-cdn-tos.douyinstatic.com/obj/temai/Fri3vPnIi5lrEfkX03D30vDXG_0Cwww120-120',
  },
  [VendorId.KAOLA]: {
    id: VendorId.KAOLA,
    title: '考拉',
    pageTitle: '考拉海购商品',
    itemType: ItemType.KAOLA,
    linkSearchGuide: {
      linkType: '考拉海购商品链接',
      linkSource: '手机考拉海购',
    },
    icon: 'https://sf3-cdn-tos.douyinstatic.com/obj/ttfe/business_image/kaola.png',
  },
};

export const CLIPBOARD_MODAL_CLOSE_ICON =
  'https://sf1-cdn-tos.douyinstatic.com/obj/eden-cn/bvkeh7flrjhq/merch_picking/clipboard_modal_close.png';

export const CLIPBOARD_MODAL_CLOSE_AWEME_ICON =
  'https://sf1-cdn-tos.douyinstatic.com/obj/eden-cn/bvkeh7flrjhq/merch_picking/clipboard_modal_close_aweme.png';

export const CLIPBOARD_MODAL_CLOSE_XIGUA_ICON =
  'https://sf1-cdn-tos.douyinstatic.com/obj/eden-cn/bvkeh7flrjhq/merch_picking/clipboard_modal_xigua_close.png';

export const CLIPBOARD_MODAL_CLOSE_HUOSHAN_ICON =
  'https://sf1-cdn-tos.douyinstatic.com/obj/eden-cn/bvkeh7flrjhq/merch_picking/clipboard_modal_huoshan_close.png';

export const CLIPBOARD = 'clipboard';

export const APP_ID = {
  toutiao: 13,
  douyin: 1128,
  xigua: 32,
  huoshan: 1112,
};

export const WEBCAST_WEBVIEW_APPNAME_MAP = {
  news_article: 'toutiao',
  aweme: 'aweme',
  hotsoon: 'hotsoon',
  video_article: 'xigua',
};

export const genMerchPickedSchema = (enter_from: string) => {
  return `sslocal://webcast_webview?url=https%3A%2F%2Flf-webcast-sourcecdn-tos.bytegecko.com%2Fobj%2Fbyte-gurd-source%2F10181%2Fgecko%2Fresource%2Fecom_webcast_legacy_aweme%2Fhtml%2Fpages%2Flive_promotion%2Fcontainers%2Fmerch_picked%2Findex.html%3Fenter_from%3D${enter_from}%26from_picking%3D1%26type%3Dfullscreen%26web_bg_color%3D%2523ffffff%26hide_nav_bar%3D1%26type%3Dfullscreen%26web_bg_color%3D%23ffffff&hide_nav_bar=1`;
};

export enum EMachineAuditCode {
  food = 1020,
  fresh = 1021,
  cosmetic = 1022,
}

// 淘宝账号绑定页面地址
export const TAOBAO_PID_MANAGEMENT_SCHEMA = genSchema({
  url: 'https://lm.jinritemai.com/views/pid_bind/home',
  hide_nav_bar: SchemaBoolean.YES,
  hide_bar: SchemaBoolean.YES,
  hide_more: SchemaBoolean.YES,
  bounce_disable: SchemaBoolean.YES,
  status_bar_color: 'ffffff',
  status_font_dark: SchemaBoolean.YES,
  loading_bgcolor: 'ffffff',
});

// 联盟收款账户实名验证页面地址
export const ALLIANCE_ACCOUNT_AUTH_SCHEMA = genSchema({
  url: 'https://lianmeng.snssdk.com/settle/realName?from=merch_picking',
  title: '开通账户',
  use_ui: SchemaBoolean.YES,
  hide_more: SchemaBoolean.YES,
});

export const FACE_RECOGNITION_RISK_TYPE = 1;

export const VALID_POPUP_ARRS_STORAGE = 'VALID_POPUP_ARRS_STORAGE';
export const INVALID_POPUP_ARRS_STORAGE = 'INVALID_POPUP_ARRS_STORAGE';

// 二级来源
export enum UserActionSecondEnum {
  myStore = '我的小店',
  starPromotion = '星图平台',
  myMerchant = '我的橱窗',
  ownerPromotion = '专属商品',
  searchResult = '搜索添加',
  linkAddResult = '链接添加',
  alliance = '精选联盟',
}

export enum UserActionThirdEnum {
  searchResult = 'search',
  linkAddResult = 'link',
}

// label 标签海淘
export const OVERSEAS_SHOP = '海淘';

// tab标识符: 我的店铺、选品库
export const TAB_KEYS_API_PARAMS = {
  SHOP: 3,
  STAR: 6,
  WINDOW: 2,
  EXCLUSIVE_PLAN: 5,
  SEARCH: 0,
  PROTOCOL: 11,
  HISTORY: 1,
  PICK_CART: 20,
  RECOMMENDED_PRODUCT: 21,
};

// 资质升级引导级别 tips or modal
export enum QualificationLevelEnum {
  unkonwn = 0,
  remind = 1,
  stop = 2,
}

// 绑定接口弹出资质升级码
export const ShowQualificationModalCode = 20002;

export const SchemaCommonParams = {
  hide_nav_bar: 1,
  status_font_dark: 1,
  loading_bgcolor: 'ffffff',
};

// 新绑定接口三级source传值
export const BindThirdSource = {
  search: 1,
  link: 2,
};
// 直播达人挂车 tips 缓存
export const PAIR_LIMIT_TIPS_KEY = 'pair-limit-tips-key';

export enum IMChatFromType {
  SHOP_WINDOW = 1, // 商品橱窗
  STATION_LETTER = 2, // 站内信-底部tab合作消息
  MERCH_PICKING = 3, // 选品广场
  DOUDIAN_DAREN_SQUARE = 4, // 抖店达人广场
  MESSAGE_PLATFORM = 5, // 消息工作台
  DOUYIN_DAREN_SQUARE = 6, // 抖音达人广场
  MERCH_PROMOTING = 7, // 商品决策页
  APPLY_SAMPLE = 8, // 申样页面
  MERCH_PICKING_LIST = 9, // 选品列表
  STATION_LETTER_CREATE = 10, // 站内信-新建联
  STATION_LETTER_UNREAD = 11, // 站内信-未读
  SHOP_DETAIL = 12, // 店铺详情
  LIVE_MERCH_PICK = 13, // 直播选品
}

export const ALERT_CACHE_KEY = 'MERCH_PICKING_LIVE_ALERT_CACHE';

export const PORTAL_LINK_SEARCH_GUIDE: LinkSearchGuideOptions = {
  linkType: '粘贴我的小店或精选联盟商品链接',
  linkSource: '',
};

// 关闭当前添品页的延时
export const CLOSE_MERCH_PAGE_DELAY_TIME = 2400;

// 搜索场景下支持的tab，不太能追溯为啥搜索场景下支持这些tab 待后端帮忙查一下，这里以线上为准搬运过来的，线上逻辑见 apps/fe-alliance-webcast-legacy/src/pages/merch_picking/app.tsx 的searchTabs
export const SEARCH_MODE_TAB_KEYS = [
  TAB_KEYS.SHOPWINDOW,
  TAB_KEYS.STORE,
  TAB_KEYS.STAR_ATLAS,
  TAB_KEYS.MAGPIE_BRIDGE,
  TAB_KEYS.PROTOCOL,
  TAB_KEYS.PICKING_CART,
];

// 筛选组件的span属性值，对应一行展示的标签数
export const FILTER_OPTION_SPAN_V1 = 4; // 旧版span=4（一行展示3个标签）
export const FILTER_OPTION_SPAN_V2 = 3; // 新版span=3（一行展示4个标签）

export const MERCH_PICKING_CART_MOVE_MODAL = '1'; // 选品车迁移提示弹窗缓存标识

export const REFER_MERCH_CART_SHOPWINDOW_RETURN = 'REFER_MERCH_CART_SHOPWINDOW_RETURN';

export const MERCH_PICKING_RECOMMEND_PATH =
  'https://lf-ecom-gr-sourcecdn.bytegecko.com/obj/byte-gurd-source-gr/ecom/alliance_mobile/webcast/alliance_webcast_legacy/html/pages/merch-picking-recommend/index.html';
