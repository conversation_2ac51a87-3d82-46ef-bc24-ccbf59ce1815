@import "~common/styles/merch_picking/base";
@import "~common/styles/merch_picking/mixins";
// @import "~assets/icons/merch_picking/style.css";

#root {
  height: 100vh;
  overflow: hidden;
}

.page {
  height: 100vh;
  overflow: hidden;
}

.tabviews {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.tabview {
  display: none;
  justify-content: space-between;
  overflow-x: hidden;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
  @include mask($position: absolute);
}

.confirm-modal__overlay {
  top: 44px;
}

.deposit-hint-bg {
  width: 100%;
  padding: 10px 16px;
  box-sizing: border-box;
  background-color: #face151e;
  mix-blend-mode: normal;
  border-radius: 4px;
  margin-bottom: 6px;
}

.header-hint {
  background-color: #fff2e0;
  font-size: 13px;
  line-height: 18px;
  // color: rgb(235, 168, 37);
  color: #ff6f00;
  padding: 10px 16px;
  display: flex;
  align-items: center;
  margin-bottom: 4px;

  &-left {
    display: flex;
  }

  &__icon {
    display: inline-block;
    width: auto;
    height: 16px;
    margin-right: 8px;

    &__default {
      display: inline-block;
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
  }

  &__text {
  }

  &-center {
    margin-left: 0;
    margin-right: auto;
    flex: 1;
  }

  &-link {
    margin-left: 2px;
    color: #fe2c55;
    display: inline-flex;
    align-items: center;
  }

  &-right {
    flex-shrink: 0;
    width: 12px;
    height: 12px;
    display: inline-block;
    margin-left: 8px;
  }



  &-arrow {
    background-image: url("data:image/svg+xml,%3Csvg width='12' height='12' viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath opacity='0.34' fill-rule='evenodd' clip-rule='evenodd' d='M3.61695 9.44783C3.3658 9.69888 3.3658 10.1059 3.61695 10.3569C3.86809 10.608 4.27523 10.608 4.52637 10.3569L8.4658 6.41734C8.71695 6.16628 8.71695 5.75925 8.4658 5.5082C8.46323 5.50599 8.46152 5.50381 8.45895 5.50165L4.52637 1.5686C4.27523 1.31755 3.86809 1.31755 3.61695 1.5686C3.3658 1.81965 3.3658 2.22668 3.61695 2.47774L7.10209 5.96281L3.61695 9.44783Z' fill='%23fe2c55'/%3E%3C/svg%3E%0A");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    width: 12px;
    height: 12px;
    display: inline-block;
    vertical-align: middle;
  }
}

.qualification-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 16px;
  background-image: linear-gradient(0deg, rgba(250, 206, 21, .12), rgba(250, 206, 21, .12));
  margin-bottom: 8px;
}

.qualification-left {
  width: 12px;
  height: 12px;
  margin-left: 2px;
  margin-right: 8px;

  .qualification-left-icon {
    width: 100%;
    height: 100%;
  }
}

.qualification-content {
  flex: 1;
  margin-right: 20px;

  .qualification-content-text {
    font-size: 13px;
    line-height: 18px;
    color: #eba825;
  }
}

.qualification-right-icon {
  width: 7px;
  height: 11px;
  background-image: url(~static/images/merch_picking/qualification-right.png);
  background-position: center;
  background-size: 100%;
}

.page_mode-item:not(:last-child) {
  margin-right: 24px;
}
