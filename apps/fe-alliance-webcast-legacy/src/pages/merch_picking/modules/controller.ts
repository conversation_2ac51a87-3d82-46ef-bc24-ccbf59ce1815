import { useEffect, useRef, useCallback, useMemo } from 'react';
// @ts-ignore
import via from '@ies/webcast-via';
import webcast from '@bridge/webcast';
import {
  AppState,
  PortalTab,
  PromotionIdType,
  RemindMessage,
  AnXinGouModalInfo,
  UserInfo,
  PickingMerch,
  Merch,
  IPopupResponse,
  LiveBindOperatePromotionParams,
  CommonQualification,
  IBypassInfo,
  BindSource,
  MAX_SELECT_COUNT,
  TOTAL_MAX_SELECT_COUNT,
  SceneType,
  RouteQuery,
  BatchBindResponse,
  BindResults,
} from '../types';
import {
  getQuery,
  isInAweme,
  getUrlByEnv,
  isInHuoshan,
  isInToutiao,
  isInSAAS,
  globalProps,
  osPlatform,
} from '@src/lib/env_detect';
import { isLiving, genSchema } from '@src/lib/util/merch_picking';
import { PublishContinuePushStream } from '@src/lib/util/event';
import {
  searchMerchListByLink,
  fetchPickedMerchList,
  getRemindMessage,
  fetchPopup,
  setAdAuth,
  unbindPromotionToLive,
  qualificationCheck,
  getPairLimitTip,
  liveBatchBind,
  checkCommonAlert,
} from '../services/api';
import showToast, { hideToast } from 'components/toast/ecom_toast';
import {
  genAwemeGoodsEditor,
  isSameTab,
  sendLog,
  genMachineAuditRule,
  userActionRecord,
  COMMON_STAT_PARAMS,
  jumpToOpenOverseasAccountPage,
  getBindApiParams,
  getPairLimitTipsCache,
  setPairLimitTipsCache,
  getSearchTabList,
} from '../services/utils';
import { EVENTNAME, LinkSearchScene, sendLog as sendLinkSearchLog } from '../services/send-linksearch-log';
import { useAppendedList } from './append_list';
import { useLiveTabList } from './live';
import { useAccounts } from './account';
import useAppState from './app_state';
import {
  TAB_KEYS,
  UserActionThirdEnum,
  genMerchPickedSchema,
  INVALID_POPUP_ARRS_STORAGE,
  VALID_POPUP_ARRS_STORAGE,
  UserActionSecondEnum,
  QualificationLevelEnum,
  SchemaCommonParams,
  BindThirdSource,
  ALERT_CACHE_KEY,
  CLOSE_MERCH_PAGE_DELAY_TIME,
} from '../constants';
import { openWebview } from '@src/common/kit/bridge';

import { openSchema } from '@src/common/bridge';
import { IPairLimitCommonParams } from '../components/pair_limit/types';
import {
  computeNeedInvoiceShow,
  InvoiceDrawerShow,
  makeNoticeItems,
} from '@src/pages/merch_picking/components/invoice_notice_modal/invoiceNoticeDrawer';
import { useDefaultTab } from '../services/hooks';
import { autofocusFn } from '@alliance-mobile/utils';
import { MERCH_BTM } from '../constants/btm';
import { myCoreLinkClient } from '@src/lib/util/mera/core-link';
import { getPriceLevel } from '@alliance-mobile/platform-merchant-common/src/utils/price-level';
import { usePersistCallback } from '@byted/hooks';
import { getStorage, getStorageString } from '@src/lib/util';
import { isEmpty } from '@byted-flash/utils';

const query = getQuery<RouteQuery>();

export interface AppController {
  // 左上角返回
  onNavBack(): void;

  // “粘贴商品链接”按钮点击，展示链接搜索弹窗
  onLinkSearchToggleClick(): void;

  // “搜索商品或店铺”按钮点击，展示聚合搜索弹窗
  onComboSearchToggleClick(): void;

  // “选品”按钮点击，跳转到选品广场首页
  onClickJumpToMerchPickingPage(): void;

  // 商品按钮点击处理：加橱窗、添加、移除、替换
  onMerchPick(merch: Merch, index: number, fromBatch?: boolean, fromClip?: boolean): Promise<void>;

  // 商品点击处理，跳转到商品详情页
  onMerchClick(merch: Merch): void;

  // 商品标题点击
  onMerchTitleClick(merch: Merch): void;

  onMerchBetterPriceClick(merch: Merch): void;

  // 淘宝PID绑定弹窗点击“绑定PID”，跳转到淘宝PID绑定页面
  onTaobaoAuthConfirm(): void;

  // 淘宝PID绑定弹窗点击“取消”, 关闭弹窗
  onTaobaoAuthCancel(): void;

  // 展示淘宝PID绑定确认弹窗
  openTaobaoAuthConfirm(): void;

  // 展示搜索帮助弹窗
  openSearchHelpView(): void;

  // 关闭剪贴板弹窗
  onCloseClipboardModal(): void;
  // 重新打开剪贴板弹框
  onResumeClipboardModal(): void;

  // 点击tab
  onTabClick(tab: PortalTab): void;
  // 点击购物袋
  onPressShoppingBag(): void;
  // 更换选品车tab下空态时是否展示一键添品状态
  changeShowAddRecommend(): void;
  // 点击添加失败的弹窗
  onPickFailedConfirm(): void;
  // 点击搜索后退
  onSearchNavBack(): void;
  // 点击洋码头弹窗确认
  onYMaTouAuthConfirm(): void;
  // 点击洋码头弹窗取消
  onYMaTouAuthCancel(): void;
  // 点击搜索的tab
  onClickSearchTab(tab: PortalTab): void;
  // 点击链接添加的后退按钮
  onLinkSearchNavBack(): void;
  // 点击搜索帮助后退按钮
  onSearchHelpBack(): void;
  // 点击搜索按钮
  comboSearchConfirm(): void;
  // 点击橱窗下的星图相同商品弹窗确认
  addGoodNotUseStarAtlas(): void;
  // 点击橱窗下的星图相同商品弹窗取消
  hideGoodInStarAtlasModal(): void;
  handleClickMachineAuditRule(url: string, title: string): void;
  onClosePopupLinkModal(): void;
  onConfirmPopupLinkModal(): void;
  // 账户资质弹窗，确认跳转方法
  onAccountModalConfirm(): void;
  // 账户资质弹窗，取消跳转方法
  onAccountModalCancel(): void;
  // 广告弹窗 确认
  onAdModalConfirm(isConfirm: boolean): void;
  // 海淘账户引导弹窗
  onConfirmOverseasAccount(): void;
  onCancelOverseasAccount(): void;
  onConfirmFirstShowOverseas(): void;
  onConfirmGoodsLimit(): void;
  // 获取安心购信息
  onUpdateAnXinGoInfo(): void;
  // 绑定公共弹窗确认事件
  onCommonModalConfirm(): void;
  // 绑定公共弹窗取消事件
  onCommonModalCancel(): void;
  // 资质升级检查
  qualificationCheck(): void;
  // 资质升级弹窗去升级按钮
  onQualificationModalConfirm(): void;
  // 资质升级弹窗取消按钮
  onQualificationModalCancel(): void;
  // 绑定操作资质升级弹窗去升级按钮
  onBindQualificationModalConfirm(): void;
  // 绑定操作资质升级弹窗取消按钮
  onBindQualificationModalCancel(): void;
  // 检查商品豁免次数以及是否可以豁免
  checkPairLimit(data: Merch, index: number, callback: (bypassed: boolean) => void): void;
  // 打开豁免原因 Sheets 弹框
  openPairLimitSheets(info: IPairLimitCommonParams, bypassInfo: IBypassInfo): void;
  onPairLimitModalClose(): void;
  onPairLimitPopupClose(): void;
  // 绑定跳转编辑页 因为服务端兼容性原因临时过渡 后续会干掉这个操作
  skipToEditor(promotionId: string): void;
  /** 点击全选按钮时触发的埋点上报 */
  sendSelectAllLog(): void;
  /** 取消某个tab的全选状态 */
  onCancelSelectAll(tabs: string): void;
  /** 取消某个tab的全选状态 */
  onClearSelectList(): void;
  /** 增删批量选择商品 */
  onBatchSelectChange(merch: Merch, status: boolean): void;
  /** 批量添加 */
  onBatchAdd(selectAll: boolean, merchList?: Merch[]): void;
  /** 隐藏批量添加结果弹层 */
  onHideBatchModal(): void;
  checkAlert(successCb?: () => void): void;
  changeAutoSelectAll(flag: boolean): void;
  mixController: IMixController;
}

export interface IMixController {
  /** 点击关联商品 */
  onClickAggregateButton: (merch: Merch, options?: unknown) => Promise<void>;
  closeAggrPromotion: () => void;
}

export function useAppController(): [
  AppController,
  AppState,
  PortalTab[],
  PromotionIdType[],
  boolean
  // eslint-disable-next-line indent
] {
  // 通知外部可以开始接受消息
  useEffect(() => {
    query.pick_mode === 'id' && webcast.app?.publishEvent?.({ eventName: 'ready_webcast_merchpicking', params: {} });
  }, []);
  // 获取已添加过的商品
  const [
    pickedPromotionsListRef,
    appendMerchToAppendedList,
    removeMerchFromAppendedList,
    appendedListState,
    setAppendedList,
    fetchedPickedList,
  ] = useAppendedList();
  const [liveTabList, tabListReady, defaultTab, isAB] = useLiveTabList();
  // useLiveTabList的 defaultTab 包含了默认定位逻辑（如果后端有下发某个tab anchored为true，则锚点到后端下发的这个tab，否则走原来的前端锚点判断 见useDefaultTab方法）
  // 默认定位只适用于一级添品页，不适用于搜索场景，因为搜索场景下展示的tab列表和一级添品页所展示的tab列表不一样，搜索是前端根据tab_list接口下发的tab做了一下过滤，后端下发逻辑目前没管搜索场景
  // 所以搜索场景的tab锚点还是走原来的前端锚点判断 useDefaultTab方法
  // defaultTabByFE 搜索场景使用
  const defaultTabByFE = useDefaultTab();
  const [appStateRef, updateAppState] = useAppState();
  const [accountStateRef, accountStateController] = useAccounts();
  const controllerRef = useRef({
    promotionId: '',
    userId: '',
    timestamp: new Date().getTime(),
    live_status: '',
    pickingMerchInfo: {},
    popupLinkResult: {},
    remindMessage: {} as RemindMessage,
    qualificationMsg: {} as CommonQualification,
  }).current;

  const getBtm = useCallback(() => {
    if (appStateRef.current.isComboSearchViewActive) return MERCH_BTM[UserActionThirdEnum.searchResult];
    else if (appStateRef.current.isLinkSearchViewActive) return MERCH_BTM[UserActionThirdEnum.linkAddResult];
    else if (MERCH_BTM[appStateRef.current.activeTab.key]) return MERCH_BTM[appStateRef.current.activeTab.key];
    else return '';
  }, [appStateRef]);

  const getCommonStatParams = useCallback(() => {
    return {
      anchor_id: controllerRef.userId,
      room_id: query.room_id,
      live_status: controllerRef.live_status,
      ...COMMON_STAT_PARAMS,
    };
  }, []);

  const batchAddCallBack = usePersistCallback(
    (
      res: BatchBindResponse,
      merchList: Merch[],
      options: {
        logParams?: Record<string, unknown>;
      }
    ) => {
      const batchList = (merchList || appStateRef.current.batchSelectedList).map(merch => {
        return {
          promotion_id: merch.promotion_id,
          product_id: merch.product_id,
          item_type: merch.item_type,
          bind_source: merch.bind_source,
          tab_title: merch.tab_title, // 该参数后端不需要，只是埋点用
          tag_codes: merch.tag_codes,
        };
      });

      const { data } = res || {};
      const {
        success_count: successCount = 0,
        failure_count: failureCount = 0,
        failure_list: failureList = [],
        oversold_remaind_result: oversoldList = [],
        success_pmt_info: successList = [],
        is_toast: isToast,
        guide_info: guideInfo,
        partial_failure_list,
        partial_failure_count,
      } = data || {};
      const { logParams = {} } = options || {};

      const sendPickProduct = (merch: typeof batchList[number]) => {
        sendLog('pick_product', {
          ...logParams,
          // logParams里会进行批量化处理，这里覆盖掉（commodity_id，product_id，commodity_type）
          commodity_id: merch.promotion_id,
          product_id: merch.product_id,
          commodity_type: merch.item_type,
          price_level: getPriceLevel(merch.tag_codes || []),
        });
      };

      // 将添加成功的商品推入appendedList中
      (successList || []).forEach(item => item.promotion_id && appendMerchToAppendedList(item.promotion_id));
      // 同步商品列表信息
      const { enter_from } = query;
      if (enter_from === 'webcast_living') {
        via.business.refreshPromotions({ need_refresh: true });
      } else {
        const currentLength = pickedPromotionsListRef.current.length;
        via.business.modifiedPromotions({ promotion_num: currentLength });
      }
      if (appStateRef.current.isMerchV2) {
        webcast.app.publishEvent({
          eventName: 'ecom.anchor.refreshPickedView',
          params: {},
        });
      }
      // 清空选中商品列表
      updateAppState({ batchSelectedList: [] });
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      $app.onCancelSelectAll('all');
      const hasSuccess = Boolean(successList?.length || 0);
      const isBatchBindSuccess = failureCount === 0 && !partial_failure_count;
      if (!isBatchBindSuccess && hasSuccess) {
        // 如果有成功但没全部
        const successListInBatch = batchList.filter(item =>
          Boolean(successList?.find?.(si => si?.promotion_id === item?.promotion_id))
        );
        successListInBatch.forEach(merch => {
          sendPickProduct(merch);
        });
      }

      // 上报添品结果 统计添品成功率
      sendLog('add_product_result_collect', {
        page_name: '移动端-直播添品页',
        add_type: 'batch',
        success_count: data?.success_count || 0,
        failure_count: data?.failure_count || 0,
        total_count: (data?.success_count || 0) + (data?.failure_count || 0),
      });

      if (isBatchBindSuccess) {
        // 如果全部商品都添加成功
        // 新版添品页 非链接和商品搜索场景 添加成功后关闭当前添品页 回到前置页
        showToast(`${successCount}个商品添加成功`);
        if (appStateRef.current.isMerchV2) {
          if (!appStateRef?.current?.isLinkSearchViewActive && !appStateRef?.current?.isComboSearchViewActive) {
            setTimeout(() => {
              webcast?.app?.close();
            }, CLOSE_MERCH_PAGE_DELAY_TIME);
          }
        }
        sendLog('livesdk_pick_product_success', { ...(logParams || {}) });
        // 算法侧要求单个商品上报pick_product
        batchList.forEach(merch => {
          sendPickProduct(merch);
        });
        return;
      }

      sendLog('addproduct_errorpop_show', {
        ...getCommonStatParams(),
        page_name: '直播添品页',
        live_type: !isLiving ? 'off' : 'on',
        errcode: guideInfo?.err_code || '',
        title: guideInfo?.title || '',
        message: guideInfo?.content || '',
        button_for: guideInfo?.confirm_text || '',
        button_url: guideInfo?.guide_link || '',
        failure_count: failureCount || 0,
      });

      // 如果判定为用toast显示添加错误信息，则直接弹toast
      if (isToast) {
        return showToast(guideInfo?.content || '添加或取消绑定商品到直播间失败');
      }

      if (guideInfo) {
        updateAppState({
          isShowCommonModal: true,
          commonModal: guideInfo,
          isCancelBindToLiveModalShow: false,
          isLoadingMaskActive: false,
        });
        return;
      }
      // 如果有添加失败的商品，显示批量绑定结果信息弹窗
      updateAppState({
        isShowBatchResultModal: true,
        batchResultModal: {
          successCount,
          failureCount,
          failureList: failureList || [],
          oversoldList: oversoldList || [],
          partialFailureList: partial_failure_list || [],
        },
      });
    }
  );

  const $app = useMemo<AppController>(() => {
    return {
      onNavBack() {
        if (appStateRef.current.isTaobaoAuthConfirmActive) {
          $app.onTaobaoAuthCancel();
        } else if (appStateRef.current.isSearchHelpViewActive) {
          updateAppState({ isSearchHelpViewActive: false });
        } else if (appStateRef.current.isMerchPickForbidden) {
          updateAppState({ isMerchPickForbidden: false });
        } else {
          const currentTimestamp = new Date().getTime();
          const duration = currentTimestamp - controllerRef.timestamp;
          sendLog('livesdk_add_product_duration', {
            ...getCommonStatParams(),
            duration,
          });
          via.app.close();
        }
      },
      onLinkSearchToggleClick() {
        sendLog('livesdk_click_button', {
          ...getCommonStatParams(),
          button_for: 'link',
        });
        userActionRecord.setUserAction({
          actionLevel2: UserActionSecondEnum.alliance,
          actionLevel3: UserActionThirdEnum.linkAddResult,
        });
        updateAppState({ isLinkSearchViewActive: true });
      },
      onComboSearchToggleClick() {
        userActionRecord.setUserAction({
          actionLevel3: UserActionThirdEnum.searchResult,
        });
        if (osPlatform === 'ios') {
          autofocusFn();
        }
        updateAppState({ isComboSearchViewActive: true });
      },
      onClickJumpToMerchPickingPage() {
        sendLog('xpgc_click', {
          ...getCommonStatParams(),
          user_id: controllerRef.userId,
        });

        openSchema({
          schema: genSchema({
            url: `https://lf-ecom-gr-sourcecdn.bytegecko.com/obj/byte-gurd-source-gr/merchPicking/gecko/h5Resource/alliance_merch_picking_h5/cn/html/merch-picking/index.html?_pia_=1&mode=rich`,
            isWebCast: true,
            hide_nav_bar: 1,
            trans_status_bar: 1,
            status_bar_color: 'black',
            hide_loading: 1,
            web_bg_color: 'ffffff',
            loader_name: 'forest',
            disable_thread_spread: 1,
            disable_host_jsb_method: 1,
          }),
        });
      },
      onCommonModalConfirm() {
        if (appStateRef.current.commonModal.guide_link) {
          // 直播中 跳转全屏页需要主动发送 continue_push_stream 事件，否则直播画面会黑屏
          // PublishContinuePushStream里做了新版本和直播中状态的判断
          PublishContinuePushStream();
          openSchema({
            schema: genSchema({
              url: appStateRef.current.commonModal.guide_link,
            }),
          });
        }
        updateAppState({ isShowCommonModal: false });
      },
      onCommonModalCancel() {
        updateAppState({ isShowCommonModal: false });
      },
      changeShowAddRecommend(showAddRecommend: boolean) {
        updateAppState({ showAddRecommend });
      },
      skipToEditor(promotionId) {
        location.href = genAwemeGoodsEditor(promotionId);
        updateAppState({
          isLoadingMaskActive: false,
          isBackFromGoodEditor: true,
        });
      },
      // 粘贴板弹窗用到了这个方法, 链接界面-添加
      async onMerchPick(
        merch: PickingMerch,
        index: number,
        fromBatch?: boolean,
        fromClip?: boolean,
        disableLog?: boolean
      ) {
        // 最早是前端根据本地的状态 控制显示是添加还是移除 中间有延迟 用该字段判断列表api已经返回结果
        if (fetchedPickedList.current) {
          return;
        }

        // 判断已选商品列表中商品的个数是否超过200  历史产品逻辑  该逻辑可放在服务端 因为需要额外请求一个接口获取 或者直接融合到获取列表的接口里 也可以融合在merch属性上面 但是从设计来讲不太合适
        const appendedList = pickedPromotionsListRef.current;
        // 用户行为记录
        const userActionProps = userActionRecord.getUserActionMap();
        const isCancel = appendedList.findIndex(id => id === merch.promotion_id) >= 0;
        sendLog('livesdk_pick_product', {
          ...userActionProps,
          ...getCommonStatParams(),
          first_source: getClickFirstSource(),
          second_source: appStateRef.current.activeTab.title,
          commodity_id: merch.promotion_id,
          product_id: merch.product_id,
          commodity_type: merch.item_type,
          click_type: isCancel ? 'cancel' : 'add',
          edit_type: 'add_product',
        });

        // 防止上一个Toast未消失时，出现taost重叠的情况
        hideToast();

        // 移除已添加商品
        if (isCancel) {
          // 智能直播货盘仅取出 promotionId 即可
          if (query.pick_mode === 'id') {
            webcast.app.publishEvent(
              {
                eventName: 'submit_webcast_merchpicking',
                params: {
                  removePickedPromotionId: merch.promotion_id,
                  toast: false,
                },
              },
              () => {
                removeMerchFromAppendedList(merch.promotion_id);
              }
            );
            return;
          }

          // 解绑直播间
          updateAppState({ isCancelBindToLiveModalShow: true });
          bindGoodsToLiveNew(
            {
              promotion_id: merch.promotion_id,
              product_id: merch.product_id,
              item_type: merch.item_type,
            },
            true,
            () => {
              removeMerchFromAppendedList(merch.promotion_id);
              if (appStateRef.current.clipBoardModalVisible) {
                userActionRecord.fallbackRecord();
              }
              updateAppState({
                isCancelBindToLiveModalShow: false,
                clipBoardModalVisible: false,
              });
              if (!disableLog) sendPickProductSuccessLog(merch, true);
            }
          );
          return;
        }

        if (appendedList.length === TOTAL_MAX_SELECT_COUNT) {
          showToast(`单场直播最多支持添加${TOTAL_MAX_SELECT_COUNT}个商品`);
          return;
        }

        updateAppState({
          isLoadingMaskActive: index >= 0,
          isClipboardModalAuditing: index < 0,
        });
        const promotionId = merch.promotion_id;
        // 是否是星图
        const isFromStarAtlas =
          appStateRef.current.activeTab.key === TAB_KEYS.STAR_ATLAS ||
          appStateRef.current.activeSearchTab.key === TAB_KEYS.STAR_ATLAS;
        // 是否从我的橱窗进来
        const isFromShopWindow =
          (appStateRef.current.activeTab.key === TAB_KEYS.SHOPWINDOW &&
            !appStateRef.current.clipBoardModalVisible &&
            !appStateRef.current.isLinkSearchViewActive &&
            !appStateRef.current.isComboSearchViewActive) ||
          (appStateRef.current.activeSearchTab.key === TAB_KEYS.SHOPWINDOW &&
            appStateRef.current.isComboSearchViewActive);
        // 橱窗下的商品 或者 星图下商品已在橱窗中不需要 调添加到橱窗的接口
        const isNeedAddToShopWindow =
          !isFromShopWindow && (!isFromStarAtlas || (isFromStarAtlas && merch.extra?.user_shop_exist === 'false'));
        try {
          if (isFromShopWindow && merch.is_star_promotion) {
            updateAppState({
              isGoodInStarAtlasModalShow: true,
              isLoadingMaskActive: false,
            });
            controllerRef.promotionId = promotionId;
            controllerRef.pickingMerchInfo = merch;
            return;
          }
          const isLinkActive =
            appStateRef.current.isLinkSearchViewActive || Boolean(appStateRef.current.isSkipFromClipBoard);
          const realTab = appStateRef.current.isComboSearchViewActive
            ? appStateRef.current.activeSearchTab
            : appStateRef.current.activeTab;

          const bindParams: LiveBindOperatePromotionParams = {
            promotion_id: promotionId,
            product_id: merch.product_id,
            item_type: merch.item_type,
            bind_source: getBindApiParams(isLinkActive, realTab.key),
          };
          const thirdSource = userActionProps.pick_third_source && BindThirdSource[userActionProps.pick_third_source];
          if (thirdSource) {
            bindParams.third_source = thirdSource;
          }
          const invoiceNoticeList = makeNoticeItems([merch]);
          const addMerch = function () {
            // 智能直播货盘仅取出 promotionId 即可
            if (query.pick_mode === 'id') {
              updateAppState({
                isLoadingMaskActive: false,
                clipBoardModalVisible: false,
              });
              webcast.app.publishEvent(
                {
                  eventName: 'submit_webcast_merchpicking',
                  params: {
                    pickedPromotionIds: [merch.promotion_id],
                    toast: false,
                  },
                },
                () => {
                  if (isCancel) {
                    removeMerchFromAppendedList(merch.promotion_id);
                  } else {
                    appendMerchToAppendedList(merch.promotion_id);
                  }
                }
              );
              return;
            }

            bindGoodsToLiveNew(bindParams, false, (isGuideInfo: boolean, msg: string) => {
              updateAppState({
                isLoadingMaskActive: false,
                clipBoardModalVisible: false,
              });
              if (isGuideInfo) {
                return;
              }
              if (!disableLog) sendPickProductSuccessLog(merch, false);
              appendMerchToAppendedList(promotionId);
              const toastMsg = !isNeedAddToShopWindow ? '商品已添加到购物袋' : '商品已添加到购物袋和橱窗';
              if (fromBatch) {
                updateAppState({ batchSelectedList: [] });
              }
              setTimeout(() => {
                showToast(toastMsg || '添加成功');
              }, 300);
              // 新版添品页 非链接和商品搜索场景 添加成功后关闭当前添品页 回到前置页
              if (appStateRef.current.isMerchV2) {
                if (!appStateRef?.current?.isLinkSearchViewActive && !appStateRef?.current?.isComboSearchViewActive) {
                  setTimeout(() => {
                    webcast?.app?.close();
                  }, CLOSE_MERCH_PAGE_DELAY_TIME);
                }
              }
            });
          };
          // 当存在需弹窗商品且不在七天限制之内
          // 且从粘贴板来（外面批量添加的已经做了处理了）
          if (invoiceNoticeList.length !== 0 && computeNeedInvoiceShow() && fromClip) {
            updateAppState({
              isLoadingMaskActive: false,
              clipBoardModalVisible: false,
            });
            InvoiceDrawerShow({ noticeItems: invoiceNoticeList, confirmModal: addMerch });
            return;
            // 中断后续流程
          }
          addMerch();
        } catch (error) {
          sendLog('livesdk_show_popup', {
            ...getCommonStatParams(),
            popup: 'add_product_fail',
          });
          updateAppState({
            isLoadingMaskActive: false,
          });
        }
      },
      onMerchClick(merch: Merch) {
        return;
      },
      onMerchTitleClick(merch: Merch, index: number) {
        // 非抖音端禁止跳转
        if (!isInAweme) {
          return;
        }

        const url = `https://lm.jinritemai.com/views/merch_promoting?promoting_id=${merch.promotion_id}`;
        const openPromotingPageSchema = {
          ...getCommonStatParams(),
          url,
          hide_nav_bar: 1,
          nav_bar_color: 'ffffff',
          status_bar_color: 'ffffff',
          title_color: '161823',
          status_font_dark: 1,
          promotion_id: merch.promotion_id,
          loading_bgcolor: 'ffffff',
          bg_theme: 'ffffff',
          pick_first_source: 'live',
          pick_second_source: 'feed',
          pick_third_source: '移动添品页',
          pick_source_id: '移动添品页',
          page_name: '移动添品页',
        };
        // 用户行为记录
        const userActionProps = userActionRecord.getUserActionMap();
        sendLog('enter_product_inner_detail', {
          ...userActionProps,
          ...getCommonStatParams(),
          commodity_id: merch.promotion_id,
          commodity_location: index,
          recall_source: merch.recall_source,
          log_pb: merch.logPb,
          highprice_warning_show: merch?.price_good_same_style?.text_right?.text,
        });
        // 直播中 跳转全屏页需要主动发送 continue_push_stream 事件，否则直播画面会黑屏
        // PublishContinuePushStream里做了新版本和直播中状态的判断
        PublishContinuePushStream();
        openSchema({ schema: genSchema(openPromotingPageSchema) });
      },
      /* eslint-disable @typescript-eslint/no-use-before-define */
      onMerchBetterPriceClick(merch: Merch, index: number) {
        // 用户行为记录
        const userActionProps = userActionRecord.getUserActionMap();
        sendLog('enter_product_inner_detail', {
          ...userActionProps,
          ...getCommonStatParams(),
          commodity_id: merch.promotion_id,
          commodity_location: index,
          recall_source: merch.recall_source,
          log_pb: merch.logPb,
          highprice_warning_show: merch?.price_good_same_style?.text_right?.text,
          click_type: '点击添加入口',
          button_for: '去添加',
        });
      },
      onTaobaoAuthConfirm() {
        updateAppState({
          isBackFromTaobaoAuth: true,
          isTaobaoAuthConfirmActive: false,
        });
        openWebview({
          url: getUrlByEnv('https://ffh.jinritemai.com/falcon/e_commerce/webcast/pid_binding/home'),
        });
      },
      onTaobaoAuthCancel() {
        updateAppState({ isTaobaoAuthConfirmActive: false });
      },
      openTaobaoAuthConfirm() {
        updateAppState({ isTaobaoAuthConfirmActive: true });
      },
      openSearchHelpView() {
        updateAppState({ isSearchHelpViewActive: true });
      },
      onYMaTouAuthConfirm() {
        updateAppState({
          isBackFromTaobaoAuth: true,
          isYMaTouAuthConfirmActive: false,
        });
        openWebview({
          url: getUrlByEnv('https://ffh.jinritemai.com/falcon/e_commerce/webcast/pid_binding/home'),
        });
      },
      onYMaTouAuthCancel() {
        updateAppState({ isYMaTouAuthConfirmActive: false });
      },
      onCloseClipboardModal() {
        updateAppState({ clipBoardModalVisible: false });
        userActionRecord.fallbackRecord();
        sendLog('livesdk_close_popup', {
          ...getCommonStatParams(),
          popup: 'product_link',
        });
      },
      onResumeClipboardModal() {
        updateAppState({ clipBoardModalVisible: true });
      },
      onTabClick(tab: PortalTab) {
        if (!isSameTab(tab, appStateRef.current.activeTab)) {
          tab.isRendered = true;
          userActionRecord.setUserAction({ actionLevel2: tab.title });
          updateAppState({ activeTab: tab });
        }
      },
      onPressShoppingBag() {
        sendLog('livesdk_click_button', {
          ...getCommonStatParams(),
          button_for: 'shop',
        });
        if (query.from_picked === '1') {
          const currentTimestamp = new Date().getTime();
          const duration = currentTimestamp - controllerRef.timestamp;
          sendLog('livesdk_add_product_duration', {
            ...getCommonStatParams(),
            duration,
          });
          via.app.close();
          return;
        }
        updateAppState({ isBackFromPicked: true });
        location.href = genMerchPickedSchema(query.enter_from);
      },
      onPickFailedConfirm() {
        updateAppState({ isPickFailedModalShow: false });
        sendLog('livesdk_confirm_popup', {
          ...getCommonStatParams(),
          popup: 'add_product_fail',
        });
      },
      changeAutoSelectAll(select_all = false) {
        updateAppState({ autoSelectAll: select_all });
      },
      onConfirmOverseasAccount() {
        updateAppState({ isShowOpenOverseasAccount: false });
        jumpToOpenOverseasAccountPage();
      },
      onConfirmFirstShowOverseas() {
        updateAppState({ isShowFirstAddOverseas: false });
      },
      onConfirmGoodsLimit() {
        updateAppState({ isShowGoodsLimitModal: false });
      },
      onCancelOverseasAccount() {
        updateAppState({ isShowOpenOverseasAccount: false });
      },
      async onSearchNavBack() {
        myCoreLinkClient.sendCoreLinkEvent('live_add_search_back_btn_click');
        userActionRecord.fallbackRecord();
        updateAppState({
          isComboSearchViewActive: false,
          activeSearchTab: defaultTabByFE,
        });
      },
      onClickSearchTab(tab: PortalTab) {
        if (!isSameTab(tab, appStateRef.current.activeSearchTab)) {
          tab.isRendered = true;
          updateAppState({ activeSearchTab: tab });
        }
      },
      onLinkSearchNavBack() {
        // 回退上一层用户行为记录
        userActionRecord.fallbackRecord();
        updateAppState({ isLinkSearchViewActive: false });
        myCoreLinkClient.sendCoreLinkEvent('live_link_add_back_btn_click');
      },
      onSearchHelpBack() {
        updateAppState({ isSearchHelpViewActive: false });
      },
      comboSearchConfirm() {
        sendLog('livesdk_click_button', {
          ...getCommonStatParams(),
          button_for: 'search',
        });
        // 搜索场景下 设置activeSearchTab为搜索之前用户停留的tab
        // 如果搜索tab里没有之前用户停留的tab，则不设置，走defaultTab
        const searchTabs = getSearchTabList(appStateRef?.current?.tabList);
        const searchModeHasCurrActiveTab = searchTabs?.filter(
          item => item?.key === appStateRef?.current?.activeTab?.key
        );
        // 如果搜索tab里有之前用户停留的tab，则activeSearchTab设置为之前停留的tab，否则设置为默认tab
        if (searchModeHasCurrActiveTab?.length) {
          updateAppState({
            activeSearchTab: appStateRef?.current?.activeTab,
          });
        }
      },
      // 星图
      addGoodNotUseStarAtlas() {
        const { promotionId } = controllerRef;
        const promotionsList = [promotionId, ...pickedPromotionsListRef.current].join(',');
        // 用户行为记录
        const userActionProps = userActionRecord.getUserActionMap();
        const isLinkActive =
          appStateRef.current.isLinkSearchViewActive || Boolean(appStateRef.current.isSkipFromClipBoard);
        const bindParams: LiveBindOperatePromotionParams = {
          promotion_id: promotionId,
          product_id: (controllerRef.pickingMerchInfo as PickingMerch).product_id,
          item_type: (controllerRef.pickingMerchInfo as PickingMerch).item_type,
          bind_source: getBindApiParams(isLinkActive, appStateRef.current.activeTab.key),
        };
        const thirdSource = userActionProps.pick_third_source && BindThirdSource[userActionProps.pick_third_source];
        if (thirdSource) {
          bindParams.third_source = thirdSource;
        }
        bindGoodsToLiveNew(bindParams, false, (isGuideInfo: boolean, msg: string) => {
          updateAppState({
            isLoadingMaskActive: false,
            clipBoardModalVisible: false,
            isGoodInStarAtlasModalShow: false,
          });
          if (isGuideInfo) {
            return;
          }
          sendPickProductSuccessLog(controllerRef.pickingMerchInfo as PickingMerch, false);
          appendMerchToAppendedList(promotionId);
          // const toastMsg = msg || "商品已添加到购物袋";
          const toastMsg = '商品已添加到购物袋';
          setTimeout(() => showToast(toastMsg), 300);
          // 新版添品页 非链接和商品搜索场景 添加成功后关闭当前添品页 回到前置页
          if (appStateRef.current.isMerchV2) {
            if (!appStateRef?.current?.isLinkSearchViewActive && !appStateRef?.current?.isComboSearchViewActive) {
              setTimeout(() => {
                webcast?.app?.close();
              }, CLOSE_MERCH_PAGE_DELAY_TIME);
            }
          }
        });
      },
      hideGoodInStarAtlasModal() {
        updateAppState({
          isGoodInStarAtlasModalShow: false,
          isLoadingMaskActive: false,
        });
      },
      handleClickMachineAuditRule(title: string, url: string) {
        genMachineAuditRule(title, url);
      },
      onClosePopupLinkModal() {
        sendLog('livesdk_click_popup_button', {
          ...getCommonStatParams(),
          ...{
            popupfor: 'select_product',
            url: (controllerRef.popupLinkResult as IPopupResponse)?.contents?.url,
            id: (controllerRef.popupLinkResult as IPopupResponse)?.id,
            button_for: '我知道了',
          },
        });
        const [validPopupArrs] = getValidPopupArrs(controllerRef.popupLinkResult as IPopupResponse);
        localStorage.setItem(
          VALID_POPUP_ARRS_STORAGE,
          JSON.stringify([...validPopupArrs, controllerRef.popupLinkResult])
        );
        updateAppState({ isPopupLinkModalShow: false });
      },
      onConfirmPopupLinkModal() {
        sendLog('livesdk_click_popup_button', {
          ...getCommonStatParams(),
          ...{
            popupfor: 'select_product',
            url: (controllerRef.popupLinkResult as IPopupResponse)?.contents?.url,
            id: (controllerRef.popupLinkResult as IPopupResponse)?.id,
            button_for: '去看看',
          },
        });
        const [_, invalidPopupArrs] = getValidPopupArrs(controllerRef.popupLinkResult as IPopupResponse);
        localStorage.setItem(
          INVALID_POPUP_ARRS_STORAGE,
          JSON.stringify([...invalidPopupArrs, (controllerRef.popupLinkResult as IPopupResponse)?.id])
        );
        updateAppState({ isPopupLinkModalShow: false });
        via.app.openWebview({
          url: `${
            (controllerRef.popupLinkResult as IPopupResponse)?.contents?.url
          }?enter_from=e_commerce_select&hide_nav_bar=0&hide_status_bar=0&__live_platform__=webcast&status_bar_color=black&status_bar_bg_color=%23ffffff&web_bg_color=%23ffffff`,
        });
      },
      onAccountModalConfirm() {
        accountStateController.handleModalConfirm();
        updateAppState({ isAccountModalShow: false });
      },
      onAccountModalCancel() {
        accountStateController.handleModalCancel();
        updateAppState({ isAccountModalShow: false });
      },
      onAdModalConfirm(isConfirm) {
        updateAppState({ isShowAdModal: false });
        setAdAuth({
          status: isConfirm ? 2 : 1,
        })
          .then(res => {
            showToast('设置成功，可在“我-商品橱窗-常用服务-推广授权”查看或更改授权状态');
          })
          .catch(() => {});
      },
      onQualificationModalConfirm() {
        // 直播中 跳转全屏页需要主动发送 continue_push_stream 事件，否则直播画面会黑屏
        // PublishContinuePushStream里做了新版本和直播中状态的判断
        PublishContinuePushStream();
        openSchema({
          schema: genSchema({
            ...SchemaCommonParams,
            url: appStateRef.current.qualificationMsg.jump_url!,
          }),
        });
        // openWebview({
        //   url: appStateRef.current.qualificationMsg.jump_url!,
        //   showBack: true,
        // });
        updateAppState({
          isShowQualificationModal: false,
        });
      },
      onQualificationModalCancel() {
        updateAppState({
          isShowQualificationModal: false,
        });
      },
      onBindQualificationModalConfirm() {
        // openWebview({
        //   showBack: true,
        //   url: appStateRef.current.jumpBindQualificationUrl!
        // });
        openSchema({
          schema: genSchema({
            ...SchemaCommonParams,
            url: appStateRef.current.jumpBindQualificationUrl!,
          }),
        });
        updateAppState({
          isShowBindQualificationModal: false,
        });
      },
      onBindQualificationModalCancel() {
        updateAppState({
          isShowBindQualificationModal: false,
        });
      },
      onUpdateAnXinGoInfo() {
        getRemindMessage()
          .then(res => {
            const { tag_info, peace_switch_info, first_use_alert, is_show } = res || {};
            const jumpUrl = tag_info?.url || ''; // 提示跳转链接
            if (jumpUrl && jumpUrl.startsWith('http')) {
              tag_info.url = decodeURIComponent(tag_info.url);
            }
            // is_show代表版本支持且直播间类型支持
            // peace_switch_info?.open_status为1代表已经开启安心购
            const needFirstUseAlert = is_show && peace_switch_info?.open_status !== 1;
            if (JSON.stringify(tag_info) !== JSON.stringify(appStateRef.current.remindMessage.message)) {
              webcast.app.publishEvent({
                eventName: 'ecom.anchor.refresh_promotions',
                params: {},
              });
            }
            updateAppState({
              isRemindApiBack: true,
              remindMessage: {
                message: {} as any,
                firstUseAnXinGouModal: needFirstUseAlert ? first_use_alert : ({} as AnXinGouModalInfo),
                isShowAnXinGo: is_show,
              },
            });
          })
          .catch(() => {
            updateAppState({
              isRemindApiBack: true,
            });
          });
      },
      qualificationCheck() {
        // 资质检查
        qualificationCheck()
          .then(res => {
            const { need_upgrade, remind_text, level, jump_url } = res.data;
            updateAppState({
              isQualificationApiBack: true,
              qualificationMsg: {
                remind_text,
                level,
                jump_url,
                need_upgrade,
              },
              isShowQualificationModal: need_upgrade && level === QualificationLevelEnum.stop,
            });
          })
          .catch(() => {
            updateAppState({
              isQualificationApiBack: true,
              qualificationMsg: {
                remind_text: '',
                level: 0,
                jump_url: '',
                need_upgrade: false,
              },
              isShowQualificationModal: false,
            });
          });
      },

      checkPairLimit(data: Merch, index: number, callback: (bypassed: boolean) => void) {
        updateAppState({
          pairLimitConfirmInfo: {
            data,
            index,
            callback,
          },
        });
      },
      onPairLimitModalClose() {
        updateAppState({
          pairLimitConfirmInfo: null,
        });
      },
      openPairLimitSheets(info, bypassInfo: IBypassInfo) {
        updateAppState({
          pairLimitSheetsInfo: { ...info, ...bypassInfo },
        });
      },
      onPairLimitPopupClose() {
        updateAppState({
          pairLimitSheetsInfo: null,
        });
      },
      sendSelectAllLog(status: boolean) {
        const userActionProps = userActionRecord.getUserActionMap();
        sendLog('livesdk_pick_product_click_button', {
          ...userActionProps,
          ...getCommonStatParams(),
          edit_type: 'add_product',
          page_name: 'live_select_product',
          click_button: status ? '全选' : '取消全选',
        });
      },
      onCancelSelectAll(tabs: string) {
        if (!tabs) {
          return;
        }
        // 为了每次都能有效地触发全选取消，所以增加一个随机数
        updateAppState({ cancelSelectAllTab: `${tabs} ${Math.random()}` });
      },
      onClearSelectList(tab) {
        const selected = appStateRef.current.batchSelectedList;
        let deleteTabs = '';
        for (let i = selected.length - 1; i >= 0; i--) {
          if ((selected[i].fromTab || '').indexOf(tab.key) === -1) {
            continue;
          }
          deleteTabs += selected[i].fromTab || '';
          selected.splice(i, 1);
        }
        if (deleteTabs) {
          $app.onCancelSelectAll(deleteTabs);
        }
        updateAppState({ batchSelectedList: [...selected] });
      },
      onBatchSelectChange(merch: Merch, status: boolean, activeTab) {
        const selected = appStateRef.current.batchSelectedList;
        // 超过最大可选数量则不让选中
        const limit = Math.min(TOTAL_MAX_SELECT_COUNT - pickedPromotionsListRef.current.length, MAX_SELECT_COUNT);
        if (status && selected.length >= limit) {
          return;
        }
        const selectedIndex = selected.findIndex(item => item.promotion_id === merch.promotion_id);
        merch.fromTab = activeTab.key;
        merch.bind_source = getBindApiParams(false, activeTab.key);
        merch.tab_title = activeTab.title;
        if (status) {
          if (selectedIndex > -1) {
            // 如果已选列表里面已有，那么判断是否是在不同tab下面添加的，如果是的话得再加上新tab的信息（用于取消全选的时候用）
            selected[selectedIndex].fromTab =
              (selected[selectedIndex].fromTab || '').indexOf(activeTab.key) > -1
                ? selected[selectedIndex].fromTab
                : `${selected[selectedIndex].fromTab} ${activeTab.key}`;
          } else {
            // 如果已选列表里面没有，则直接添加
            selected.push(merch);
          }
        } else if (!status && selectedIndex > -1) {
          $app.onCancelSelectAll(selected[selectedIndex].fromTab || '');
          selected.splice(selectedIndex, 1);
        }
        updateAppState({ batchSelectedList: [...selected] });
      },
      // 页面底部批量添加
      onBatchAdd(selectAll, merchList) {
        if (!appStateRef.current.batchSelectedList?.length) {
          return;
        }
        sendLog('addproduct_click', { count: appStateRef.current.batchSelectedList?.length, page_name: '直播添品页' });
        // 先定义一个添加商品的方法，它在下面会被用到
        const addMerch = () => {
          // 智能直播货盘仅取出 promotionId 即可
          if (query.pick_mode === 'id') {
            const pickedPromotionIds = appStateRef.current.batchSelectedList.map(merch => merch.promotion_id);
            console.log('当前已选择', pickedPromotionIds);
            webcast.app.publishEvent(
              {
                eventName: 'submit_webcast_merchpicking',
                params: {
                  pickedPromotionIds: appStateRef.current.batchSelectedList.map(merch => merch.promotion_id),
                },
              },
              () => {
                webcast.app.close();
              }
            );
            return;
          }

          if (appStateRef.current.batchSelectedList.length === 1) {
            const merch = appStateRef.current.batchSelectedList[0];
            return $app.onMerchPick(merch, merch.index || 0, true);
          }

          let windowCount = 0;
          let latestBindCount = 0;
          let shopCount = 0;
          let copperOrderCount = 0;
          let exclusivePlanCount = 0;
          let starCount = 0;
          let pickingCartCount = 0;
          let recommendTabCount = 0;
          const batchList = (merchList || appStateRef.current.batchSelectedList).map(merch => {
            switch (merch.bind_source) {
              case BindSource.LatestBind:
                latestBindCount += 1;
                break;
              case BindSource.Shop:
                shopCount += 1;
                break;
              case BindSource.CopperOrder:
                copperOrderCount += 1;
                break;
              case BindSource.ExclusivePlan:
                exclusivePlanCount += 1;
                break;
              case BindSource.Star:
                starCount += 1;
                break;
              case BindSource.PickingCart:
                pickingCartCount += 1;
                break;
              case BindSource.LiveFeedTab:
                recommendTabCount += 1;
                break;
              case BindSource.Window:
              default:
                windowCount += 1;
                break;
            }
            return {
              promotion_id: merch.promotion_id,
              product_id: merch.product_id,
              item_type: merch.item_type,
              bind_source: merch.bind_source,
              tab_title: merch.tab_title, // 该参数后端不需要，只是埋点用
              tag_codes: merch.tag_codes,
            };
          });

          const userActionProps = userActionRecord.getUserActionMap();
          const batchLog = {
            zuijin_product_number: latestBindCount,
            chuchuang_product_number: windowCount,
            dianpu_product_number: shopCount,
            hezuo_product_number: copperOrderCount,
            zhuanshu_product_number: exclusivePlanCount,
            xingtu_product_number: starCount,
            xuanpinche_product_number: pickingCartCount,
            recommend_product_number: recommendTabCount,
            select_all: selectAll,
          };
          const logParams = {
            ...userActionProps,
            ...getCommonStatParams(),
            ...batchLog,
            first_source: getClickFirstSource(),
            second_source: appStateRef.current.activeTab.title,
            commodity_id: batchList.map(merch => merch.promotion_id).join(','),
            product_id: batchList.map(merch => merch.product_id).join(','),
            commodity_type: batchList.map(merch => merch.item_type).join(','),
            click_type: 'add',
            edit_type: 'add_product',
            page_name: 'live_select_product',
            btm: getBtm(),
            pick_third_source: query?.pick_third_source,
          };
          sendLog('livesdk_pick_product', logParams);
          liveBatchBind(batchList)
            .then(res => {
              batchAddCallBack(res, merchList || appStateRef.current.batchSelectedList, {
                logParams,
              });
            })
            .catch(res => {
              showToast(res?.msg || '网络错误，请重试');
            });
        };
        // 计算是否存在需要弹窗的商品
        const invoiceNoticeList = makeNoticeItems(appStateRef.current.batchSelectedList);
        // 当存在需弹窗商品且不在七天限制之内
        if (invoiceNoticeList.length !== 0 && computeNeedInvoiceShow()) {
          InvoiceDrawerShow({ noticeItems: invoiceNoticeList, confirmModal: addMerch });
          return;
          // 中断后续流程
        }
        addMerch();
      },
      onHideBatchModal() {
        updateAppState({ isShowBatchResultModal: false });
      },
      async checkAlert(successCb?: () => void) {
        const cache = localStorage.getItem(ALERT_CACHE_KEY) ?? undefined;
        const tmp = await getStorageString(ALERT_CACHE_KEY).catch(() => '');
        const newCache = tmp || cache;
        checkCommonAlert({ scene_type: SceneType.Live, room_id: query.room_id, shield_tip: newCache })
          .then(res => {
            const { data } = res || {};
            updateAppState({ commonAlertInfo: data });
            successCb?.();
          })
          .finally(() => {
            updateAppState({ isRemindApiBack: true });
          });
      },
      mixController: {
        // 打开一个portal，里面请求关联商品
        async onClickAggregateButton(merch, options) {
          updateAppState({
            aggregatePromotionModalState: {
              data: {
                product_id: merch.product_id,
                promotion_id: merch.promotion_id,
              },
              options: {
                getTipTitle: () => '以下为专场中合作店铺的小时达商品',
              },
            },
          });
        },
        closeAggrPromotion() {
          updateAppState({
            aggregatePromotionModalState: undefined,
          });
        },
      },
    };
  }, [defaultTab]);

  const getValidPopupArrs = (popRes: IPopupResponse): [IPopupResponse[], string[]] => {
    // localStorage.clear();
    try {
      const popupArrsRes = JSON.parse(localStorage.getItem(VALID_POPUP_ARRS_STORAGE) || '[]') as IPopupResponse[];

      let validPopupArrs: IPopupResponse[] = popupArrsRes;
      const findPopup = popupArrsRes?.find(item => item.id === popRes.id);
      if (findPopup) {
        if (Number(findPopup.expire_time) <= Number(popRes.now_time)) {
          validPopupArrs = popupArrsRes?.filter(item => item.id !== popRes.id);
        }
      }

      const invalidPopupArrs = JSON.parse(localStorage.getItem(INVALID_POPUP_ARRS_STORAGE) || '[]') as string[];

      return [validPopupArrs, invalidPopupArrs];
    } catch (error) {
      localStorage.removeItem(INVALID_POPUP_ARRS_STORAGE);
      localStorage.removeItem(VALID_POPUP_ARRS_STORAGE);
      return [[], []];
    }
  };

  /**
   * 弹窗逻辑
   */
  const handleFetchPopupLink = async () => {
    if (!isInAweme) {
      updateAppState({ isCheckUnion: true });
      return;
    }
    if (isLiving) {
      updateAppState({ isCheckUnion: true });
      return;
    }
    try {
      const popRes = await fetchPopup({ pop_type: 'live_pick' });
      updateAppState({ isCheckUnion: true });
      const [validPopupArrs, invalidPopupArrs] = getValidPopupArrs(popRes);
      const { id } = popRes;
      if (
        id &&
        popRes?.contents?.url &&
        validPopupArrs.map(item => item.id).indexOf(id) < 0 &&
        invalidPopupArrs.indexOf(id) < 0
      ) {
        controllerRef.popupLinkResult = popRes;
        updateAppState({ isPopupLinkModalShow: true, popupLinkResult: popRes });
        localStorage.setItem(VALID_POPUP_ARRS_STORAGE, JSON.stringify([...validPopupArrs, popRes]));
        sendLog('livesdk_show_popup', {
          ...getCommonStatParams(),
          ...{
            popupfor: 'select_product',
            url: popRes?.contents?.url,
            id: popRes?.id,
          },
        });
      }
    } catch (error) {
      updateAppState({ isPopupLinkModalShow: false, isCheckUnion: true });
    }
  };

  useEffect(() => {
    updateAppState({
      isCheckAdBack: accountStateRef.isCheckApiAdBack,
      isShowAdModal: accountStateRef.adAuthStatus,
    });
  }, [accountStateRef.isCheckApiAdBack, accountStateRef.adAuthStatus]);

  useEffect(() => {
    updateAppState({
      tabList: liveTabList,
    });
  }, [liveTabList]);
  /**
   * 弹窗逻辑
   */
  useEffect(() => {
    // updateAppState({ isShowAdModal: true });
    userActionRecord.setUserAction({ actionLevel2: liveTabList[0]?.title });
    handleFetchPopupLink();
  }, []);

  useEffect(() => {
    document.addEventListener('visibilitychange', function () {
      if (document.visibilityState === 'visible') {
        // 抖音从编辑页回到选品判断是否添加了商品
        if (appStateRef.current.isBackFromGoodEditor) {
          updateAppState({
            isBackFromGoodEditor: false,
            clipBoardModalVisible: false,
          });
          via.app.getStorage(
            {
              key: 'live_good_add_from_editor',
            },
            (_: any, { code, value }: { code: number; value: string }) => {
              if (code === 1 && Boolean(value)) {
                const userActionProps = userActionRecord.getUserActionMap();
                const bindCallBack = (isGuideInfo: boolean, showMsg: string) => {
                  sendPickProductSuccessLog(controllerRef.pickingMerchInfo as PickingMerch, false);
                  appendMerchToAppendedList(value);
                  via.app.getStorage(
                    {
                      key: 'is_skip_bind_shop',
                    },
                    (_: any, { code, value }: { code: number; value: string }) => {
                      via.app.setStorage({
                        key: 'is_skip_bind_shop',
                        value: '',
                      });
                      if (isGuideInfo) {
                        return;
                      }
                      let msg = '商品已同时添加到购物袋和橱窗';
                      if (code === 1 && value === 'true') {
                        msg = '商品已添加到购物袋';
                      }
                      const toastMsg = showMsg || msg;
                      showToast(toastMsg);
                    }
                  );
                };
                const isLinkActive =
                  appStateRef.current.isLinkSearchViewActive || Boolean(appStateRef.current.isSkipFromClipBoard);
                const bindParams: LiveBindOperatePromotionParams = {
                  promotion_id: (controllerRef.pickingMerchInfo as PickingMerch).promotion_id,
                  product_id: (controllerRef.pickingMerchInfo as PickingMerch).product_id,
                  item_type: (controllerRef.pickingMerchInfo as PickingMerch).item_type,
                  bind_source: getBindApiParams(isLinkActive, appStateRef.current.activeTab.key),
                };
                const thirdSource =
                  userActionProps.pick_third_source && BindThirdSource[userActionProps.pick_third_source];
                if (thirdSource) {
                  bindParams.third_source = thirdSource;
                }
                bindGoodsToLiveNew(bindParams, false, bindCallBack);
                via.app.setStorage({
                  key: 'live_good_add_from_editor',
                  value: '',
                });
              }
              if (appStateRef.current.isSkipFromClipBoard) {
                updateAppState({ isSkipFromClipBoard: false });
                userActionRecord.fallbackRecord();
              }
            }
          );
        }
        // 从已选商品页回来，刷新已选的list
        // 同时刷新安心购状态
        if (appStateRef.current.isBackFromPicked) {
          if (!isInSAAS) {
            $app.onUpdateAnXinGoInfo();
          }
          fetchPickedMerchList()
            .then(res => {
              // eslint-disable-next-line max-nested-callbacks
              const promotionIdList = res.items.map(item => item.promotion_id);
              setAppendedList(promotionIdList);
              updateAppState({ isBackFromPicked: false });
            })
            .catch(() => {});
        }
      }
    });

    via.app
      .getUserInfo()
      .then((res: UserInfo) => {
        const { code, user_id } = res;
        if (code === 1) {
          controllerRef.userId = user_id;
        }
        sendLog('livesdk_select_product_view', {
          ...getCommonStatParams(),
        });
      })
      .catch(() => {});
    controllerRef.live_status = !isLiving ? 'live_before' : 'live_on';
    return () => {
      document.removeEventListener('visibilitychange', () => {});
    };
  }, []);
  useEffect(() => {
    if (globalProps?.queryItems?.type === 'popup' || query?.type === 'popup') {
      updateAppState({ isMerchV2: true });
    }
    if (!tabListReady) {
      updateAppState({ isCheckPaste: true });
      return;
    }
    if (defaultTab.key) {
      updateAppState({
        activeTab: defaultTab,
        activeSearchTab: defaultTabByFE,
        autoSelectAll: defaultTab.key === TAB_KEYS.RECOMMENDED_PRODUCT ? true : false,
      });
    }
    // 抖音 && 有星图的权限, 就不再识别粘贴板
    if (isInAweme && liveTabList[0]?.key === TAB_KEYS.STAR_ATLAS) {
      updateAppState({ activeTab: liveTabList[0], isCheckPaste: true });
    } else {
      via.app.fetchClipBoard().then((res: { content: string }) => {
        const { content } = res;
        if (content) {
          searchMerchListByLink(content)
            .then(res => {
              updateAppState({
                isCheckPaste: true,
                clipBoardModalVisible: true,
                clipboardPromotionInfo: res.items[0],
              });
              sendLinkSearchLog(
                EVENTNAME.LINKSEARCHCLICK,
                {
                  link_content: content,
                  source: LinkSearchScene.mobileLiveClipBoard,
                },
                controllerRef.userId
              );
              userActionRecord.setUserAction({
                actionLevel3: UserActionThirdEnum.linkAddResult,
              });
              sendLog('livesdk_show_popup', {
                ...getCommonStatParams(),
                popup: 'product_link',
              });
              via.app.copyToClipboard({
                content: '',
              });
            })
            .catch(_ => {
              updateAppState({ isCheckPaste: true });
            });
          return;
        }
        updateAppState({ isCheckPaste: true });
      });
    }
  }, [tabListReady]);

  const getClickFirstSource = () => {
    const { isComboSearchViewActive, isLinkSearchViewActive, clipBoardModalVisible } = appStateRef.current;
    if (isComboSearchViewActive) {
      return 'search';
    } else if (isLinkSearchViewActive) {
      return 'copy_link';
    } else if (clipBoardModalVisible) {
      return 'link_popup';
    } else {
      return 'index';
    }
  };

  const sendPickProductSuccessLog = useCallback((merch: PickingMerch, isCancel: boolean) => {
    sendLog('livesdk_pick_product_success', {
      ...getCommonStatParams(),
      first_source: getClickFirstSource(),
      second_source: appStateRef.current.activeTab.title,
      commodity_id: merch.promotion_id,
      product_id: merch.product_id,
      commodity_type: merch.item_type,
      click_type: isCancel ? 'cancel' : 'add',
    });
    sendLog('pick_product', {
      ...getCommonStatParams(),
      first_source: getClickFirstSource(),
      second_source: appStateRef.current.activeTab.title,
      commodity_id: merch.promotion_id,
      product_id: merch.product_id,
      commodity_type: merch.item_type,
      click_type: isCancel ? 'cancel' : 'add',
      edit_type: isCancel ? 'cancel' : 'add_product',
      btm: getBtm(),
      pick_third_source: query?.pick_third_source,
      price_level: getPriceLevel(merch.tag_codes || []),
    });
  }, []);

  // 添加商品绑定直播间
  const bindGoodsToLiveNew = useCallback(
    (
      { promotion_id, product_id, ...rest }: LiveBindOperatePromotionParams,
      isCancel?: boolean,
      callback?: (isGuideInfo: boolean, msg: string) => void,
      options?: {
        resCallback?: (res: BatchBindResponse) => void;
      }
    ) => {
      const baseParams = { promotion_id, product_id, ...rest };
      const bindFunc = isCancel ? unbindPromotionToLive(baseParams) : liveBatchBind([baseParams]);
      bindFunc
        .then(_ => {
          options?.resCallback?.(_);
          const { enter_from } = query;
          if (enter_from === 'webcast_living') {
            via.business.refreshPromotions({ need_refresh: true });
          } else {
            const currentLength = pickedPromotionsListRef.current.length;
            const length = isCancel ? currentLength - 1 : currentLength + 1;
            via.business.modifiedPromotions({
              promotion_num: length,
            });
          }
          if (appStateRef.current.isMerchV2) {
            webcast.app.publishEvent({
              eventName: 'ecom.anchor.refreshPickedView',
              params: {},
            });
          }
          const { data } = _ as { data?: BindResults };
          const {
            failure_count,
            partial_failure_count,
            success_count,
            failure_list,
            partial_failure_guide_info, // 单品失败
            partial_failure_list,
            oversold_remaind_result,
            new_guide_info, // 新版单个商品[完全失败]详情
          } = data || {};
          const guideInfo = data && data.guide_info;
          const isGuideInfo = Boolean(guideInfo);
          const isToast = data?.is_toast;
          const showBatchModal =
            new_guide_info || failure_list?.length || partial_failure_list?.length || partial_failure_guide_info;
          if (!isCancel) {
            // 上报添品结果 统计添品成功率
            sendLog('add_product_result_collect', {
              page_name: '移动端-直播添品页',
              add_type: 'single',
              success_count: data?.success_count || 0,
              failure_count: data?.failure_count || 0,
              total_count: (data?.success_count || 0) + (data?.failure_count || 0),
            });
          }

          if (!isToast && (guideInfo || showBatchModal)) {
            sendLog('addproduct_errorpop_show', {
              ...getCommonStatParams(),
              page_name: '直播添品页',
              live_type: !isLiving ? 'off' : 'on',
              errcode: guideInfo?.err_code || '',
              title: guideInfo?.title || '',
              message: guideInfo?.content || '',
              button_for: guideInfo?.confirm_text || '',
              button_url: guideInfo?.guide_link || '',
            });
            if (showBatchModal) {
              let partialFailureList = [...(partial_failure_list || [])];
              if (partial_failure_guide_info) {
                partialFailureList = [partial_failure_guide_info];
              }
              let failureList = [...(failure_list || [])];
              if (new_guide_info) {
                failureList = [new_guide_info];
              }
              // 如果有添加失败的商品，显示批量绑定结果信息弹窗
              updateAppState({
                isShowBatchResultModal: true,
                batchResultModal: {
                  successCount: success_count || 0,
                  failureCount: failure_count || 0,
                  failureList: failureList,
                  oversoldList: oversold_remaind_result || [],
                  partialFailureList,
                },
              });
            } else {
              updateAppState({
                isShowCommonModal: true,
                commonModal: guideInfo,
                isCancelBindToLiveModalShow: false,
                isLoadingMaskActive: false,
              });
            }
          }
          if (isToast) {
            // 添加走新接口，toast内容取guideInfo?.content
            const toastContent = isCancel ? data?.bind_reason : guideInfo?.content;
            showToast(toastContent || '添加或取消绑定商品到直播间失败');
          }
          if (callback) {
            callback(showBatchModal || isGuideInfo || isToast, data && data.bind_msg);
          }
        })
        .catch(error => {
          updateAppState({
            isCancelBindToLiveModalShow: false,
            isLoadingMaskActive: false,
          });
          const { data } = error;
          const guideInfo = data && data.guide_info;
          const isToast = data?.is_toast;
          if (isToast) {
            showToast(data?.bind_reason || '添加或取消绑定商品到直播间失败');
            return;
          }
          if (!isToast && guideInfo) {
            updateAppState({
              isShowCommonModal: true,
              commonModal: guideInfo,
            });
            return;
          }
          showToast(error?.status_msg || '添加或取消绑定商品到直播间失败');
        });
    },
    []
  );

  return [$app, appStateRef.current, liveTabList, appendedListState, isAB];
}
