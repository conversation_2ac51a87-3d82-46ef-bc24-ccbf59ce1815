import { useRef, useState } from 'react';
import { SortingOption } from '../types';
import { isTaobaoMerchLink } from '../services/utils';
import { SEARCH_HISTORY_LS_KEY } from '../constants';

interface LinkSearchSession {
    searchText: string;
}

interface LinkSearchState {
    searchSession: LinkSearchSession;
    searchFailReason: string;
    isTaobaoMerchLink: boolean;
}

interface ComboSearchSession {
    searchText: string;
    sortingOption: SortingOption | null;
}

interface ComboSearchState {
    searchHistory: string[];
    sortingOptions: SortingOption[];
    searchSession: ComboSearchSession;
    searchFailReason: string;
}

type LinkSearchStatePatch = Partial<LinkSearchState>;

type ComboSearchStatePatch = Partial<ComboSearchState>;

type SearchTextCommitter = (searchText: string) => void;

const initialSearchHistory = (() => {
    try {
        const raw = localStorage.getItem(SEARCH_HISTORY_LS_KEY) || '[]';
        return JSON.parse(raw);
    } catch (e) {
        return [];
    }
})();

const pristineComboSearchSession: ComboSearchSession = {
    searchText: '',
    sortingOption: null
};

const pristineLinkSearchSession: LinkSearchSession = {
    searchText: ''
};

const pristineSortingOptions: SortingOption[] = [
    { key: '', title: '综合', reverse: false },
    { key: 'cos_fee', title: '佣金', reverse: true },
    { key: 'sales', title: '销量', reverse: true },
    { key: 'price', title: '价格', reverse: false, initReverse: false, shiftable: true }
];

export function useLinkSearchState(): [LinkSearchState, SearchTextCommitter, Function, Function] {
    const [state, setState] = useState<LinkSearchState>({
        searchFailReason: '',
        isTaobaoMerchLink: false,
        searchSession: pristineLinkSearchSession
    });

    const { current } = useRef({
        state: { ...state },
        update(patch: LinkSearchStatePatch) {
            Object.assign(current.state, patch);
            setState({ ...current.state });
        },
        setSearchFailReason(searchFailReason: string) {
            current.update({ searchFailReason });
        },
        setAsTaobaoMerchLink() {
            current.update({ isTaobaoMerchLink: true });
        },
        commitSearchText(searchText: string) {
            current.update({
                searchSession: { searchText },
                searchFailReason: '',
                isTaobaoMerchLink: isTaobaoMerchLink(searchText)
            });
        }
    });

    return [
        current.state,
        current.commitSearchText,
        current.setSearchFailReason,
        current.setAsTaobaoMerchLink
    ];
}

export function useComboSearchState(): [ComboSearchState, Function, Function, Function, Function] {
    const [state, setState] = useState<ComboSearchState>({
        searchFailReason: '',
        searchHistory: initialSearchHistory,
        sortingOptions: pristineSortingOptions,
        searchSession: pristineComboSearchSession
    });

    const { current } = useRef({
        state: { ...state },
        update(patch: ComboSearchStatePatch) {
            Object.assign(current.state, patch);
            setState({ ...current.state });
        },
        updateSearchHistory(searchHistory: string[]) {
            current.update({ searchHistory });
            localStorage.setItem(SEARCH_HISTORY_LS_KEY, JSON.stringify(searchHistory));
        },
        addSearchHistory(item: string) {
            if (!item || !item.trim()) {
                return;
            }
            const history = [...current.state.searchHistory];
            const index = history.indexOf(item);
            if (index !== -1) {
                history.splice(index, 1);
            }
            current.updateSearchHistory([item, ...history].slice(0, 10));
        },
        clearSearchHistory() {
            current.updateSearchHistory([]);
        },
        commitSearchText(searchText: string) {
            const { searchSession, sortingOptions } = current.state;
            if (searchText !== searchSession.searchText) {
                current.addSearchHistory(searchText);
            }
            current.update({
                searchFailReason: '',
                searchSession: {
                    searchText,
                    sortingOption: sortingOptions[0]
                }
            });
        },
        commitSortingOption(option: SortingOption) {
            let selectedSortingOption = option;
            let { searchSession, sortingOptions } = current.state;
            if (option.shiftable) {
                selectedSortingOption = {
                    ...option,
                    reverse: !option.reverse
                };
                if (
                    option !== searchSession.sortingOption &&
                    typeof option.initReverse === 'boolean'
                ) {
                    selectedSortingOption.reverse = option.initReverse;
                }
                sortingOptions = sortingOptions.map(sortingOption => {
                    if (sortingOption !== option) {
                        return sortingOption;
                    }
                    return selectedSortingOption;
                });
            }
            current.update({
                sortingOptions,
                searchSession: {
                    ...searchSession,
                    sortingOption: selectedSortingOption
                }
            });
        },
        setSearchFailReason(searchFailReason: string) {
            current.update({ searchFailReason });
        }
    });

    return [
        current.state,
        current.commitSearchText,
        current.commitSortingOption,
        current.setSearchFailReason,
        current.clearSearchHistory
    ];
}
