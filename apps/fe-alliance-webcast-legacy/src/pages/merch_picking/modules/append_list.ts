/**
 * 用来记录已经绑定到直播间的商品的custom hooks @douyanlin
 * 初始化从接口获取绑定过的商品
 */
import { useState, useRef, useEffect, useCallback } from 'react';
import webcast from '@bridge/webcast';
import { getQuery } from '@src/lib/env_detect';
import { PromotionIdType, RouteQuery } from '../types';
import { fetchPickedMerchList } from '../services/api';
const initialState: PromotionIdType[] = [];

const query = getQuery<RouteQuery>();

export type HandleMerchFunction = (id: PromotionIdType) => void;

export function useAppendedList(): [
  { current: PromotionIdType[] },
  HandleMerchFunction,
  HandleMerchFunction,
  PromotionIdType[],
  (promotionIds: PromotionIdType[]) => void,
  { current: boolean }
] {
  const [appendedList, setAppendedList] = useState(initialState);
  const fetchedPickedList = useRef(true); // fetchingPickedList

  const pickedPromotionsListRef = useRef(appendedList);

  useEffect(() => {
    let unsubscribe;
    if (query.pick_mode === 'id') {
      unsubscribe = webcast.app?.subscribeEvent?.({
        eventName: 'init_webcast_merchpicking',
        callback(res) {
          if (!res?.pickedPromotionIds?.length) return;
          setAppendedList(res.pickedPromotionIds);
          pickedPromotionsListRef.current = res.pickedPromotionIds;
          fetchedPickedList.current = false;
        },
      });
    } else {
      fetchPickedMerchList()
        .then(res => {
          const promotionIdList = res.items.map(item => item.promotion_id);
          setAppendedList(promotionIdList);
          pickedPromotionsListRef.current = promotionIdList;
          fetchedPickedList.current = false;
        })
        .catch(() => {
          fetchedPickedList.current = false;
        });
    }
    return () => {
      unsubscribe?.();
    };
  }, []);

  const appendMerchToList = useCallback((id: PromotionIdType) => {
    const list = pickedPromotionsListRef.current;
    const newList = [id, ...list];
    setAppendedList(newList);
    pickedPromotionsListRef.current = newList;
  }, []);

  const removeMerchFromList = useCallback((promotionId: PromotionIdType) => {
    const list = pickedPromotionsListRef.current;
    const foundIndex = list.findIndex(id => id === promotionId);
    list.splice(foundIndex, 1);
    const newList = [...list];
    setAppendedList(newList);
    pickedPromotionsListRef.current = newList;
  }, []);

  const coverAppendedList = useCallback((promotionIds: PromotionIdType[]) => {
    setAppendedList(promotionIds);
    pickedPromotionsListRef.current = promotionIds;
  }, []);

  return [
    pickedPromotionsListRef,
    appendMerchToList,
    removeMerchFromList,
    appendedList,
    coverAppendedList,
    fetchedPickedList,
  ];
}
