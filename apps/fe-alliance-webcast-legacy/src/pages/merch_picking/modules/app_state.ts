/**
 * 定义当前页面State的hooks @douyanlin
 */
import { useRef, useState } from 'react';
import { useDefaultTab } from '../services/hooks';
import {
  AccountModalTextInfo,
  AppState,
  AppStatePatcher,
  CommonModalInfo,
  IPopupResponse,
  QualificationMsg,
  RemindMessage,
  batchResultModalInfo,
  PortalTab,
} from '../types';

const defaultMerch = {
  promotion_id: '',
  cover: '',
  product_id: '',
  title: '',
  elastic_title: '',
  price: 0,
  market_price: 0,
  cos_fee: 0,
  detail_url: '',
  sales: 0,
  item_type: 1,
  cos_ratio: 0,
  favor: false,
  in_shop: false,
  platform_label: '',
  selling_point: '',
  coupon_amount: 0,
};
export default function (): [{ current: AppState }, AppStatePatcher] {
  const defaultTab = useDefaultTab();

  const [appState, setAppState] = useState<AppState>({
    isAuthorityOutdated: false,
    isLoadingMaskActive: false,
    isComboSearchViewActive: false, // 搜索页（历史搜索）
    isLinkSearchViewActive: false, // 通过链接添加商品
    isSearchHelpViewActive: false, // 搜索 - 帮助
    isTaobaoAuthConfirmActive: false, // 淘宝pid绑定弹窗是否展示
    isBackFromTaobaoAuth: false,
    isBackFromAllianceAuth: false,
    isMerchPickForbidden: false,
    merchPickForbiddenReason: '',
    clipBoardModalVisible: false, // 剪贴板识别商品弹窗
    activeTab: defaultTab,
    activeSearchTab: defaultTab,
    isBackFromGoodEditor: false,
    clipboardPromotionInfo: defaultMerch,
    isPickFailedModalShow: false,
    pickFailedModalText: '添加失败，请重试',
    isYMaTouAuthConfirmActive: false,
    isCancelBindToLiveModalShow: false,
    isClipboardModalAuditing: false,
    isBackFromPicked: false,
    isGoodInStarAtlasModalShow: false,
    isSkipFromClipBoard: false,
    machineAudit: {},
    isPopupLinkModalShow: false,
    popupLinkResult: {} as IPopupResponse,
    isAccountModalShow: false,
    accountModalTextInfo: {} as AccountModalTextInfo,
    isShowOpenOverseasAccount: false,
    isShowGoodsLimitModal: false,
    isShowFirstAddOverseas: false,
    isShowAdModal: false,
    isCheckAdBack: false,
    isCheckPaste: false,
    isCheckUnion: false,
    remindMessage: {} as RemindMessage,
    commonModal: {} as CommonModalInfo,
    isShowCommonModal: false,
    qualificationMsg: {} as QualificationMsg,
    isRemindApiBack: false,
    isQualificationApiBack: false,
    isShowQualificationModal: false,
    isShowBindQualificationModal: false,
    jumpBindQualificationUrl: '',
    bindQualificationContent: '',
    isPairLimitTipApiBack: false,
    pairLimit: null,
    pairLimitConfirmInfo: null,
    pairLimitSheetsInfo: null,
    batchSelectedList: [],
    batchResultModal: {} as batchResultModalInfo,
    isShowBatchResultModal: false,
    commonAlertInfo: undefined,
    cancelSelectAllTab: '',
    isMerchV2: false,
    tabList: [] as PortalTab[],
    autoSelectAll: false,
    showAddRecommend: false,
    aggregatePromotionModalState: undefined,
  });

  const stateRef = useRef(appState);

  function updateAppState(patch: Partial<AppState>) {
    const newState = { ...stateRef.current, ...patch };
    stateRef.current = newState;
    setAppState(newState);
  }

  return [stateRef, updateAppState];
}
