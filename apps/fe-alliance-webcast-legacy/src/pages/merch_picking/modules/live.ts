/**
 * 本文件是直播选品相关 custom hooks @douyanlin
 */
import { useState, useEffect } from 'react';
import { fetchLiveTabList } from '../services/api';
import { LiveTabListRequestBusinessScene, PortalTab, TabStruct } from '../types';
import { TAB_KEYS, UserActionSecondEnum } from '../constants';
import { isInAweme } from '@src/lib/env_detect';
import { userActionRecord } from '../services/utils';
import { useDefaultTab } from '../services/hooks';
import { formatQueryString } from '@src/lib/util';
import { PerformanceLifecycle, remewReport } from '../perf';

const tabKeyMap: { [key: string]: string } = {
  '8': TAB_KEYS.HISTORY_ADD, // 最近添加
  '4': TAB_KEYS.SHOPWINDOW, // 我的橱窗
  '3': TAB_KEYS.STORE, // 我的小店
  '1': TAB_KEYS.EXCLUSIVE, // 专属商品
  '7': TAB_KEYS.STAR_ATLAS, // 星图平台
  '16': TAB_KEYS.MAGPIE_BRIDGE, // 鹊桥商品
  '11': TAB_KEYS.PROTOCOL, // 合作订单商品
  '20': TAB_KEYS.PICKING_CART, // 选品车
  '21': TAB_KEYS.RECOMMENDED_PRODUCT, // 推荐商品
  '25': TAB_KEYS.ORIENTATION, // 定向商品
};

function tabAdapter(tab: TabStruct, defaultRenderKey: string): PortalTab {
  const { tab_id, name } = tab;
  const title = Object.keys(tab)[0];
  const type = tab[title];
  const result: PortalTab = {
    key: tabKeyMap[tab_id.toString()],
    title: name,
    isRendered: tabKeyMap[tab_id.toString()] === defaultRenderKey,
    type,
  };
  return result;
}

export function useLiveTabList(): [PortalTab[], boolean, PortalTab, boolean] {
  const { init_tab } = formatQueryString();
  const scene = init_tab === 'recommend' ? LiveTabListRequestBusinessScene.LiveControlFeed : undefined;
  const defaultTab = useDefaultTab();
  const initialState: PortalTab[] = [defaultTab];
  const [state, setState] = useState<PortalTab[]>(initialState);
  const [tabListReady, setTabListReady] = useState(false);
  const [AnchorTab, setAnchorTab] = useState(defaultTab);
  const [isAB, setIsAB] = useState(false);

  useEffect(() => {
    if (defaultTab.key) {
      remewReport.addPerformanceInfo('fetchLiveTabList', PerformanceLifecycle.T_MAIN_FETCH_START, Date.now());
      fetchLiveTabList('live', scene, true)
        .then(res => {
          remewReport.addPerformanceInfo('fetchLiveTabList', PerformanceLifecycle.T_MAIN_FETCH_END, Date.now());
          const tabs = res?.tab_list || [];
          setIsAB(Boolean(res?.extra?.live_recommend_tab_ab === '1'));
          const defaultTabIndex = tabs?.findIndex(item => item.anchored);
          let defaultRenderKey = defaultTab.key;
          if (defaultTabIndex > -1) {
            defaultRenderKey = tabKeyMap[tabs[defaultTabIndex].tab_id.toString()];
            setAnchorTab(tabAdapter(tabs[defaultTabIndex], defaultRenderKey));
          } else {
            const { tab_id, name } = tabs?.[0] || {};
            setAnchorTab({
              key: tabKeyMap[tab_id],
              title: name,
              isRendered: true,
              type: tab_id,
            });
          }
          let result = tabs?.map(item => tabAdapter(item, defaultRenderKey)) || [];
          // 抖音内 -> 星图tab -> 移到第一位
          if (isInAweme) {
            const starTab = result.filter(item => item.type === 7);
            if (starTab.length === 1) {
              result = starTab.concat(
                result.filter(item => {
                  const isStar = item.type !== 7;
                  item.isRendered = !isStar || item.type === 1;
                  return isStar;
                })
              );
              // 设置二级来源为星图
              userActionRecord.setUserAction({
                actionLevel2: UserActionSecondEnum.starPromotion,
              });
            }
          }
          if (result.length === 1) {
            result[0].isRendered = true;
          }
          setState(result);
          setTabListReady(true);
        })
        .catch(() => {
          setState([defaultTab]);
          setTabListReady(true);
        });
    }
  }, [defaultTab]);
  return [state, tabListReady, AnchorTab, isAB];
}
