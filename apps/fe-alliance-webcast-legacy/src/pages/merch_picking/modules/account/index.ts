import { AccountState, AccountStateController, AccountModalTextInfo } from './types';

import { openSchema, showToast } from "@src/common/bridge";
import { useState, useRef, useEffect } from 'react';
import {
    ALLIANCE_INCOME_ACCOUNT_AUTHED,
    ALLIANCE_ACCOUNT_NEXT_PAGE_SCHEMA,
    hasOldAccountModalInfo,
    hasSubmittedQualificationModalInfo,
    overseasAccountModalInfo,
    hasNoOldAccountModalInfo,
    ALLIANCE_ACCOUNT_UPGRADE_HAS_SHOWN
} from './constants';
import * as api from './api';
import { isInAweme, isInToutiao } from '@src/lib/env_detect';

const initialState: AccountState = {
    wxOpenAccount: false,
    alipayOpenAccount: false,
    paOpenAccount: false,
    isSubmitQualification: false,
    isCheckQualification: false,
    hasOpenAccountInfo: false,
    allianceUpgradeModalHasShown: false,
    hasBindAccountPhoneNumber: false,
    backFromAccountPage: false,
    hasOverseasShop: false,
    currentGoodsIsOverseas: false,
    adAuthStatus: false,
    isCheckApiAdBack: false,

};

export function useAccounts(): [AccountState, AccountStateController] {
    const [state, setState] = useState(initialState);

    const { current: ref } = useRef({
        state: { ...state },
        patchState(s: Partial<AccountState>) {
            Object.assign(ref.state, s);
            setState({ ...ref.state });
        }
    });

    const controller: AccountStateController = {
        async fetchAllAccountsInfo() {
            if (localStorage.getItem(ALLIANCE_ACCOUNT_UPGRADE_HAS_SHOWN)) {
                ref.patchState({ allianceUpgradeModalHasShown: true });
            }
            if (isInAweme){
                const response = await Promise.all([
                    api.fetchAccountStates(),
                    api.fetchQualification(),
                    api.fetchAllianceIncomeAccountStatus(),
                    api.fetchOpenAccountInfo()
                ]);
                const [
                    accountStates,
                    qualificationInfo,
                    allianceIncomeAccount,
                    openAccountInfo
                ] = response;
                const {
                    wx_status: wxOpenAccountStatus,
                    alipay_status: alipayOpenAccountStatus,
                    pa_status: paOpenAccountStatus
                } = accountStates || {};
                const {
                    submit_qualification_status: submitQualificationStatus,
                    check_qualification_status: checkQualificationStatus,
                    has_haitao_shop: hasHaiTaoShop,
                    ad_auth_status: adAuthStatus,  //0:未选择(默认) 1:每次询问 2:始终允许
                } = qualificationInfo || {};
                const { real_status } = allianceIncomeAccount;
                const { phone_num } = openAccountInfo || {};
                ref.patchState({
                    isCheckApiAdBack: true,
                    wxOpenAccount: wxOpenAccountStatus !== 0,
                    alipayOpenAccount: alipayOpenAccountStatus !== 0,
                    paOpenAccount: paOpenAccountStatus !== 0,
                    isSubmitQualification: submitQualificationStatus !== 0,
                    isCheckQualification: checkQualificationStatus !== 0,
                    hasOpenAccountInfo: real_status === ALLIANCE_INCOME_ACCOUNT_AUTHED,
                    hasBindAccountPhoneNumber: !!phone_num,
                    hasOverseasShop: !!hasHaiTaoShop,
                    adAuthStatus: adAuthStatus === 0,
                });
            }else{
                const response = await api.fetchAllianceIncomeAccountStatus();                
                const { real_status } = response;
                ref.patchState({
                    isCheckApiAdBack: true,
                    hasOpenAccountInfo: real_status === ALLIANCE_INCOME_ACCOUNT_AUTHED,
                });
            }
           
            
           
        },
        isAccountModalPopup(isOverseas: boolean) : {
            showAccountModal : boolean,
            accountModalTextInfo?: AccountModalTextInfo
        } {
            // 抖音判断逻辑
            if(isInAweme){
                const {
                    hasOpenAccountInfo,
                    isSubmitQualification,
                    isCheckQualification,
                    wxOpenAccount,
                    paOpenAccount,
                    alipayOpenAccount,
                    allianceUpgradeModalHasShown,
                    hasOverseasShop
                } = ref.state;
                ref.patchState({ currentGoodsIsOverseas: isOverseas });
                if (wxOpenAccount && paOpenAccount && alipayOpenAccount) {
                    return {showAccountModal : false};
                }
                // 判断是否是海淘商品 如果是海淘商品 不弹窗 接下来判断
                if (isOverseas) {
                    return { showAccountModal : false };
                }
                if (isSubmitQualification || isCheckQualification) {
                    // 有提交过资质申请或者已经获得资质(有shop_id)
                    if (!isOverseas && hasOverseasShop) {
                        //如果是海外商品以及拥有境外店铺
                        return ({ showAccountModal: true, accountModalTextInfo:overseasAccountModalInfo });
                    }
                    return ({showAccountModal: true, accountModalTextInfo:hasSubmittedQualificationModalInfo })
                } else {
                    if (hasOpenAccountInfo) {
                        // 有快速账户
                        if (!allianceUpgradeModalHasShown) {
                            // 升级弹窗第一次弹出
                            return ({showAccountModal: true, accountModalTextInfo:hasOldAccountModalInfo})
                        } else {
                            return { showAccountModal : false };
                        }
                    } else {
                        // 没有快速账户
                        return ({showAccountModal: true, accountModalTextInfo:hasNoOldAccountModalInfo})
                    }
                }
            }else{
                const {
                    hasOpenAccountInfo
                } = ref.state;
                if (hasOpenAccountInfo) {
                    return { showAccountModal : false };
                } else {
                    // 没有快速账户
                    return ({showAccountModal: true, accountModalTextInfo:hasNoOldAccountModalInfo})
                }
            }            
        },
        /**
         * 1. 没有旧收款方式 && 未提交过升级资质，跳转到 开通账户选择页
         * 2. 有旧收款方式 && 有绑定手机号，跳转到账户验证页面
         * 3. 有旧收款方式 && 没有绑定手机号，跳转到 选择资质页面
         * 4. 提交过升级资质但是没审核通过，跳转到 资质认证
         * 5. 提交过升级资质且审核通过，跳转到 结算账户开通页
         */
        handleModalConfirm() {
            const {
                isSubmitQualification,
                isCheckQualification,
                hasOpenAccountInfo,
                hasBindAccountPhoneNumber,
                currentGoodsIsOverseas,
                hasOverseasShop
            } = ref.state;
            ref.patchState({ backFromAccountPage: true })
            if (!currentGoodsIsOverseas && hasOverseasShop) {
                return;
            } else if (!isSubmitQualification && !isCheckQualification) {
                if (!hasOpenAccountInfo) {
                    openSchema(
                        { schema: ALLIANCE_ACCOUNT_NEXT_PAGE_SCHEMA.accountTypeSelect}
                    );
                } else if (hasBindAccountPhoneNumber) {
                    openSchema(
                        { schema: ALLIANCE_ACCOUNT_NEXT_PAGE_SCHEMA.accountPayeeVerify}
                    );
                } else {
                    openSchema(
                        { schema: ALLIANCE_ACCOUNT_NEXT_PAGE_SCHEMA.accountQualificationSelect}
                    );
                }
            } else if (isSubmitQualification && !isCheckQualification) {
                openSchema(
                    { schema: ALLIANCE_ACCOUNT_NEXT_PAGE_SCHEMA.shopSettlePerson}
                );
            } else if (isSubmitQualification && isCheckQualification) {
                openSchema(
                    { schema: ALLIANCE_ACCOUNT_NEXT_PAGE_SCHEMA.accountList}
                );
            } else {
                // isSubmitQualification && !isCheckQualification
                // 异常状态兜底，视作已开通
                openSchema(
                    { schema: ALLIANCE_ACCOUNT_NEXT_PAGE_SCHEMA.accountList}
                );
            }
        },
        handleModalCancel() {
            const { allianceUpgradeModalHasShown } = ref.state;
            // 点击暂不升级后，不再弹出
            if (!allianceUpgradeModalHasShown) {
                localStorage.setItem(ALLIANCE_ACCOUNT_UPGRADE_HAS_SHOWN, '1');
                ref.patchState({ allianceUpgradeModalHasShown: true });
            }
        },
        hasAllFormalAccountsSet(){
            const {
                wxOpenAccount,
                paOpenAccount,
                alipayOpenAccount,
            } = ref.state;
            return wxOpenAccount && paOpenAccount && alipayOpenAccount;
        }
    };
    
    useEffect(() => {
        controller.fetchAllAccountsInfo();
        const onVisibilityChange = () => {
            // 由于5个页面之间互相可以串联，所以每次页面切换都重新抓取接口
            if (document.visibilityState === 'visible') {
                if(ref.state.backFromAccountPage){
                    controller.fetchAllAccountsInfo();
                    ref.patchState({backFromAccountPage:false});
                }
            }
        }
        document.addEventListener('visibilitychange', onVisibilityChange)
        return () => {
            document.removeEventListener('visibilitychange', onVisibilityChange)
        }
    }, [])

    return [ref.state, controller];
}
