import { L_API_BASE } from 'common/constants/index';
import fetch from '@src/lib/request/fetch';
import { isInAweme, isInToutiao, isInHuoshan, isInXigua } from '@src/lib/env_detect';
import {
    QualificationApiResponse,
    AccountStatesApiResponse,
    AllianceAccountStatusApiResponse,
    OpenAccountInfoApiResponse
} from './types';

// 获取正式账户信息
export async function fetchAccountStates() {
    const url = `${L_API_BASE}/ies/v2/author/openAccountStates`;
    const res = await fetch<AccountStatesApiResponse>({url});
    return res.data;
  }
  
// 获取达人资质信息
export async function fetchQualification() {
const url = `${L_API_BASE}/ies/v2/author/getQualificationInfo`;
const res = await fetch<QualificationApiResponse>({url});
return res.data;
}

// 获取云账户账户状态
export async function fetchAllianceIncomeAccountStatus() {
    const url = `${L_API_BASE}/ies/v2/author/openAccountStatus`;

    const params = { b_type: 2, b_type_new: 2 };
    if(isInToutiao) {
        params.b_type = 3,
        params.b_type_new = 3
    }
    if(isInHuoshan) {
        params.b_type = 1,
        params.b_type_new = 1
    }
    if(isInXigua){
        params.b_type = 4,
        params.b_type_new = 4
    }
    const res = await fetch<AllianceAccountStatusApiResponse>({
        url,
        params: params
    });
    return res.data;
}

// 获取已开通云账户的账户详情。
export async function fetchOpenAccountInfo() {
const url = `${L_API_BASE}/ies/v2/author/getOpenAccountInfo`;
const res = await fetch<OpenAccountInfoApiResponse>({url});
return res.data;
}