import { ApiResponse } from 'types/request';
export interface AccountModalTextInfo {
    title: string;
    content: string;
    confirmText: string;
    cancelText: string;
}
export interface AccountState {
    // 是否开通微信支付
    wxOpenAccount: boolean;
    // 是否开通支付宝
    alipayOpenAccount: boolean;
    // 是否开通聚合账户支付
    paOpenAccount: boolean;
    // 是否提交资质申请
    isSubmitQualification: boolean;
    // 是否完成资质申请
    isCheckQualification: boolean;
    // 是否拥有财经账户（财经账户=老账户=云账户=快速账户）
    hasOpenAccountInfo: boolean;
    // 老账户升级弹窗是否被弹出过
    allianceUpgradeModalHasShown: boolean;
    // 是否绑定电话号码
    hasBindAccountPhoneNumber: boolean;
    // 是否从帐号开通页面回来
    backFromAccountPage: boolean;
    // 是否拥有海淘店铺
    hasOverseasShop: boolean;
    // 当前商品是否为海淘商品
    currentGoodsIsOverseas: boolean;
    // 小店随心推代投授权 
    adAuthStatus: boolean;
    // 请求api是否返回
    isCheckApiAdBack: boolean;
}

export interface AccountStateController {
    fetchAllAccountsInfo(): void;
    isAccountModalPopup(isOverseas: boolean): {
        showAccountModal : boolean,
        accountModalTextInfo?: AccountModalTextInfo
    };
    handleModalConfirm(): void;
    handleModalCancel(): void;
    hasAllFormalAccountsSet(): boolean;
}
export interface AccountStatesApiResponse extends ApiResponse {
    data: {
        wx_status: number; // 微信开户状态，1为已经开户
        alipay_status: number; // 支付宝开户状态， 1为已经开户
        pa_status: number; // 平安聚合账户开户状态， 1为已经开户
    };
}

export interface AllianceAccountStatusApiResponse extends ApiResponse {
    data: {
        open_status: number; // 是否开户 0未开户 2 已开户
        real_status: number; // 是否实名 0为实名 2已实名
    };
}

export interface QualificationApiResponse extends ApiResponse {
    data: {
        check_qualification_status: number;
        submit_qualification_status: number;
        has_haitao_shop: boolean;
        ad_auth_status: number;
    };
}

export interface OpenAccountInfoApiResponse extends ApiResponse {
    data: {
        phone_num: number; // 是否绑定手机号
    };
}
