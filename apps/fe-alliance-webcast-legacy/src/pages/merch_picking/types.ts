import { ReactNode } from 'react';
import type { SchemaConfig } from '@alliance-mobile/constants';
import { ApiResponse } from 'types/request';
import { PromotionShopLabel } from 'components/novice-shop-tag/types';
import { IPairLimitCommonParams, IPairLimitSheetParams } from './components/pair_limit/types';
import { AnchorShopAb } from './services/api';
import { isInSAAS } from '@src/lib/env_detect';

/** 最大可添品数量 */
export const MAX_SELECT_COUNT = 100;
export const NEW_MAX_SELECT_COUNT = 200;
export let TOTAL_MAX_SELECT_COUNT = MAX_SELECT_COUNT;
let abFlag: boolean;

const getAbTest = async () => {
  if (typeof abFlag !== 'undefined') {
    return;
  }
  if (isInSAAS) {
    abFlag = false;
    return;
  }
  try {
    const res = await AnchorShopAb({
      name_space: 'buyin_anchor_shop',
      ab_filters: ['live_promotion_extend'],
    });
    abFlag = res?.live_promotion_extend === 1;
    if (abFlag) {
      TOTAL_MAX_SELECT_COUNT = NEW_MAX_SELECT_COUNT;
    }
  } catch {}
};
getAbTest();

export enum ItemType {
  TAOBAO = 1, // 淘宝
  ALLIANCE = 4, // 精选联盟
  TAOBAO_FEED = 5, // 淘宝推荐
  FXG = 6, // 小店
  TAO_COMMAND = 7, // 淘口令
  JD = 8, // 京东
  KAOLA = 9, // 考拉
  VIPSHOP = 10, // 唯品会
  SUNING = 11, // 苏宁
  YMATOU = 12, // 洋码头
  YANXUAN = 13, // 网易严选
}

export interface AuthorityState {
  authority: Authority;
  isFetchingAuthority: boolean;
  isFetchAuthorityFailed: boolean;
}

export interface MerchSearchParams {
  // 结果数量
  count: number;
  // 偏移量
  offset: number;
  // 搜索关键词
  title?: string;
  // 排序字段
  order?: string;
  // 排序：1.asc；2.desc
  sort?: number;
  // 指定搜某个平台的商品，不指定则为商品库
  item_type?: ItemType;
  commonFilter: Record<string, unknown>;
}

// author/v1/author/authority接口原始返回
export interface AuthorityResponse extends ApiResponse {
  authority: {
    // 淘宝pid
    subpid: string;
    // 是否开通了小店
    has_xiaodian: boolean;
    // 是否授权开通了财经账号
    has_alliance_account: boolean;
  };
}
export interface Activity {
  activity_icon: string;
  width: number;
}
export interface Discount {
  campaign_type: string;
  title: string;
}

interface Icon {
  height: number;
  uri: string;
  url_list: string[];
  width: number;
}

interface PrivilegeInfoDetail {
  tag_id: string; // 权益id标识
  title: string; // 权益标题(eg:正品保障)
  content: string; // 详细权益信息(100%正品保障，假一赔十，放心选购)
  icon: Icon; // 详细信息中的权益icon
  small_icon: Icon; // 权益信息小icon
  show_small_icon: boolean; // 是否展示small_icon
  link?: string; // 跳转链接
}

interface CouponTag {
  title: string; // 团长券名称
  discountInfo: string; // 券信息
  couponType: string; // 券类型
}

export interface PrivilegeInfo {
  // 权益icon(只有安心购才会有icon)
  icon: Icon;
  platform_public_url: string; // 平台公示
  privilege_id: string; // 权益id(1:安心购，2:其他权益)
  privilege_info_detail: PrivilegeInfoDetail[]; // 权益详情
}

export interface ProductTag {
  type?: ProductTagType;
}
export enum ProductTagType {
  CheckReal = 2, // 鉴定验真
}

export enum TieredCosStatus {
  PreStatus = 1, // 未生效
  Active = 2, // 生效中
}

export interface CosStruct {
  cos_ratio?: number;
  cos_fee?: number;
}

export interface TieredCosStruct {
  /**
   *阶梯佣金,0:base， 1:top
   */
  tiered_cos_list?: CosStruct[];
  /**
   *生效状态
   */
  status?: TieredCosStatus;
  /**
   *阶梯佣金门槛
   */
  threshold?: number[];
  /**
   *开始时间
   */
  start_time?: string;
  /**
   *结束时间
   */
  end_time?: string;
  /**
   *阶梯计划Id
   */
  plan_id?: string;
}

export interface BlockReasonInfo {
  /**
   *添加按钮置灰跳转链接
   */
  block_add_reason_link: string;
  /**
   *添加按钮置灰跳转链接文案
   */
  block_add_reason_link_tips: string;
}

export interface PmtCard4Kol {
  /**
   *推广id
   */
  promotion_id?: string;
  /**
   *商品id
   */
  product_id?: string;
  /**
   *标题
   */
  title?: string;
  /**
   *商品图片
   */
  cover?: Url;
  /**
   *价格
   */
  price?: number;
  /**
   *原价
   */
  market_price?: number;
  /**
   *佣金
   */
  cos_fee?: number;
  /**
   *佣金率
   */
  cos_ratio?: number;
  /**
   *详情页链接
   */
  detail_url?: string;
  /**
   *平台来源
   */
  item_type?: number;
  /**
   *销量
   */
  sales?: number;
  /**
   *小店标识icon
   */
  icon?: string;
  /**
   *平台活动(eg:宠粉节)
   */
  activity?: Activity;
  /**
   *新好货标签
   */
  new_goods_tag_infos?: NewGoodsTagInfo[];
  /**
   *是否收藏
   */
  is_fav?: boolean;
  /**
   *佣金展示前缀(赚 ¥)
   */
  cos_ratio_text?: string;
  /**
   *店铺id
   */
  shop_id?: number;
  /**
   *店铺名称
   */
  shop_name?: string;
  /**
   *是否可建联
   */
  can_contact_shop?: boolean;
  /**
   *活动文案,展示活动剩余时间
   */
  activity_text?: string;
  /**
   *商品图片,前端机审用
   */
  images?: Url[];
  /**
   *推荐理由
   */
  recommend?: string;
  /**
   *店铺体验分
   */
  exp_score?: string;
  /**
   *运营坑位标记
   */
  recall_source?: string;
  /**
   *是否为海淘商品
   */
  is_haitao?: boolean;
  /**
   *是否加橱窗
   */
  is_in_window?: boolean;

  /**
   *权益信息
   */
  privilege_info?: PrivilegeInfo;
  /**
   *新版商品卡佣金展示(赚¥)
   */
  new_cos_ratio_text?: string;
  /**
   *货源标签
   */
  goods_source_tag_info?: GoodsSourceTagInfo[];
  /**
   *是否是团长商品
   */
  is_leader?: boolean;

  real_cos_ratio?: number;
  /**
   *佣金发票标识 true 展示ICON
   */
  commission_ticket_tag?: boolean;
  /**
   *阶梯佣金信息
   */
  tiered_cos?: TieredCosStruct;
  /**
   *公开阶梯佣金标签
   */
  tiered_cos_tag?: boolean;
  /**
   *抖in好物标签
   */
  douyin_goods_info?: DouyinGoodsInfo;
  /**
   *无忧带标签
   */
  no_worry_info?: NoWorryInfo;
  /**
   *商品卡标签，新增的标签，放在这个数组
   */
  tag_list?: ProductTag[];
  /**
   *不让添加直播间的原因，为""则可以添加直播间
   */
  live_add_enum?: string;
  /**
   *订单数据
   */
  order_product?: OrderData;
  /**
   *是否是星图商品
   */
  is_star_promotion?: boolean;
  /**
   *0点待生效的佣金 23%->23
   */
  pending_cos_ratio?: number;
  /**
   *是否鉴真潮物
   */
  is_marvel?: boolean;
  /**
   *pk赛标签
   */
  is_pk_competition?: boolean;

  /**
   *发票提醒信息
   */
  commission_ticket_tip_info?: CommissionTicketTipInfo;
  /**
   *大标签
   */
  pic_product_tag?: MainProductTag[];
  /**
   *小标签
   */
  text_product_tag?: MainProductTag[];
  /**
   *店铺头像
   */
  shop_avatar?: string;
  /**
   *风险提示
   */
  risk_warning?: string[];
  /**
   *同款
   */
  has_same_type?: boolean;
  /**
   *同款理由
   */
  same_rec_reason?: ReasonInfo;

  in_cart?: boolean;
  /**
   *商品卡底部的推荐信息
   */
  recommend_info?: ReasonInfo;
  /**
   *控制是否展示体验分
   */
  is_show_score?: boolean;

  /**
   *价格文案
   */
  price_text?: string;
  /**
   *添品工具置灰不可添品原因(具体透出时机和文案服务端收敛)
   */
  block_add_reason?: string;
  pre_check_status?: number;
  /**
   *对应Good.views, 目前无值
   */
  views?: number;
  /**
   *对应Good.clicks, 目前无值
   */
  clicks?: number;
  /**
   *该推广好物帮排行
   */
  rank?: number;
  /**
   *好物帮跳转链接
   */
  rank_url?: string;
  /**
   *达人添加的标签
   */
  label?: string[];
  /**
   *低价同款替换专区
   */
  price_good_same_style?: MainProductTag;
  /**
   *69: optional MainProductTag         risk_warning_v2,         // 新风险提示信息(内部控制前端样式、颜色等)
   *普通库存
   */
  stock_num_sum?: number;
  /**
   *是否有同款列表
   */
  has_target_price_same_list?: boolean;
  /**
   *标签codes
   */
  tag_codes?: string[];
  /**
   *商品tab个性化推荐理由
   */
  /**
   *添品按钮置灰额外信息
   */
  block_add_reason_info?: BlockReasonInfo;
  selection_reason?: ReasonInfo[];
}

export interface LivingPrice {
  /**
   *价格,单位:分
   */
  price?: number;
  /**
   *价格文案
   */
  price_text?: string;
}

export interface Merch extends PmtCard4Kol {
  promotion_id: string;
  cover: string;
  product_id: string;
  title: string;
  elastic_title: string;
  price: number;
  market_price: number;
  cos_fee: number;
  detail_url: string;
  sales: number;
  item_type: ItemType;
  cos_ratio: number;
  favor: boolean;
  in_shop: boolean;
  shop_id: number;
  platform: number;
  platform_label: string;
  selling_point: string;
  coupon_amount: number;
  activity: Activity;
  full_discount: Discount[];
  shop_label: PromotionShopLabel;
  is_haitao: boolean;
  is_leader: boolean;
  block_add_reason?: string; // 阻塞商品添加成功的原因
  live_add_enum?: string; // 添加商品提示文案，默认空字符串可添加，非空字符串toast提示并拦截添加
  privilege_info?: PrivilegeInfo; // 安心购等权益详情
  coupons?: CouponTag[]; // 团长券数组
  oversold_remind?: boolean; // 是否超卖
  new_goods_tag_infos?: NewGoodsTagInfo[]; // 新好货标签
  goods_source_tag_info?: GoodsSourceTagInfo[]; // 货源标签
  commission_ticket_tag?: boolean; // 佣金发票
  order_product?: OrderData; // 订单数据
  douyin_goods_info?: DouyinGoodsInfo | null; // 抖音好物标签
  is_pk_competition?: boolean; // 是否是战队赛
  tag_list?: ProductTag[]; // 鉴定验真商品
  pending_cos_ratio?: number; // 0点待生效的佣金 23%->23
  no_worry_info?: NoWorryInfo; // 无忧带标签
  fromTab?: string; // 选择商品时商品对应的tab（如果在多个tab下被全选添加，则可能有多个）
  tab_title?: string; // 选择商品时商品对应的tab名称
  bind_source?: BindSource; // 选择商品时商品对应的tab
  index?: number; // 商品在列表中的位置，用于批量添加
  is_star_promotion?: boolean; // 是否是星图商品
  commission_ticket_tip_info?: CommissionTicketTipInfo; // 发票提醒信息
  pic_product_tag?: Array<MainProductTag>; // 大标签
  text_product_tag?: Array<MainProductTag>; // 小标签
  risk_warning?: string[];
  recommend_info?: ReasonInfo; // 商品卡底部的推荐信息
  tag_codes?: string[];
  /**
   *价格文案
   */
  price_text?: string;
  /** pick添加的时候透传埋点 */
  pick_add_params?: Record<string, unknown>;
  // 直播中价格
  living_price: LivingPrice;
}

export interface ReasonInfo {
  reason?: string;
  icon?: string;
  /** 推荐类型，  1:万人团  2:榜单  3: 商品静态理由  4: 个性化理由 */
  rec_type?: number;
  /** 具体推荐类型 */
  sub_rec_type?: number;
  /** 存储一些埋点相关的信息, 榜单: tab_id, rank, type, billboard_type； recommend_type：sale_increase： 销量增长 */
  extra?: { [key: string]: string };
}

/** 小标签 */
export interface MainProductTag {
  /** 标签类型，在tcc配置 */
  type?: string;
  /** pc端前置图片 or 大标签图片 */
  pic?: string;
  /** 主文字，券类型的左端文字 */
  text?: TextTag;
  /** 券类型的右端文字 */
  text_right?: TextTag;
  height?: number;
  width?: number;
  /** 边框颜色 */
  border_color?: string;
  action?: SpecialActionTag;
}

export interface TextTag {
  text?: string;
  /** 文字颜色 */
  color?: string;
  /** 背景颜色 */
  bg_color?: string;
}

/** 点击展示的结构体 与决策页相关 */
export enum TextTagType {
  /** 文字（pc可能有前置pic */
  Text = 0,
  TextDouble = 1,
}

export interface SpecialActionTag {
  hover?: Hover;
  click?: Click;
}

export interface Hover {
  coupon_hover?: Array<CouponHover>;
}

/** 多个券的hover */
export interface CouponHover {
  text_left?: TextTag;
  text_right?: TextTag;
}

export interface Click {
  douyin_goods?: Array<DouyinGoodsInfoDetail>;
}

export interface CommissionTicketTipInfo {
  type?: CommissionTicketTipType;
  effecte_time?: string; // 商家发票设置生效时间
  freeze_ratio?: number; // 达人冻佣比例
}

export enum CommissionTicketTipType {
  Normal = 1, // 正常添品
  ShopTicketPending = 2, // 商家发票逻辑待生效，提醒
  FreezeCommission = 3, // 带货冻结佣金提醒
}
export interface OrderData {
  /**
   *是否可以豁免
   */
  can_apply?: boolean;
}
export interface ReasonList {
  reason_id?: number;
  text?: string;
}
export interface IBypassInfo {
  total_count: number;
  released_count: number;
  reason_list: ReasonList[];
}
export interface NewGoodsTagInfo {
  banner_type?: string; // nice_goods：精选好物 new_arrival：首发新品  main_new_arrival：主推新品
  icon?: Url; // 图片
}

export interface Url {
  uri?: string;
  url_list?: string[];
  width?: number;
  height?: number;
}

export interface GoodsSourceTagInfo {
  banner_type?: string; // global_shopping：全球购 brand_shop：品牌  base_selection：基地甄选
  icon?: Url; // 图片
}

export enum Scene {
  live = 1, // 直播
  video = 2, // 短视频
  shop = 3, // 橱窗(和rpc接口定义不一致，使用需要确认)
  Selection = 4, // 选品广场
}

export interface GetPmtByLinkRequest {
  promotion_link?: string;
  /**
   *场景
   */
  scene?: Scene;
  /**
   *是否是新的商品卡返回
   */
  new_card?: boolean;
  /**
   *需要返回同款列表
   */
  need_same_style_list?: boolean;
  /**
   *ab参数,
   */
  extra?: Record<string, string>;
}

export interface GetTabPromotionRequest {
  cursor?: number;
  count?: number;
  /**
   *tab对应的id
   */
  tab_id?: number;
  /**
   *来源 //"media":短视频,"live"
   */
  source?: string;
  /**
   *通用筛选项（需要反序列解析）
   */
  common_filter?: string;
  /**
   *feed_id
   */
  feed_id?: string;
  /**
   *搜索字段
   */
  key_word?: string;
  /**
   *业务标识，逗号分隔。如“hour_arrive”小时达, 多个标识可叠加扩展，透传至kol_selection.TabPmtListRequest定义的tags字段
   */
  tags?: string;
  /**
   *场景标识 //下发场景，用于区分不用tab的处理方式
   */
  scene?: string;
}

export interface MerchListResponse extends ApiResponse {
  offset: number;
  total: number;
  count: number;
  has_more: boolean;
  promotions: Merch[];
  live_room_type?: number; // 直播间类型，0:普通，1:安心购
}

interface ButtonInfo {
  text: string;
  schema: string;
}

interface WindowInfo {
  title: string;
  content: string;
  btn_l: ButtonInfo;
  btn_r: ButtonInfo;
}

interface HintInfo {
  title: string;
  content: string;
  btn: ButtonInfo;
}

export interface AlertResponse extends ApiResponse {
  status_code: number;
  status_msg: string;
  id: string;
  window: WindowInfo;
  hint: HintInfo;
}

export interface MachineAuditResponse {
  status_code?: boolean | number;
  status_msg?: string;
  reject_reason?: string;
  reject_type?: number;
  explanation?: string;
  rule_title?: string;
  rule_url?: string;
  questionnaire_title?: string;
  questionnaire_url?: string;
  i_get_it?: string;
  guide_info?: ModalConfig;
}

export interface Authority {
  // 是否开通了小店
  isXiaoDianAuthed: boolean;
  // 是否绑定了淘宝PID
  isTaobaoPidAuthed: boolean;
  // 是否授权开通了财经账号
  isAllianceAccountAuthed: boolean;
}

export interface PortalTab {
  key: string;
  title: string;
  isRendered: boolean;
  type: number;
}

export interface PortalTabState {
  tabList: PortalTab[];
  activeTab: PortalTab;
}

export interface RawTaobaoMaterial {
  id: number;
  name: string;
}

export interface TaobaoMaterialTab {
  id: number;
  key: string;
  title: string;
  isRendered: boolean;
}

export interface TaobaoMaterialTabState {
  tabList: TaobaoMaterialTab[];
  activeTab: TaobaoMaterialTab | null;
  isFetchingTabList: boolean;
  isFetchTabListFailed: boolean;
}

export enum VendorId {
  TAOBAO = '1',
  JD = '8',
  KAOLA = '9',
}

export interface LinkSearchGuideOptions {
  linkType: string;
  linkSource: string;
}

export interface VendorEntry {
  id: VendorId;
  title: string;
  icon: string;
  pageTitle: string;
  itemType?: ItemType;
  linkSearchGuide: LinkSearchGuideOptions;
}

export interface SortingOption {
  key: string;
  title: string;
  reverse?: boolean;
  shiftable?: boolean;
  initReverse?: boolean;
}

export interface ModalConfig {
  title: string;
  content: string;
  confirm_text: string;
  cancel_text: string;
  guide_link?: string;
}

export interface IAggregatePromotionModalState {
  data: {
    promotion_id: string;
    product_id: string;
  };
  options?: {
    getTipTitle?: (params: { length?: number }) => ReactNode;
  };
}

export interface AppState {
  isAuthorityOutdated: boolean;
  isLoadingMaskActive: boolean;
  isComboSearchViewActive: boolean;
  isLinkSearchViewActive: boolean;
  isSearchHelpViewActive: boolean;
  isTaobaoAuthConfirmActive: boolean;
  isBackFromTaobaoAuth: boolean;
  isBackFromAllianceAuth: boolean;
  isMerchPickForbidden?: boolean;
  merchPickForbiddenReason?: string;
  isBackFromGoodEditor: boolean;
  clipBoardModalVisible: boolean; // 获取剪贴板内容之后弹窗
  isSkipFromClipBoard?: boolean; // 判断是否是从剪贴板跳过去的 埋点逻辑专用
  activeTab: PortalTab;
  clipboardPromotionInfo: Merch;
  isPickFailedModalShow: boolean;
  pickFailedModalText: string;
  isYMaTouAuthConfirmActive: boolean;
  isCancelBindToLiveModalShow: boolean;
  activeSearchTab: PortalTab;
  isClipboardModalAuditing: boolean;
  isBackFromPicked: boolean;
  isGoodInStarAtlasModalShow: boolean;
  machineAudit: MachineAuditResponse;
  isPopupLinkModalShow: boolean;
  popupLinkResult: IPopupResponse;
  isAccountModalShow: boolean; // 账户资质弹窗判断
  accountModalTextInfo?: AccountModalTextInfo;
  isShowOpenOverseasAccount: boolean;
  isShowGoodsLimitModal: boolean;
  isShowFirstAddOverseas: boolean;
  isShowAdModal: boolean;
  isCheckAdBack: boolean;
  isCheckPaste: boolean;
  isCheckUnion: boolean;
  limitModal: ModalConfig;
  remindMessage: RemindMessage;
  commonModal: CommonModalInfo;
  isShowCommonModal: boolean;
  qualificationMsg: CommonQualification;
  isRemindApiBack: boolean; // 安心购接口返回
  isQualificationApiBack: boolean; // 资质升级接口返回
  isShowQualificationModal: boolean; // 是否展示资质升级modal
  isShowBindQualificationModal: boolean; // 绑定的时候是否展示资质升级modal
  jumpBindQualificationUrl: string; // 资质升级跳转url
  bindQualificationContent: string; // 资质升级展示文案
  isPairLimitTipApiBack: boolean; // 接口返回
  // 直播添品达人挂车提示
  pairLimit: null | {
    isLimitNotice: boolean; // 第一次上线
    isLimitForced: boolean; // 第二次上线
    timeText: string; // 日期
  };
  pairLimitConfirmInfo: null | IPairLimitCommonParams;
  pairLimitSheetsInfo: null | IPairLimitSheetParams;
  /** 批量选择商品列表 */
  batchSelectedList: Merch[];
  /** 批量绑定结果弹层信息 */
  batchResultModal: batchResultModalInfo;
  /** 是否显示批量绑定结果弹层 */
  isShowBatchResultModal: boolean;
  commonAlertInfo?: AlertData;
  /** 取消选中商品所属的tab */
  cancelSelectAllTab: string;
  /** 是否是新版添品页（配合直播添品链路优化的添品ux改版） */
  isMerchV2?: boolean;
  // tab列表
  tabList?: PortalTab[];
  // 是否自动全选
  autoSelectAll?: boolean;
  // 选品车tab下空态时是否展示一键添品
  showAddRecommend?: boolean;
  // 关联商品弹窗状态
  aggregatePromotionModalState: IAggregatePromotionModalState | undefined;
}

export interface AppContext {
  appName: string;
  appendedMerchList: string[];
  tab_type?: number;
}

export type AppStatePatcher = (patch: Partial<AppState>) => void;

export type AuthorityFetcher = (authority: Authority) => Promise<void>;

export interface Statistics {
  onMerchPick(merch: Merch, index: number): void;
}

export interface PortalStatistics extends Statistics {
  onSwitchTab(tab: PortalTab): void;
}

export interface VendorStatistics extends Statistics {
  onPageView(vendor: VendorEntry): void;
  onSwitchTab(tab: TaobaoMaterialTab): void;
}

export interface ActivityStatistics extends Statistics {
  onPageView(): void;
}

export interface TaobaoMaterialListResponse extends ApiResponse {
  materials: RawTaobaoMaterial[];
}

export interface AutomanDocInfo {
  docTitle: string;
  docContent: string;
  docTheme: string;
}

export interface AutomanDocResponse extends ApiResponse {
  data?: AutomanDocInfo;
}

export interface LiveTab {
  [key: string]: number;
}

export interface TabStruct {
  tab_id: number;
  name: string;
  anchored?: boolean; // 页面进入是否锚定tab
}

export interface LiveTabListResponse extends ApiResponse {
  data: {
    tab_list?: TabStruct[]; // Tablist,包括 1专属推广、2定向计划、3我的店铺；短视频来的固定有：4我的橱窗 5我的收藏, 直播的有：7星图平台； 8最近添加； 11合作订单；16鹊桥计划商品; 21:推荐商品
    user_level?: number; // 达人等级
    log_id?: string; // log_id
  };
}

export enum LiveTabListRequestBusinessScene {
  LiveControlFeed = 1, // 直播中控推荐域
}

export interface LiveTabDetailParams {
  page: number;
  size: number;
  tab_id: number;
}

export interface LivePromotionSearchParams {
  key_word: string;
  page: number;
  size: number;
  tab_id: number;
}
export interface LiveMachineAuditParams {
  promotion_id: string;
  product_id: string;
  item_type: number;
  request_from?: number;
}

export interface LiveMachineAuditResponse extends ApiResponse {
  pass: boolean;
  reject_reason: string;
}

export interface LiveBindPromotionParams extends UserActionRecordProps {
  promotions: string;
  new_bind_promotion?: string;
  room_id?: string;
}

// 直播选品绑定api重构
export interface LiveBindOperatePromotionParams {
  promotion_id: string;
  product_id: string;
  item_type: number;
  bind_source?: number;
  third_source?: number;
}

export interface BindGoodsToLiveProps extends UserActionRecordProps {
  promotionIds: string;
  promotionId?: string;
  roomId?: string;
}

export interface LiveAddPromotionParams {
  promotion_id: string;
}

export type PromotionIdType = string;

export interface UserInfo {
  code: number;
  user_id: string;
  sec_user_id: string;
}

export type MerchPickHandler = (merch: Merch, index?: number | undefined, source?: string | undefined) => Promise<void>;

interface MerchExtra {
  is_star_promotion?: string;
  user_shop_exist?: string;
}
type StarAtlasMerch = Merch & { extra: MerchExtra };
export type PickingMerch = Merch & { extra?: MerchExtra };
export interface MerchStarAtlasSection {
  promotions: StarAtlasMerch[];
  title: string;
  task_id: number;
  task_name: string;
  task_status: number;
  task_status_text: string;
}

export interface StarAtlasTabDetailResponse extends ApiResponse {
  star_task: MerchStarAtlasSection[];
  total: number;
  has_more: boolean;
}

export type MerchEventHandler = (
  merch: Merch,
  index: number,
  isShouldAudit?: boolean,
  fromClip?: boolean,
  disableLog?: boolean
) => void;

export interface ShopRiskVerifyParams {
  risk_type: number;
}

export enum RISK_TYPE {
  hasRisk = 1,
  noRisk = 2,
}

export interface ShopRiskVerifyResponse extends ApiResponse {
  verify_status: RISK_TYPE;
}

export interface CertificationSubmitParams {
  skip_record_verify: boolean;
  return_url: string;
}

export interface CertificationSubmitResponse extends ApiResponse {
  zhima_token: string;
  url: string;
  transaction_id: string;
  merchant_id: string;
}

export interface IPopupParams {
  pop_type: string;
}

export interface IPopupResponse extends ApiResponse {
  contents: {
    img: string;
    url: string;
    description: string;
  };
  id: string; // 该记录id，用于对比是否为相同记录
  expire_time: string | number; // 记录过期时间，格式”2006-01-02T15:04:05“
  now_time: string; // 当前时间
}

export interface UserActionRecordProps {
  pick_first_source?: string;
  pick_second_source?: string;
  pick_third_source?: string;
}

export interface ActionRecordProps {
  actionLevel1?: string;
  actionLevel2?: string;
  actionLevel3?: string;
  position?: 'list';
}

// 账户资质相关弹窗文本
export interface AccountModalTextInfo {
  title: string;
  content: string;
  confirmText: string;
  cancelText: string;
}

export interface CommonModalInfo {
  title: string;
  guide_link?: string;
  content: string;
  confirm_text: string;
  cancel_text: string;
}

// 设置广告弹窗
export interface ISetAdAuthParams {
  status: number;
}

export type ISetAdAuthResponse = ApiResponse;

// 安心购弹窗
export interface AnXinGouModalInfo {
  title: string; // 标题图片URL
  text: string[]; // 弹窗正文
  confirm_button: string; // 确定按钮
  cancel_button: string; // 取消按钮
  switch_on_info: {
    // 安心购介绍页说明
    title: string; // 开启安心购你讲获得
    switch_on_content: {
      // 数组返回每个说明
      item_title: string; // 说明的title
      item_text: string; // 说明的内容
      item_icon: string; // 图片地址
    }[];
  };
  switch_on_constraint: {
    title: string; // 开启安心购限制条件
    switch_on_constraint: {
      // 数组返回每个限制条件
      item_title: string; // 条件title
      item_first_text: string; // 条件内容第一部分
      item_second_text: string; // 条件内容第二部分
    }[];
  };
  agreement_content: string; // 协议文案
  agreement_url: string; // 协议跳转链接
}

// 顶部提示【安心购|真实保】
export interface MessageInfo {
  icon: string;
  text: string;
  url: string;
}

export type RemindMessage = {
  message: MessageInfo;
  firstUseAnXinGouModal: AnXinGouModalInfo;
  isShowAnXinGo: boolean;
};

// 顶部横幅提醒文案
export interface RemindMessageResponse extends ApiResponse {
  tag_info: {
    // 顶部横幅提示文案【安心购，真实保】
    icon; // 图标
    text; // 文本
    url; // 跳转链接
  };
  peace_switch_info: {
    open_status: number; // 安心购开启状态 0：未开启；1：开启
  };
  first_use_alert: AnXinGouModalInfo;
  is_show: boolean; // is_show代表版本支持且直播间类型支持
}

export interface CommonQualification {
  remind_text?: string; // 提醒文案
  level?: number; // 响应等级
  jump_url?: string; // 跳转链接
  need_upgrade: boolean; // 是否需要升级
}
export interface QualificationResponse extends ApiResponse {
  data: CommonQualification;
}

export interface QualificationMsg extends CommonQualification {
  isShowQualificationModal: boolean;
}

export interface GoodsIconInfo {
  uri: string;
  url_list: string[];
  width: number;
  height: number;
}

export interface DouyinGoodsInfo {
  tag_id: number;
  icon: GoodsIconInfo;
  douyin_goods_info_detail: DouyinGoodsInfoDetail[];
}

export interface DouyinGoodsInfoDetail {
  tag_id: string;
  title: string;
  content: string;
  link: string;
  icon: GoodsIconInfo;
}

export interface NoWorryInfo {
  tag_id?: number; // 标签id(1:无忧带)
  icon?: Url; // 权益icon(无忧带)
}

/** 批量直播绑定接口请求类型定义 */
export interface BatchBindRequest {
  promotions: LiveBindPromotions[]; // 推广id
}
export enum BindSource {
  /** 我的橱窗 */
  Window = 2,
  /** 我的店铺 */
  Shop = 3,
  /** 定向计划 */
  OrientationPlan = 4,
  /** 专属计划 */
  ExclusivePlan = 5,
  /** 星图 */
  Star = 6,
  /** 合作订单 */
  CopperOrder = 11,
  /** 链接 */
  Link = 0,
  /** 最近添加 */
  LatestBind = 1,
  /** 选品车 */
  PickingCart = 20,
  /** 移动端直播推荐商品 */
  LiveFeedTab = 21,
  /** 最近带货 */
  RecentlyGoods = 8,
  /** 定向合作（专属佣金） */
  ExclusiveCooperate = 25,
}
export enum BindThirdSource {
  Search = 1, // 搜索
  Link = 2, // 链接
}
export interface LiveBindPromotions {
  promotion_id: string; // 推广id
  product_id: string; // 商品id
  item_type: number; // 商品来源
  bind_source?: BindSource;
  third_source?: BindThirdSource;
}

export interface NoticeItem {
  title: string; // 商品名称
  product_id: string; // 商品id
  bind_msg?: string; // 绑定状态文案
  main_img?: string; // 商品主图
}

/** 批量直播绑定接口返回类型定义 */
export interface BatchBindResponse extends ApiResponse {
  data: BindResults; // 绑定结果
}
export interface BindResult {
  /**
   *绑定状态
   */
  bind_status: number;
  /**
   *绑定状态文案
   */
  bind_msg?: string;
  /**
   *绑定状态详情
   */
  bind_reason?: string;
  /**
   *商品id
   */
  product_id: string;
  /**
   *商品名称
   */
  title: string;
  /**
   *弹窗(内部有sub_guide_info优先展示sub_guide_info)
   */
  guide_info?: LiveGuideInfo;
  /**
   *是否是弹窗的形式
   */
  is_toast?: boolean;
  /**
   *商品主图
   */
  main_img?: string;
  /**
   *推广id
   */
  promotion_id?: string;
}
export interface SubGuideInfo {
  /**
   *主品对应的弹窗信息
   */
  title?: string;
  content?: string;
  /**
   *确认按钮 文本
   */
  confirm_text?: string;
  /**
   *取消按钮  文本
   */
  cancel_text?: string;
  /**
   *引导页url
   */
  guide_link?: string;
  /**
   *主品对应的商品信息
   *商品id
   */
  product_id?: string;
  /**
   *推广id
   */
  promotion_id?: string;
  /**
   *商品名称
   */
  product_title?: string;
  /**
   *商品主图
   */
  product_ing?: string;
}

export interface LiveGuideInfo {
  title?: string;
  content?: string;
  /**
   *确认按钮 文本
   */
  confirm_text?: string;
  /**
   *取消按钮  文本
   */
  cancel_text?: string;
  /**
   *引导页url
   */
  guide_link?: string;
  /**
   *完全失败也有可能是因为聚合品下的所有主品失败
   *如果前端有此信息，则优先展示此信息
   */
  sub_guide_info?: SubGuideInfo[];
}

export interface LiveGuideInfoPartialFailure {
  /**
   *部分失败只透出这个list即可
   */
  sub_guide_info?: SubGuideInfo[];
}

export interface BindResults {
  /**
   *绑定成功个数
   */
  success_count: number;
  /**
   *绑定失败个数
   */
  failure_count: number;
  /**
   *批量绑定[完全失败]详情(多个商品的挂车，如果失败了用这个字段，对应没有在c侧直播间占坑位)
   */
  failure_list?: BindResult[];
  /**
   *商品超卖提醒商品信息
   */
  oversold_remaind_result?: GoodsOversoldRemaindResult[];
  /**
   *单个商品[完全失败]详情(单个商品的挂车，如果失败了用这个字段，对应没有在c侧直播间占坑位)
   */
  guide_info?: LiveGuideInfo;
  /**
   *成功的商品
   */
  success_pmt_info?: BindResult[];
  /**
   *是否是弹窗的形式
   */
  is_toast?: boolean;
  /**
   *部分失败个数 (实际已经在c侧占了坑位，但关联的子品或主品存在部分失败，目前仅跨店聚合品使用)
   */
  partial_failure_count?: number;
  /**
   *单个商品[部分失败]详情 (实际已经在c侧占了坑位，但关联的子品或主品存在部分失败，目前仅跨店聚合品使用)
   */
  partial_failure_guide_info?: BindResult;
  /**
   *批量绑定[部分失败]详情 (实际已经在c侧占了坑位，但关联的子品或主品存在部分失败，目前仅跨店聚合品使用)
   */
  partial_failure_list?: BindResult[];
  /**
   *新版单个商品[完全失败]详情(兼容了跨店聚合品下关联主商品的版本，后续新逻辑坐在这个字段上)(如果失败了用这个字段，对应没有在c侧直播间占坑位)
   */
  new_guide_info?: BindResult;
}
export interface GoodsOversoldRemaindResult {
  main_img: string; // 商品主图
  product_id: string; // 商品id
  title: string; // 商品名称
}

/** 批量直播绑定结果 */
export interface batchResultModalInfo {
  successCount: number; // 绑定成功个数
  failureCount: number; // 绑定失败个数
  failureList: BindResult[]; // 绑定失败详情
  oversoldList?: GoodsOversoldRemaindResult[]; // 商品超卖提醒商品信息
  partialFailureList: BindResult[]; // 绑定失败详情
}

export enum SpecialShowType {
  Anxingou = 1, // 安心购专场
  CheckReal = 2, // 鉴定验真专场
  Brand = 3, // 品牌专场
}
export enum TipType {
  /** 保证金 */
  Margin = 1,
  /** 运营配置 */
  SelectionOperation = 2,
  /** 税务 */
  QualificationCheck = 3,
  /** 直播联盟控挂车 仅直播 */
  AllianceOrder = 4,
  /** 专场： 安心购，鉴定验真，品牌  仅直播 */
  SpecialShow = 5,
  /** 真实宝  仅直播 */
  RealInsurance = 6,
  /** 混合经营 */
  MixOperate = 7,
}

export interface AlertData {
  icon?: string;
  text?: string;
  url?: string;
  /**
   *返回的类型
   */
  tip_type?: number;
  /**
   *直播专场类型
   */
  special_show_type?: SpecialShowType[];
  /**
   *是否可x掉
   */
  can_del?: boolean;
  /**
   *确认按钮引导跳转链接的文案
   */
  confirm_text?: string;
  /**
   *取消
   */
  cancel_text?: string;
  /**
   *tips名称
   */
  tip_name?: string;
  /**
   *弹窗提示tile
   */
  title?: string;
  /**
   *颜色主题
   */
  theme?: string;
}

export enum ShowType {
  Tip = 0, // tip展示
  PopWindow = 1, // 弹窗提示
}

export interface AlertRequest {
  scene_type?: SceneType;
  room_id?: number;
  shield_tip?: string; // list<TipType>  shield_tip, //屏蔽的tips
  show_type?: ShowType;
}
export enum SceneType {
  Square = 0, // 选品广场
  Media = 1, // 短视频
  Live = 2, // 直播
  Decision = 3, // 决策页
}

export interface CommonAlertResponse extends ApiResponse {
  data?: AlertData;
}

export interface KolRiskVerifyData {
  risk_verify_status?: RiskVerifyStatusEnum;
  risk_verify_msg?: string;
}
export interface KolRiskVerifyResponse {
  code: number; // 业务状态码，0: 成功
  st: number; // 状态码，0: 成功
  msg: string; // 出错提示消息
  data?: KolRiskVerifyData;
  log_id?: string; // log_id
}
export enum RiskVerifyStatusEnum {
  Pass = 1, // 未命中拦截逻辑，放行
  NeedFaceIdentification = 2, // 命中风控逻辑，需要进行人脸验证
}

// 推荐理由类型：
export enum ReasonInfoSubType {
  // 101: 商品静态理由-业务活动万人团，
  'WANRENTUAN' = 101,
  // 201: 商品静态理由-榜单，
  'RANK' = 201,
  // 301: 商品静态理由-销量速增，
  'SALES_INCREASE' = 301,
  // 302: 商品静态理由-带货达人数，
  'SALES_PERSON' = 302,
  // 303: 商品静态理由-评论原声，
  'COMMENT_ORIGINAL' = 303,
  // 401: 个性化理由-同行头部达人，
  'TONGXIN_DAREN' = 401,
  // 402: 个性化理由-相似达人热卖，
  'SIMILAR_DAREN' = 402,
  // 403: 个性化理由-粉丝历史热购类目，
  'FANS_HISTORY_CATEGORY' = 403,
  // 404: 个性化理由-粉丝画像匹配，
  'FANS_IMAGE_MATCH' = 404,
}

export interface RouteQuery extends SchemaConfig {
  enter_from: 'live_control_operation' | 'webcast_living' | 'auto_live_picking';
  from_picked?: '1';
  /**
   * 直播间 或者 准备直播(roomId=0) id
   */
  roomId?: string | number;
  /**
   * pick_node 流程图见：https://bytedance.larkoffice.com/docx/MoxqdouGDolwlhxqLQxcLC7anEb#O02rdkVJgolDvPxXuclcsPO1nWd
   * room 读写直播间或准备直播中控
   * id 仅传入传出 id，无数据存储
   */
  pick_mode?: 'room' | 'id';
}

export interface dataRenderConfig {
  renderKey: string;
  scale: number;
  titleNode: ReactNode;
  dataNode: ReactNode;
}

export enum EDataRenderKey {
  price = 'price', // 售价
  cos = 'cos', // 佣金
  sales = 'sales', // 月售
  stock = 'stock', // 库存
}
