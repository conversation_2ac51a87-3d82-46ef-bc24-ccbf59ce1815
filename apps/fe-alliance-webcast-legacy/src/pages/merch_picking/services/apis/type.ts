export interface aggr_product_list_request {
  /**
   *聚合id
   */
  aggr_id: string;
  /**
   *店铺id or 商品id
   */
  search_text?: string;
}

export interface AuthorPlatformModel {
  /**
   *昨日出单数
   */
  yesterday_sale?: IntView;
  /**
   *体裁订单数
   */
  media_order_cnt?: MediaOrderCnt;
}
export interface CategoryItem {
  /**
   *类目ID
   */
  category_id?: number;
  /**
   *类目名
   */
  category_name?: string;
}
export interface Click {
  goods?: GoodsInfo[];
}
export interface ContextInfo {
  /**
   *各种信息
   */
  text?: string;
  /**
   *图片
   */
  icon?: string;
  /**
   *跳转链接
   */
  url?: string;
  /**
   *类型
   */
  rec_type?: number;
  /**
   *额外信息
   */
  extra?: Record<string, string>;
}
export interface CosInfo {
  /**
   *佣金
   */
  cos?: CosStruct;
  /**
   *佣金展示
   */
  cos_ratio_text?: string;
  /**
   *佣金类型
   */
  cos_type?: number;
  /**
   *阶梯信息
   */
  tiered_cos_info?: TieredCosInfo;
}
export interface CosStruct {
  /**
   *佣金
   */
  cos_fee?: IntView;
  /**
   *佣金率
   */
  cos_ratio?: DoubleView;
}
export interface CouponHover {
  /**
   *hover的券左半部分
   */
  text_left?: TextTag;
  /**
   *hover的券右半部分
   */
  text_right?: TextTag;
}
export interface DoubleView {
  /**
   *原值(eg:98%=>origin=98)，佣金率等百分比使用如下表达值
   */
  origin?: number;
  /**
   *表达值整数部分
   */
  integer?: string;
  /**
   *表达值小数部分
   */
  decimal?: string;
  /**
   *表达值后缀
   */
  suffix?: string;
}
export interface ExtendInfo {
  /**
   *是否加橱窗
   */
  is_in_window?: boolean;
  /**
   *是否在选品车中
   */
  is_in_cart?: boolean;
  /**
   *是否有同款
   */
  has_same_type?: boolean;
  /**
   *同款信息
   */
  same_reason?: ReasonInfo;
  /**
   *价格提醒
   */
  price_reminder?: ReminderInfo;
}
export interface Float64StatsData {
  value: number;
  time_option: TimeOption;
}
export interface FrontCategoryInfo {
  front_categories?: CategoryItem[];
}
export interface GoodsInfo {
  /**
   *标签标识
   */
  tag_id: string;
  /**
   *标签标题(eg:流量倾斜)
   */
  title: string;
  /**
   *详细标签信息
   */
  content: string;
  /**
   *详细信息中的icon
   */
  icon?: UrlStruct;
  /**
   *跳转链接
   */
  link?: string;
}
export enum HideStatus {
  Hide = 1, // 隐藏
  Visible = 2, // 可见
}
export interface HideStatusInfo {
  hide_status?: HideStatus;
}
export interface Hover {
  /**
   *多个券的hover
   */
  coupon_hover?: CouponHover[];
  /**
   *一般hover
   */
  normal_hover?: NormalHover;
}
export interface Int64StatsData {
  value: number;
  time_option: TimeOption;
}
export interface IntView {
  /**
   *原值，如果是金额，单位是分！
   */
  origin?: number;
  /**
   *表达值整数部分
   */
  integer?: string;
  /**
   *表达值小数部分
   */
  decimal?: string;
  /**
   *表达值后缀
   */
  suffix?: string;
}
export interface KolProductInfo {
  product_id?: string;
  promotion_id?: string;
  base_model?: ProductBaseModel;
  custom_model?: ProductCustomModel;
}
export interface MarketingInfo {
  /**
   *价格描述
   */
  price_desc?: PriceDesc;
}
export interface MediaOrderCnt {
  /**
   *高价订单数
   */
  high_price_order_cnt?: IntView;
  /**
   *支付订单数
   */
  pay_order_cnt?: IntView;
}
export interface NormalHover {
  /**
   *文字属性
   */
  text?: TextTag[];
  /**
   *hover整体背景颜色
   */
  bg_color?: string;
}
export interface OperatingData {
  /**
   *成交总金额
   */
  gmv?: Int64StatsData;
  /**
   *曝光成交率
   */
  exposure_sold_rate?: Float64StatsData;
  /**
   *曝光点击率
   */
  exposure_click_rate?: Float64StatsData;
  /**
   *点击成交率
   */
  click_sold_rate?: Float64StatsData;
  /**
   *复购率
   */
  repurchase_rate?: Float64StatsData;
  /**
   *预估佣金
   */
  ideal_commission?: Int64StatsData;
}
export interface Pic {
  url?: string;
  height?: number;
  width?: number;
}
export interface PriceDesc {
  /**
   *价格
   */
  price?: IntView;
  /**
   *划线价
   */
  regular_price?: IntView;
  /**
   *价格文案
   */
  price_text?: string;
}
export interface ProductBaseModel {
  /**
   *商品信息
   */
  product_info?: ProductInfo;
  /**
   *推广信息
   */
  promotion_info?: PromotionInfo;
  /**
   *营销信息
   */
  marketing_info?: MarketingInfo;
  /**
   *店铺信息
   */
  shop_info?: ShopInfo;
  /**
   *标签信息
   */
  tag_info?: TagInfo;
  /**
   *扩展信息
   */
  extend_info?: ExtendInfo;
}
export interface ProductCategory {
  /**
   *一级类目
   */
  first_category?: CategoryItem;
  /**
   *二级类目
   */
  second_category?: CategoryItem;
  /**
   *三级类目
   */
  third_category?: CategoryItem;
  /**
   *四级类目
   */
  fourth_category?: CategoryItem;
  /**
   *叶子节点层数
   */
  leaf_layer?: number;
}
export interface ProductCustomModel {
  /**
   *选品广场业务域
   */
  selection_square_model?: SelectionSquareModel;
  /**
   *橱窗经营业务域
   */
  shop_window_model?: ShopWindowModel;
  /**
   *作者平台业务域
   */
  author_platform_model?: AuthorPlatformModel;
}
export interface ProductInfo {
  /**
   *商品名称
   */
  name?: string;
  /**
   *商品图片
   */
  main_img?: UrlStruct;
  /**
   *商品白底图片
   */
  white_img?: UrlStruct;
  /**
   *详情页链接
   */
  detail_url?: string;
  /**
   *后台类目
   */
  category?: ProductCategory;
  /**
   *商品状态
   */
  product_status?: ProductStatus;
  /**
   *库存信息
   */
  product_stock_info?: StockInfo;
  /**
   *前台类目
   */
  front_category_info?: FrontCategoryInfo;
  /**
   *商品月销量
   */
  month_sale?: IntView;
  /**
   *商品好评率
   */
  good_ratio?: DoubleView;
  /**
   *商品图片,前端机审用
   */
  images?: UrlStruct[];
}
export interface ProductStatus {
  /**
   *上下架状态，取值：0商品上架/1商品下架/2商品删除
   */
  status?: number;
  /**
   *审核状态，取值：0初始状态/1商品未提交/2商品待审核/3商品审核通过/4商品审核未通过/5商品下线/6商品解封/7商品审核通过，待上架
   */
  check_status?: number;
}
export interface ProductTagInfo {
  /**
   *标签类型，在tcc配置
   */
  type?: string;
  /**
   *大标签图片、pc文字前置图片
   */
  pic?: string;
  /**
   *小标签主文字、券类型的左端文字
   */
  text?: TextTag;
  /**
   *券类型的右端文字
   */
  text_right?: TextTag;
  height?: number;
  width?: number;
  /**
   *边框颜色
   */
  border_color?: string;
  /**
   *特殊交互，点击/hover
   */
  action?: SpecialActionTag;
}
export interface PromotionInfo {
  /**
   *商品来源
   */
  promotion_source?: number;
  /**
   *品牌icon
   */
  brand_icon?: string;
  /**
   *推广状态
   */
  promotion_status?: PromotionStatus;
  /**
   *佣金信息
   */
  cos_info?: CosInfo;
  /**
   *佣金发票提醒
   */
  ticket_tip_info?: TicketTipInfo;
}
export enum PromotionStatus {
  OpStop = 1,
  Normal = 2, // 推广正常
  OwnerStop = 3,
  TmpStop = 4,
  Auditing = 5, // 以后会逐渐废弃；勿用
  PlatformStop = 6, // 平台清退
  Delete = 99, // 删除
}
export interface ReasonInfo {
  reason?: string;
  icon?: string;
}
export interface RecommendInfo {
  /**
   *运营坑位标记
   */
  recall_source?: string;
}
export interface ReminderInfo {
  /**
   *行为唯一key
   */
  key?: string;
  /**
   *富文本
   */
  rich_text?: RichText[];
  /**
   *边框颜色
   */
  border_color?: string;
  /**
   *背景颜色
   */
  bg_color?: string;
  /**
   *商品信息
   */
  reminder_product?: ReminderProduct;
}
export interface ReminderProduct {
  /**
   *同款商品加车数量
   */
  in_cart_cnt?: number;
  /**
   *同款商品列表
   */
  product?: ReminderProductItem[];
}
export interface ReminderProductItem {
  /**
   *商品id
   */
  product_id?: string;
  /**
   *推广id
   */
  promotion_id?: string;
  /**
   *封面
   */
  cover?: string;
  /**
   *佣金率
   */
  cos_ratio?: DoubleView;
  /**
   *价格
   */
  price?: IntView;
  /**
   *好评率
   */
  good_ratio?: number;
  /**
   *月销量
   */
  month_sale?: number;
  /**
   *是否再选品车
   */
  in_cart?: boolean;
}
export interface RichText {
  /**
   *富文本类型
   */
  rich_text_type?: RichTextType;
  /**
   *模块文案
   */
  text?: Text;
  /**
   *模块icon
   */
  icon?: Pic;
}
export enum RichTextType {
  TEXT = 1, // 文字
  ICON = 2, // 图片
}
export interface SelectionSquareModel {
  /**
   *小二精选
   */
  waiter_select?: WaiterSelect;
  /**
   *推荐信息
   */
  recommend_info?: RecommendInfo;
  /**
   *key:same_rec_reason, 同款推荐理由； key: recommend_reason,商品卡推荐理由
   */
  recommend_reason?: Record<string, ContextInfo>;
  /**
   *废弃
   */
  price_reminder?: ProductTagInfo;
}
export interface ShopBuyShareInfo {
  /**
   *种草视频数量
   */
  share_count?: number;
}
export interface ShopExplanatoryVideo {
  id?: number;
  video_type?: VideoType;
  cover?: string;
  jump_url?: string;
  /**
   *视频播放地址
   */
  play_addr_url?: string;
  /**
   *视频标题
   */
  title?: string;
}
export interface ShopInfo {
  /**
   *店铺id
   */
  shop_id?: number;
  /**
   *店铺名称
   */
  shop_name?: string;
  /**
   *店铺头像
   */
  shop_logo?: UrlStruct;
  /**
   *店铺打分信息
   */
  shop_score_info?: ShopScoreInfo;
}
export interface ShopScoreDetail {
  score?: number;
  /**
   *高中低
   */
  rating?: string;
  percentage?: number;
  /**
   *当前score-七天前score
   */
  rise_than_7d_ago?: number;
}
export interface ShopScoreInfo {
  /**
   *是否有店铺体验分，无动销的商家是无法计算分数的显示"订单不足暂无分数"，或"暂无"，"--"。
   */
  has_data?: boolean;
  /**
   *店铺体验分
   */
  shop_score?: ShopScoreDetail;
}
export interface ShopWindowModel {
  /**
   *橱窗推荐语
   */
  shop_recommend_word?: string;
  /**
   *橱窗种草视频数量
   */
  shop_buy_share_info?: ShopBuyShareInfo;
  /**
   *橱窗讲解视频，最新的种草视频
   */
  shop_explanatory_video?: ShopExplanatoryVideo;
  /**
   *橱窗商品隐藏状态
   */
  hide_status_info?: HideStatusInfo;
  /**
   *经营数据
   */
  operating_data?: OperatingData;
}
export interface Slogan {
  /**
   *slogan 文案
   */
  text?: string;
  /**
   *弹窗
   */
  window?: SloganWindow;
}
export interface SloganWindow {
  /**
   *移动端弹窗
   */
  Mob?: UrlStruct;
  /**
   *Pc端弹窗
   */
  Pc?: UrlStruct;
}
export interface SpecialActionTag {
  hover?: Hover;
  click?: Click;
  slogan?: Slogan;
}
export interface StockInfo {
  /**
   *最终库存总数，无活动时=normal_stock_num_sum，有活动且时=campaign_stock_num_sum
   */
  stock_num_sum?: number;
  /**
   *普通库存总数
   */
  normal_stock_num_sum?: number;
  /**
   *可用活动库存总数
   */
  campaign_stock_num_sum?: number;
  /**
   *活动库存是否普通库存转换来的，true表示是普通库存转换来的
   */
  campaign_use_normal_stock?: boolean;
  /**
   *最终库存总数是否使用活动库存总数，true表示使用活动库存总数
   */
  stock_num_sum_use_campaign?: boolean;
}
export interface TagInfo {
  /**
   *主标签，标题前
   */
  main_tags?: ProductTagInfo[];
  /**
   *次标签，标题后
   */
  second_tags?: ProductTagInfo[];
}
export interface Text {
  text?: string;
  font_color?: string;
}
export interface TextTag {
  /**
   *标签文本
   */
  text?: string;
  /**
   *文字颜色
   */
  color?: string;
  /**
   *背景颜色
   */
  bg_color?: string;
  /**
   *链接
   */
  url?: string;
}
export interface TicketTipInfo {
  type?: TicketTipType;
  /**
   *商家发票设置生效时间
   */
  effecte_time?: number;
  /**
   *达人冻佣比例
   */
  freeze_ratio?: number;
}
export enum TicketTipType {
  Normal = 1, // 正常添品
  ShopTicketPending = 2, // 商家发票逻辑待生效，提醒
  FreezeCommission = 3, // 带货冻结佣金提醒
}
export interface TieredCosInfo {
  /**
   *阶梯佣金,0:base 1:top
   */
  tiered_cos_list?: CosStruct[];
  /**
   *阶梯佣金门槛
   */
  threshold?: number[];
  /**
   *生效状态
   */
  status?: TieredCosStatus;
  /**
   *开始时间
   */
  start_time?: string;
  /**
   *结束时间
   */
  end_time?: string;
}
export enum TieredCosStatus {
  PreStatus = 1, // 未生效
  Active = 2, // 生效中
}
export enum TimeOption {
  Yesterday = 1, // 近1日：T-1单日数据
  LastWeek = 2, // 近7日：过去7日累计数据
}
export interface UrlStruct {
  /**
   *uri
   */
  uri?: string;
  /**
   *url
   */
  url_list?: string[];
  /**
   *宽
   */
  width?: number;
  /**
   *高
   */
  height?: number;
}
export enum VideoType {
  ShortVideo = 1, // 短视频
  LiveCut = 2, // 直播切片
}
export interface WaiterSelect {
  /**
   *小二精选卖点信息
   */
  waiter_select_selling?: string;
  /**
   *是否有小二卖点设置
   */
  has_waiter_select_selling?: boolean;
  /**
   *小二预售库存
   */
  pre_sale_stock?: WaiterSelectStock[];
}
export interface WaiterSelectStock {
  /**
   *7日预售/15日预售
   */
  text?: string;
  /**
   *金额
   */
  money?: IntView;
}
export interface aggr_product_data {
  /**
   *商品列表,新商品卡
   */
  aggr_product?: KolProductInfo[];
}
export interface aggr_product_list_response {
  /**
   *状态码，0: 成功
   */
  code: number;
  st: number;
  msg: string;
  log_id?: string;
  data?: aggr_product_data;
}
