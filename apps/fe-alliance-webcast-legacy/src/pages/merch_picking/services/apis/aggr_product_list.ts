import { API_BASE, SAAS_BASE } from '@src/common/constants';
import { isInSAAS } from '@src/lib/env_detect';
import { aggr_product_list_request, aggr_product_list_response } from './type';
import fetch from '@src/lib/request/fetch';

const BASE = isInSAAS ? SAAS_BASE : API_BASE;

export const AggrProductList = async (params: aggr_product_list_request): Promise<aggr_product_list_response> => {
  const url = `${BASE}/selection_api/selection_tool/aggr_product_list`;
  return await fetch({
    url,
    method: 'POST',
    params: {
      ...params,
    },
  });
};
