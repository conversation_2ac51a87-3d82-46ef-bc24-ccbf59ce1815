import { API_BASE as AWEME_API_BASE, L_API_BASE, SAAS_BASE } from 'common/constants/index';
import fetch from '@src/lib/request/fetch';
import {
  LiveTabListResponse,
  LiveMachineAuditParams,
  LiveBindPromotionParams,
  LiveAddPromotionParams,
  StarAtlasTabDetailResponse,
  MerchStarAtlasSection,
  MachineAuditResponse,
  MerchListResponse,
  Merch,
  MerchSearchParams,
  AuthorityResponse,
  ShopRiskVerifyParams,
  ShopRiskVerifyResponse,
  CertificationSubmitParams,
  CertificationSubmitResponse,
  IPopupParams,
  IPopupResponse,
  AlertResponse,
  ISetAdAuthParams,
  ISetAdAuthResponse,
  RemindMessageResponse,
  LiveBindOperatePromotionParams,
  QualificationResponse,
  ReasonList,
  BatchBindRequest,
  BatchBindResponse,
  LiveBindPromotions,
  CommonAlertResponse,
  KolRiskVerifyResponse,
  AlertRequest,
  LiveTabListRequestBusinessScene,
  GetPmtByLinkRequest,
  GetTabPromotionRequest,
} from '../types';
import { ApiResponse } from 'types/request';
import { ListState } from 'components/list/state';
import { isInSAAS, queryParamsFromGlobal } from '@src/lib/env_detect';
import { omit } from '@byted-flash/utils';
import { PerformanceLifecycle, remewReport } from '../perf';
import { setSkuMode } from '@alliance-mobile/platform-merchant-common/src/utils/better-price-replace';

const API_BASE = isInSAAS ? SAAS_BASE : AWEME_API_BASE;

const resolveImgUrl = (src: string): string => {
  return src.replace(/\.webp$/, '.png');
};

function coerceMerchListResponse(res: MerchListResponse) {
  const {
    promotions: _promotions,
    has_more,
    live_room_type = '',
    count,
    total,
    feed_id = '',
    log_pb = '',
    summary_promotions = [],
  } = res;
  const promotions = _promotions || [];
  const items = promotions.map(item => ({
    ...item,
    live_room_type,
    cover: typeof item.cover === 'string' ? resolveImgUrl(item.cover) : item.cover?.url_list?.[0] || '',
    logPb: log_pb,
  }));
  return { items, has_more, total: count ?? total, feed_id, summary_promotions };
}

function coerceStarAtlasListResponse(res: StarAtlasTabDetailResponse) {
  const { star_promotions, has_more } = res;
  const items = (star_promotions || []).map(item => {
    item.promotions =
      item.promotions &&
      item.promotions.map(promotion => ({
        ...promotion,
        cover: typeof item.cover === 'string' ? resolveImgUrl(promotion.cover) : promotion.cover?.url_list?.[0] || '',
      }));
    return {
      ...item,
    };
  });
  return { items, has_more };
}

// 直播选品 获取tab list
export async function fetchLiveTabList(
  requestFrom: 'live',
  scene?: LiveTabListRequestBusinessScene,
  returnRes?: boolean
) {
  const url = `${API_BASE}/selection_api/selection_square/get_tab_list`;
  const params = { request_from: requestFrom, business_scene: scene };
  const res = await fetch<LiveTabListResponse>({ url, params });
  const { data = {} } = res || {};
  return returnRes ? data || {} : data.tab_list || [];
}

// 添加黑名单
export const addShopToBlockMerchant = async (params: { shop_ids: string }) => {
  const url = `${API_BASE}/blockMerchant/addBlockMerchant`;
  const res = await fetch<ApiResponse>({ url, params, method: 'post' });
  return res;
};

// 商品关键词搜索
export interface SearchParams {
  page: number;
  tab_id: number;
}

export async function searchMerchList(params: MerchSearchParams & SearchParams) {
  const url = `${API_BASE}/selection_api/selection_square/tab_promotion_search`;
  const { count, title, page, tab_id, commonFilter } = params;
  const res = await fetch<MerchListResponse>({
    url,
    params: {
      key_word: title,
      size: count,
      tab_id,
      page,
      scene: 1,
      ...(Object.keys(commonFilter).length > 0
        ? { common_filter: encodeURIComponent(JSON.stringify(commonFilter)) }
        : {}),
    },
  });
  return coerceMerchListResponse(res.data);
}

// 根据链接搜索商品
export async function searchMerchListByLink(link: string) {
  const url = `${API_BASE}/selection_api/link/pmt`;
  const params: GetPmtByLinkRequest = {
    promotion_link: link,
    scene: 1,
    need_same_style_list: false,
    new_card: false,
    need_same_style_summary_card_list: true,
  };
  const res = await fetch<MerchListResponse>({ url, params });
  return coerceMerchListResponse(res.data);
}

// 商品机审
export async function promotionMachineAudit(params: LiveMachineAuditParams) {
  const url = `${API_BASE}/live/pick/promotion/audit/`;
  const res = await fetch<MachineAuditResponse & ApiResponse>({
    url,
    params: {
      ...params,
    },
  });
  return res;
}

export function genLiveTabDetailFetcher(tab_id: number) {
  return async function fetchLiveTabDetail(params: ListState<Merch> & GetTabPromotionRequest) {
    const url = `${API_BASE}/selection_api/selection_square/get_tab_pmt`;
    const { page, pageSize, cursor, commonFilter, feedId, scene } = params;
    remewReport.addPerformanceInfo('fetchLiveTabDetail', PerformanceLifecycle.T_MAIN_FETCH_START, Date.now());
    const res = await fetch<MerchListResponse>({
      url,
      params: {
        scene,
        page,
        count: pageSize,
        cursor,
        tab_id,
        source: 'live',
        feed_id: feedId || '',
        ...(Object.keys(commonFilter).length > 0
          ? { common_filter: encodeURIComponent(JSON.stringify(commonFilter)) }
          : {}),
      },
    });

    // 同款替换实验标记，0 对照组，1 实验组
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore 字段更新延迟
    setSkuMode(res.data?.extra?.sku_same_price_entrance === '1');

    remewReport.addPerformanceInfo('fetchLiveTabDetail', PerformanceLifecycle.T_MAIN_FETCH_END, Date.now());
    return coerceMerchListResponse(res.data);
  };
}

interface StarAtlasDetailParams extends ListState<MerchStarAtlasSection> {
  keyWord?: string;
  type?: 'search';
}
export async function fetchStarAtlasTabDetail(params: StarAtlasDetailParams) {
  const { page, pageSize, keyWord, cursor, type } = params;
  const url =
    type === 'search'
      ? `${API_BASE}/selection_api/selection_square/tab_promotion_search`
      : `${API_BASE}/selection_api/selection_square/get_tab_pmt`;
  const res = await fetch<StarAtlasTabDetailResponse>({
    url,
    params: {
      page,
      count: pageSize,
      tab_id: 7,
      cursor,
      key_word: keyWord,
      source: 'live',
    },
  });
  return coerceStarAtlasListResponse(res.data);
}

export async function fetchStarAtlasTabSearch(params: StarAtlasDetailParams) {
  const url = `${API_BASE}/selection_api/selection_square/tab_promotion_search`;
  const { page, pageSize, keyWord, cursor } = params;
  const res = await fetch<StarAtlasTabDetailResponse>({
    url,
    params: {
      page,
      count: pageSize,
      tab_id: 7,
      cursor,
      key_word: keyWord,
      source: 'live',
    },
  });
  return coerceStarAtlasListResponse(res.data);
}

export async function bindPromotionToLive(params: LiveBindPromotionParams) {
  const url = `${API_BASE}/live/pick/promotion/bind/`;
  const allParams = { ...params, room_id: queryParamsFromGlobal.room_id || 0 };
  const res = await fetch<ApiResponse>({
    url,
    params: allParams,
    method: 'POST',
  });
  return res;
}

export async function addPromotionToShopWindow(params: LiveAddPromotionParams) {
  const url = `${API_BASE}/live/pick/add/promotion/additional`;
  const res = await fetch<ApiResponse>({
    url,
    params,
    method: 'POST',
  });
  return res;
}

// 获取账号权限
export async function fetchAuthority() {
  const url = `${API_BASE}/live/pick/author/authority/`;
  const res = await fetch<AuthorityResponse>({ url });
  return res.authority;
}

// 获取已选商品列表
export async function fetchPickedMerchList() {
  const url = `${API_BASE}/live/pick/promotion/list/`;
  const res = await fetch<MerchListResponse>({
    url,
    params: {
      room_id:
        (Array.isArray(queryParamsFromGlobal.room_id)
          ? queryParamsFromGlobal.room_id[0]
          : queryParamsFromGlobal.room_id) || 0,
    },
  });
  return coerceMerchListResponse(res);
}

export async function fetchShopRiskVerify(params: ShopRiskVerifyParams) {
  const url = 'https://aweme.snssdk.com/aweme/v2/shop/risk/verify';
  const res = await fetch<ShopRiskVerifyResponse>({
    url,
    params,
  });
  return res;
}

export async function certificationSubmit(params: CertificationSubmitParams) {
  const url = 'https://webcast.amemv.com/webcast/certification/submit/';
  const res = await fetch<CertificationSubmitResponse>({
    url,
    params,
    method: 'POST',
  });
  return res;
}

export const fetchPopup = async (params: IPopupParams) => {
  const url = `${API_BASE}/author/v1/popup`;
  const { pop_type } = params;
  const res = await fetch<IPopupResponse>({
    url,
    params: {
      pop_type,
    },
  });
  return res;
};

/**
 * 电商权限通用弹窗接口
 * @param enter_from
 */
export const whetherShowAlert = async (enter_from: number): Promise<AlertResponse> => {
  const url = 'https://aweme.snssdk.com/aweme/v2/shop/permission/alert/';
  const res = await fetch<AlertResponse>({
    url,
    params: {
      enter_from,
    },
  });
  return res;
};

// 设置广告权限
export const setAdAuth = async (params: ISetAdAuthParams) => {
  const url = `${L_API_BASE}/ies/v2/author/setAdAuth`;
  const res = await fetch<ISetAdAuthResponse>({
    url,
    method: 'POST',
    params: {
      ...params,
    },
  });
  return res;
};

// 获取全局提示信息【顶侧横幅】
export const getRemindMessage = async () => {
  const url = `${API_BASE}/live/pick/message/remind/`;
  const res = await fetch<RemindMessageResponse>({
    url,
    params: {
      room_id:
        (Array.isArray(queryParamsFromGlobal.room_id)
          ? queryParamsFromGlobal.room_id[0]
          : queryParamsFromGlobal.room_id) || 0,
    },
  });
  return res;
};

// 操作安心购
export const setAnXinGou = async (params: {
  operate: 0 | 1; // 0 关， 1 开
}) => {
  const url = `${API_BASE}/live/pick/peace/operate`;
  const res = await fetch<ApiResponse>({
    url,
    method: 'POST',
    params: {
      operate: params.operate,
      flashs: '',
      promotions: '',
      room_id:
        (Array.isArray(queryParamsFromGlobal.room_id)
          ? queryParamsFromGlobal.room_id[0]
          : queryParamsFromGlobal.room_id) || 0,
    },
  });
  return res;
};

interface BindResponse extends ApiResponse {
  data: {
    bind_status: number;
    bind_msg: string;
    bind_reason: string;
    product_id: string;
    title: string;
    guide_info: {
      title: string;
      content: string;
      confirm_text: string;
      cancel_text: string;
      guide_link: string;
    };
    is_toast: boolean;
  };
}

// 直播间解绑商品
export async function unbindPromotionToLive(params: LiveBindOperatePromotionParams) {
  const url = `${API_BASE}/selection_api/live/unbind`;
  const res = await fetch<BindResponse>({
    method: 'POST',
    url,
    params: {
      json_form: JSON.stringify({
        unbind_pmts: [
          {
            ...params,
          },
        ],
      }),
    },
  });
  return res;
}

// 直播间绑定品
export async function bindPromotionToLiveNew(params: LiveBindOperatePromotionParams) {
  const url = `${API_BASE}/selection_api/live/bind`;
  const res = await fetch<BindResponse>({
    url,
    method: 'POST',
    params,
  });
  return res;
}

// 资质检查
export const qualificationCheck = async () => {
  const url = `${API_BASE}/api/anchor/qualification/check`;
  const res = await fetch<QualificationResponse>({
    url,
    params: {
      source: 'live_select_page',
    },
  });
  return res;
};

// 获取直播添品达人挂车提示
export const getPairLimitTip = async () => {
  const res = await fetch<{
    data?: {
      is_show: boolean; // 是否需要展示tips
      time_text: string; // 有值时第1次上线；否则第2次
    };
  }>({
    url: `${API_BASE}/selection_api/live/tips`,
  });
  return res;
};

// 申请豁免次数
export const applyPairLimitRelease = async (params: { reason_list: string; reason?: string; product_id: string }) => {
  const res = await fetch<{ data: { used_exempt_times: number } }>({
    url: `${API_BASE}/selection_api/live/apply_release`,
    method: 'POST',
    params,
  });
  return res;
};

// 查询豁免次数
export const getPairLimitRelease = async () => {
  const res = await fetch<{
    data: {
      used_exempt_times: number;
      total_times: number;
      reason_list: ReasonList;
    };
  }>({
    url: `${API_BASE}/selection_api/live/apply_release_info`,
  });
  return res.data;
};

// 获取会话对象的IMID
export async function getIMAccount(data?: {
  account_type?: number; // 用户类型 0 当前，1 达人， 2商家
  account_id?: string | number; // 1 -> user_id, 2 -> shop_id
  account_app_id?: string; // 非当前登录用户，需要传账号对应的appId
  entrance?: number; // im入口 1:抖音-精选联盟-达人广场
}): Promise<any> {
  return await fetch({
    url: `${API_BASE}/connection/im/account`,
    params: data,
  });
}

/** 批量直播绑定接口 */
export async function liveBatchBind(list: LiveBindPromotions[]): Promise<BatchBindResponse> {
  return await fetch({
    url: `${API_BASE}/selection_api/live/batch_bind`,
    params: {
      json_form: JSON.stringify({
        promotions: list,
      }),
    },
    method: 'POST',
  });
}

export async function checkCommonAlert(params: AlertRequest) {
  return await fetch<CommonAlertResponse>({
    url: `${API_BASE}/selection_api/common/alert`,
    params: params,
  });
}

export async function fetchKolRiskVerify() {
  const url = `${API_BASE}/selection_api/kol/risk_verify`;
  const res = await fetch({
    url,
  });
  return res as KolRiskVerifyResponse;
}

export enum FilterSource {
  SEARCH_PMT = 0, // 搜索全部tab
  SEARCH_SHOP = 1, // 搜索店铺tab
  SEARCH_SHOPDETAIL = 2, // 搜索店铺详情
  FEED = 3, // 选品广场feed
  FEED_MENDEL = 4, // 三级类目feed
  SEARCH_PMT_PC = 5, // 专属选品广场搜索筛选项
  FEED_PMT_PC = 6, // 专属选品广场首页筛选项
  FEED_LUCKY = 7, // 福袋
  SEARCH_WUYOUDAI = 8, // 移动端无忧带落地页搜索筛选项
  FEED_WUYOUDAI = 9, // 移动端无忧带落地页筛选项
  SEARCH_WUYOUDAI_PC = 10, // PC端中小+专属选品广场无忧带落地页筛选项
  SEARCH_PMT_COMMON_PC = 11, // PC端普通选品广场搜索筛选项
  CART = 12, // 选品车移动端
  CART_PC = 13, // 选品车pc端
  LIVE_FEED = 14, // 移动端直播推荐商品
  RECENTLYGOODS = 17, // 最近带货商品
  ExclusiveCooperate = 19, // 专属佣金，原“定向合作”
}
export interface GetFilterRequest {
  /**
   *区分页面
   */
  filter_source: FilterSource;
  /**
   *搜索词
   */
  search_text?: string;
}
export interface CommonFilter {
  /**
   *filed 前后传递
   */
  field?: string;
  /**
   *对外展示的中文名称  示例：带货类型
   */
  name?: string;
  /**
   *选择类型
   */
  select_type?: FilterSelectType;
  sub_text_filter?: SubTextFilter;
  range_pair?: RangePair;
  /**
   *解释，eg：高佣后的问好提示
   */
  explanation?: string;
}
export enum FilterSelectType {
  ROOT_SINGLE = -10, // 非叶子节点且需要单选
  ROOT = -1, // 根节点
  SINGLE = 0, // 文字单选
  MULTI = 1, // 文字多选
  RANGE = 2, // range
  DROP = 4, // 下拉选择器
  RADIO_DROP_MULTI = 12, // 单选+多选
  SINGLE_ONE_LINE = 13, // 文字单选(每个筛选项但独一行布局)
}
export interface GetFilterData {
  /**
   *快捷筛选
   */
  quick_filter?: QuickFilter[];
  /**
   *普通筛选
   */
  common_filter?: CommonFilter[];
}
export interface GetFilterResponse {
  /**
   *状态码
   */
  code: number;
  /**
   *message
   */
  msg?: string;
  log_id?: string;
  data?: GetFilterData;
}
export interface QuickFilter {
  /**
   *filed 前后传递
   */
  field?: string;
  /**
   *对外展示的中文名称  示例：带货类型
   */
  name?: string;
  /**
   *子文字筛选
   */
  text_pair?: TextPair[];
  /**
   *选择类型, 0:单选, 1:多选
   */
  select_type?: FilterSelectType;
  /**
   *全部筛选项中的父节点
   */
  parent_field?: string;
}
export interface RangePair {
  min?: string;
  max?: string;
  unit_name?: string;
}
export interface SubTextFilter {
  /**
   *子筛选filed 前后传递
   */
  text_pair?: TextPair[];
  /**
   *解释
   */
  explanation?: string;
  /**
   *是否可以折叠: 0否1是
   */
  is_fold?: boolean;
  /**
   *默认状态：0展开1折叠
   */
  default_fold?: boolean;
  /**
   *子类型(筛选项两级嵌套，目前PC场景有用到)
   */
  sub_text_filter?: CommonFilter[];
}
export interface TextPair {
  /**
   *子筛选field 前后传递
   */
  name?: string;
  /**
   *子筛选对外展示的中文名称 示例：直播，短视频
   */
  field?: string;
}

export async function fetchFilter(params: GetFilterRequest) {
  const url = `${API_BASE}/selection_api/selection_square/get_filter`;
  const response = await fetch({ url, params });
  return response as GetFilterResponse;
}

export interface ApiSyncProductMigrateRequest {
  source?: ProductMigrateSource;
}
export enum ProductMigrateSource {
  Cupboard = 1,
}
export interface ApiSyncProductMigrateData {
  /** 是否成功同步 */
  is_synced: boolean;
}
export interface ApiSyncProductMigrateResponse {
  /**
   *状态码
   */
  code: number;
  /**
   *message
   */
  msg?: string;
  log_id?: string;
  data?: ApiSyncProductMigrateData;
}
export interface Button {
  /**
   *按钮上的文案
   */
  button_text?: string;
  /**
   *按钮的事件类型
   */
  event_type?: EventType;
  /**
   *按钮点击触发的事件
   */
  event?: Event;
}
export interface Event {
  /**
   *橱窗调起吸底弹窗文案
   */
  content?: string;
  /**
   *橱窗调起吸底弹窗次级文案
   */
  sub_content?: string;
  /**
   *吸底弹窗的图片链接
   */
  pic_url?: string;
}
export enum EventType {
  None = 0, // 单纯展示无操作，收藏弹窗
  CupboardMigrate = 1, // 橱窗迁移，点击后调起吸底弹窗，展示事件中的文案
}
export interface NoticeContent {
  /**
   *通知文案
   */
  text?: string;
  /**
   *收藏吸底弹窗次级文案
   */
  sub_text?: string;
  /**
   *收藏图片链接
   */
  pic_url?: string;
  /**
   *通知按钮
   */
  buttons?: Button[];
}
export interface NoticeEntity {
  /**
   *通知展示类型
   */
  notice_type?: NoticeType;
  /**
   *通知展示内容
   */
  content?: NoticeContent;
}
export enum NoticeType {
  StickyTop = 1, // 吸顶
  StickyBottom = 2, // 吸底
}

export const productMigrate = async (params: ApiSyncProductMigrateRequest) => {
  const url = `${API_BASE}/selection_cart_api/sync/product_migrate`;
  const response = await fetch({ url, params });
  return response as ApiSyncProductMigrateResponse;
};

export interface ApiNoticeGetNoticeRequest {
  scene?: NoticeScene;
}
export enum NoticeScene {
  CartSyncLiveList = 1, // 直播添品 橱窗
  CartSyncVideoList = 2, // 短视频添品  橱窗&收藏
  CartSyncSelectionSquare = 3, // 选品广场我的 收藏
}
export interface ApiNoticeGetNoticeData {
  notice?: NoticeEntity[];
}
export interface ApiNoticeGetNoticeResponse {
  /**
   *状态码
   */
  code: number;
  /**
   *message
   */
  msg?: string;
  log_id?: string;
  data?: ApiNoticeGetNoticeData;
}
export interface Button {
  /**
   *按钮上的文案
   */
  button_text?: string;
  /**
   *按钮的事件类型
   */
  event_type?: EventType;
  /**
   *按钮点击触发的事件
   */
  event?: Event;
}
export interface Event {
  /**
   *橱窗调起吸底弹窗文案
   */
  content?: string;
  /**
   *橱窗调起吸底弹窗次级文案
   */
  sub_content?: string;
  /**
   *吸底弹窗的图片链接
   */
  pic_url?: string;
}
export interface NoticeContent {
  /**
   *通知文案
   */
  text?: string;
  /**
   *收藏吸底弹窗次级文案
   */
  sub_text?: string;
  /**
   *收藏图片链接
   */
  pic_url?: string;
  /**
   *通知按钮
   */
  buttons?: Button[];
}
export interface NoticeEntity {
  /**
   *通知展示类型
   */
  notice_type?: NoticeType;
  /**
   *通知展示内容
   */
  content?: NoticeContent;
}

export const getNotice = async (params: ApiNoticeGetNoticeRequest) => {
  const url = `${API_BASE}/selection_cart_api/notice/get_notice`;
  const response = await fetch({ url, params });
  return response as ApiNoticeGetNoticeResponse;
};

export const AnchorShopAb = async <T extends Record<string, any>>(req: {
  name_space: string; // 命名空间，对应设置的第一层key，异动橱窗尽量定义为buyin_anchor_shop
  ab_filters: string[]; // 分流过滤参数，多个过滤参数用英文逗号,拼接，对应AB分流参数buyin_anchor_shop_flight，不填则默认为default，进入所有buyin_anchor_shop_flight不为空的实验分流
}): Promise<Partial<T>> => {
  const { ab_filters = [] } = req;
  const params = { ...omit(req, ['ab_filters']), ab_filters: ab_filters.join(',') };

  const url = `${AWEME_API_BASE}/api/anchor/shop/ab`;
  const res = await fetch({
    url,
    params: { ...params },
  });
  try {
    const result = ((res.data as any)?.ab_result as string) || '{}';
    const obj: Record<string, any> = JSON.parse(result);
    return obj as Partial<T>;
  } catch (err) {
    return {} as Partial<T>;
  }
};

/** 商品卡消重 */
export const writeImpression = async promotionId => {
  const url = `${AWEME_API_BASE}/selection_api/common/write_impression`;
  await fetch<ISetAdAuthResponse>({
    url,
    method: 'POST',
    params: {
      ids: promotionId,
      id_type: 34,
      channel_id: 451,
    },
  });
};

export enum RequestSource {
  BUYIN_MOBILE_ANCHOR = 1, // 主播版移动中控
  BUYIN_MOBILE_ASSISTANT = 2, // 助手版移动中控
  MOBILE_CREATIVE_CENTER = 3, // 创意
}

export interface LiveSettingsGetRequest {
  setting_options_str?: string; // 请求选项,表明获取哪些直播配置,多个SettingOption用逗号分割
  source: RequestSource; // 请求来源
}

export interface LiveSettingsData extends ApiResponse {
  comment_top_ab_open?: boolean;
  is_merchant_author?: boolean;
  show_prompt_in_screen?: boolean;
}

// 获取主播配置
export const author_live_settings_get = async (params: LiveSettingsGetRequest): Promise<LiveSettingsData> => {
  const url = `${API_BASE}/api/author/live/settings_get`;
  const res = await fetch({
    url,
    params: { ...params },
  });
  return res;
};

export interface FilterInfo {
  value?: string[];
}
interface PmtCard4KolResponse extends ApiResponse {
  data?: {
    promotions: Merch[];
  };
}

// 获取 PmtCard4Kol 协议商品卡
export const getPmtCard4Kol = async (params: {
  promotionId: string;
  productId: string;
}): Promise<PmtCard4KolResponse> => {
  const url = `${API_BASE}/selection_api/common/get_product_list_info`;
  const res = await fetch({
    url,
    params: {
      pmt_ids: params.promotionId,
      prod_ids: params.productId,
      pack_scene_conf: 'media_draft_cart',
      material_info: false,
    },
  });
  return res;
};

export interface AbParamRequest {
  /**
   *ab记录选品广场用户
   */
  selection_square_entrance?: string;
  /**
   *添品工具过滤场景
   */
  select_tool?: string;
}

export interface AbParamData {
  /**
   *商品卡版本 0：对照组(老版本）；1：实验1（无佣金率，突出佣金、已售）；2：实验2（变弱已售）；3：实验3（展示佣金率）；4：实验4（突出售价）；5：实验5（店铺入口）；6：实验6（无佣金率，突出佣金、已售+榜单入口）
   */
  leader_board_version?: string;
  /**
   *通用的AB参数map
   */
  ab_params_map?: Record<string, string>;
}
export interface AbParamResponse extends ApiResponse {
  /**
   *状态码
   */
  code?: number;
  /**
   *message
   */
  msg?: string;
  data?: AbParamData;
}

export async function getABParam(params?: AbParamRequest): Promise<AbParamResponse> {
  const res = await fetch({
    url: `${API_BASE}/selection_api/common/ab_param`,
    params: params,
  });
  return res;
}

export interface FilterInfoRequest {
  /**
   *场景标识
   */
  scene?: string;
  /**
   *页面id
   */
  page_id?: string;
}

export interface CommonFilterInfo {
  /**
   *选项字段
   */
  field?: string;
  /**
   *展示名称
   */
  name?: string;
  /**
   *选项类型
   */
  filter_type?: FiterType;
  /**
   *选项属性
   */
  attr?: FilterAttr;
  /**
   *子选项信息
   */
  options?: FilterOptions;
  /**
   *range信息
   */
  range_pair?: FilterRangePair;
  /**
   *选项信息
   */
  sub_common_filter?: CommonFilterInfo[];
  /**
   *字段解释
   */
  tip?: string;
}
export enum CustomOptionType {
  DEFAULT = 0,
  RANGE = 1, // 自定义类型
}
export interface CustomType {
  filter_range_pair?: FilterRangePair;
}
export interface FilterAttr {
  /**
   *是否可以折叠
   */
  fold?: boolean;
  /**
   *是否默认折叠
   */
  default_fold?: boolean;
  /**
   *选项提示
   */
  tip?: string;
  /**
   *是否独占一行
   */
  single_line?: boolean;
  /**
   *是否有"不限"选项
   */
  have_unlimited?: boolean;
  /**
   *更多信息
   */
  select_all?: boolean;
}
export interface FilterInfoData {
  common_filter: CommonFilterInfo[];
  quick_filter?: QuickFilterInfo[];
  tab_filter?: QuickFilterInfo[];
}
export interface FilterInfoResponse {
  /**
   *状态码
   */
  code?: number;
  /**
   *message
   */
  msg?: string;
  data?: FilterInfoData;
}
export interface FilterOptionItem {
  /**
   *展示名称
   */
  name?: string;
  /**
   *选项值
   */
  value?: string;
  /**
   *图片链接，不为空优先展示
   */
  pic?: FilterPicItem;
  /**
   *操作类型，默认不传
   */
  custom_optional_type?: CustomOptionType;
  /**
   *不同操作类型对应的解析信息
   */
  custom_type?: CustomType;
  /**
   *额外信息
   */
  extra?: Record<string, string>;
}
export interface FilterOptions {
  /**
   *子选项列表
   */
  items?: FilterOptionItem[];
  /**
   *默认选中值
   */
  default_values?: string[];
}
export interface FilterPicItem {
  /**
   *图片链接，不为空优先展示
   */
  url?: string;
  /**
   *筛选后的背景色
   */
  color?: string;
}
export interface FilterRangePair {
  /**
   *最小值标签
   */
  min_label?: string;
  /**
   *最大值标签
   */
  max_label?: string;
  /**
   *单位
   */
  unit_name?: string;
  /**
   *[min_val, max_val] 有效区间值，闭区间
   */
  valid_value?: string[];
  /**
   *[default_min_val, default_max_val] 默认区间值, "-" 表示不设置
   */
  default_value?: string[];
}
export enum FiterType {
  Text = 0, // 文字单选
  MultiText = 1, // 文字多选
  Range = 2, // 数字范围
  Select = 3, // 单选下拉
  MultSelect = 4, // 多选下拉
  Group = 5, // 使用每个列表中的的值单独都是一个filter
}
export interface QuickFilterInfo {
  /**
   *选项字段
   */
  field?: string;
  /**
   *展示名称
   */
  name?: string;
  /**
   *选项类型
   */
  filter_type?: FiterType;
  /**
   *快捷筛选值 (若 filter_type == Range, 表示[minVal, maxVal])
   */
  values?: string[];
  /**
   *子选项信息
   */
  options?: FilterOptions;
  /**
   *数字范围选项信息
   */
  range_pair?: FilterRangePair;
  /**
   *图片,优先展示
   */
  pic?: FilterPicItem;
}

export async function GetFilterInfo(params: FilterInfoRequest): Promise<FilterInfoResponse> {
  const res = await fetch({
    url: `${API_BASE}/selection_api/common/filter_info`,
    params: params,
  });
  return res;
}
