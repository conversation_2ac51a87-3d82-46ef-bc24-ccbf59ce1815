import { useEffect, useMemo } from 'react';
import { isAndroid } from '@src/common/kit/env';
import { PortalTab } from '../types';
import { TAB_KEYS } from '../constants';

// 解决 android 机型键盘收起之后没有失焦导致的底部按钮没有显示问题
export const useAndriodKeyboardResize = (onResizeCallback: (isKeyboardOpen: boolean) => void) => {
  useEffect(() => {
    if (!isAndroid) {
      return;
    }

    let lastHeight = 0;

    const resizeListener = () => {
      const currHeight = document.documentElement.clientHeight || document.body.clientHeight;

      if (lastHeight && lastHeight < currHeight) {
        onResizeCallback(false);
      } else {
        onResizeCallback(true);
      }

      lastHeight = currHeight;
    };

    window.addEventListener('resize', resizeListener);

    return () => {
      window.removeEventListener('resize', resizeListener);
    };
  }, []);
};

const shopWindowTab: PortalTab = {
  key: TAB_KEYS.SHOPWINDOW,
  title: '我的橱窗',
  isRendered: true,
  type: 1,
};

const pickingCartTab: PortalTab = {
  key: TAB_KEYS.PICKING_CART,
  title: '选品车',
  isRendered: true,
  type: 20,
};

export const useDefaultTab = () => {
  return pickingCartTab;
};
