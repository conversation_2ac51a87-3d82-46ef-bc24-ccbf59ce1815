import { PortalTab, ItemType, UserActionRecordProps, ActionRecordProps } from '../types';
import { queryParamsFromGlobal as query, getUrlByEnv, appSmallName } from '@src/lib/env_detect';
import { appendQuery, genSchema } from '@src/lib/util/merch_picking';
import { SchemaBoolean } from 'types/schema';
import { Promotion } from '@src/types/product';
import {
  TAB_KEYS,
  TAB_KEYS_API_PARAMS,
  APP_ID,
  IMChatFromType,
  PAIR_LIMIT_TIPS_KEY,
  SEARCH_MODE_TAB_KEYS,
} from '../constants';
import { getIMAccount, writeImpression } from './api';
import toast from '@src/components/toast';
import { sendEvent } from '@alliance-mobile/event';

// 记录达人行为层级
function userRecordInstance() {
  let userActionMap: ActionRecordProps = {
    actionLevel1: 'live',
    actionLevel2: '',
    actionLevel3: '',
  };
  const userActionStack: ActionRecordProps[] = [];
  return {
    setUserAction: (props: ActionRecordProps = {}) => {
      userActionMap = { ...userActionMap, ...props };
      userActionStack.push(userActionMap);
    },
    getUserActionMap: (): UserActionRecordProps => {
      const { actionLevel1, actionLevel2, actionLevel3, ...rest } = userActionMap;
      return {
        position: 'list',
        pick_first_source: actionLevel1,
        pick_second_source: actionLevel2,
        pick_third_source: actionLevel3,
        ...rest,
      };
    },
    fallbackRecord: () => {
      if (userActionStack.length <= 1) {
        return;
      }
      userActionStack.pop();
      userActionMap = userActionStack[userActionStack.length - 1];
    },
  };
}
export const userActionRecord = userRecordInstance();

// 判断localStorage中某个key的值是否等于给定的默认值
export function isLSItemEqual(key: string, dft: boolean) {
  try {
    const v = localStorage.getItem(key);
    return v ? v === `${dft}` : true;
  } catch (e) {}
  return false;
}

// 识别淘口令
export function isTaobaoCommand(text: string) {
  return /\.tb\.cn|淘♂寳♀|◇綯℡寳|\B([^\w])\w{11}\1\B/.test(text);
}

// 识别淘宝商品链接, 包括淘口令
export function isTaobaoMerchLink(link: string) {
  const patterns = ['taobao.com', 'tmall.com'];
  return patterns.some(pattern => link.indexOf(pattern) !== -1) || isTaobaoCommand(link);
}

export function isSameTab(first: Partial<PortalTab>, second: Partial<PortalTab>): boolean {
  return Boolean(first) && first.key === second.key;
}

export function genAwemeGoodsEditor(promotion_id: string): string {
  const url = appendQuery(getUrlByEnv('https://lm.jinritemai.com/views/live/good_editor'), {
    ...query,
    hide_nav_bar: 1,
    enter_from: 'webcast',
    // hide_status_bar: 1,
    __live_platform__: 'webcast',
    status_bar_color: 'black',
    promotion_id,
    ...userActionRecord.getUserActionMap(),
  });
  return `sslocal://webcast_webview?url=${encodeURIComponent(url)}`;
}

export function genMachineAuditRule(title: string, url: string) {
  location.href = genSchema({
    url,
    title,
    hide_more: SchemaBoolean.YES,
    bounce_disable: SchemaBoolean.YES,
    need_safe_area: SchemaBoolean.YES,
    hide_close_btn: SchemaBoolean.YES,
  });
}

export function sendLog(eventName: string, params: Record<string, unknown> = {}) {
  sendEvent(eventName, params);
}

// 识别淘宝商品
export function isTaobaoMerch(itemType: ItemType) {
  return [ItemType.TAOBAO, ItemType.TAOBAO_FEED, ItemType.TAO_COMMAND].indexOf(itemType) !== -1;
}

// 识别联盟商品
export function isAllianceMerch(itemType: ItemType) {
  return !isTaobaoMerch(itemType) && !isYMATOUMerch(itemType);
}

// 识别联盟商品
export function isYMATOUMerch(itemType: ItemType) {
  return itemType === ItemType.YMATOU;
}

// 识别需要账户资质验证的商品
export function isAllianceAuthNeedMerch(itemType: ItemType) {
  return itemType === ItemType.ALLIANCE;
}
// 公共的埋点参数
export const COMMON_STAT_PARAMS = {
  EVENT_ORIGIN_FEATURE: 'TEMAI',
  page_name: 'shoppingWindow',
  data_type: 'commerce_data',
  pick_source_id: '',
};

export function jumpToOpenOverseasAccountPage() {
  const url = getUrlByEnv('https://fxg.jinritemai.com/h5/_daren/account/pay-manage');
  location.href = genSchema({
    url,
    use_ui: 1,
    hide_more: 1,
    show_more_button: 1,
    copy_link_action: 0,
  });
}

/**
 *
 * 获取直播间绑定api的参数映射关系
 */
export function getBindApiParams(isLinkSearch: boolean, tabKeys: string) {
  if (isLinkSearch) {
    return TAB_KEYS_API_PARAMS.SEARCH;
  }
  let bindSource = 0;
  switch (tabKeys) {
    case TAB_KEYS.STORE:
      bindSource = TAB_KEYS_API_PARAMS.SHOP;
      break;
    case TAB_KEYS.SHOPWINDOW:
      bindSource = TAB_KEYS_API_PARAMS.WINDOW;
      break;
    case TAB_KEYS.EXCLUSIVE:
      bindSource = TAB_KEYS_API_PARAMS.EXCLUSIVE_PLAN;
      break;
    case TAB_KEYS.STAR_ATLAS:
      bindSource = TAB_KEYS_API_PARAMS.STAR;
      break;
    case TAB_KEYS.PROTOCOL:
      bindSource = TAB_KEYS_API_PARAMS.PROTOCOL;
      break;
    case TAB_KEYS.HISTORY_ADD:
      bindSource = TAB_KEYS_API_PARAMS.HISTORY;
      break;
    case TAB_KEYS.PICKING_CART:
      bindSource = TAB_KEYS_API_PARAMS.PICK_CART;
      break;
    case TAB_KEYS.RECOMMENDED_PRODUCT:
      bindSource = TAB_KEYS_API_PARAMS.RECOMMENDED_PRODUCT;
      break;
    default:
  }
  return bindSource;
}

/**
 *  商品曝光埋点
 * TODO: 更加定制化
 */
const intersectObserver = IntersectionObserver
  ? new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.intersectionRatio > 0) {
            const context = userActionRecord.getUserActionMap();
            const teaData = entry.target.getAttribute('tea-data') || '{}';
            const needImpr = entry.target.getAttribute('need-impression') || '';
            const merch: Promotion = JSON.parse(teaData);
            const extra = merch.extra || {};

            sendLog('show_product_outer', {
              ...COMMON_STAT_PARAMS,
              ...context,
              ...extra,
              product_id: merch.product_id || '',
              commodity_id: merch.promotion_id || '',
              commodity_type: merch.item_type || 0,
              is_rights: merch.privilege_info?.privilege_id || '',
              is_anxingou_live: merch.live_room_type ?? '',
              log_pb: merch.logPb,
              commodity_location: merch.commodity_location,
              recall_source: merch.recall_source,
              highprice_warning_show: merch?.price_good_same_style?.text_right?.text,
              pick_third_source: merch?.pick_third_source,
            });

            if (needImpr) {
              writeImpression(merch.promotion_id || '');
              intersectObserver.unobserve(entry.target);
            }
          }
        });
      },
      {
        threshold: 0.25,
      }
    )
  : {
      observe: () => {},
      unobserve: () => {},
    };

/**
 * 用法：
 * import { intersectObserver } from 'THIS_FILE';
 *
 * domRef.current?.setAttribute('tea-data', JSON.stringify(data));
 * intersectObserver.observe(domRef.current!)
 */
export { intersectObserver };

// 直播挂车缓存工具
export const getPairLimitTipsCache = () => {
  return localStorage.getItem(PAIR_LIMIT_TIPS_KEY) === '1';
};
export const setPairLimitTipsCache = () => {
  localStorage.setItem(PAIR_LIMIT_TIPS_KEY, '1');
};

/**
 * 跳转到电商学院
 */
export const gotoEcomSchool = () => {
  location.href = genSchema({
    url: `https://school.jinritemai.com/doudian/web/article/aHRtxwvnJJhK?from=buyin_author`,
    use_ui: 1,
    hide_more: 1,
    show_more_button: 1,
    copy_link_action: 0,
  });
};

/**
 * 前往与指定店铺会话
 * @param shopId 店铺ID
 */
export const toChatPageWithShop = (shopId: number) => {
  getIMAccount({
    account_id: shopId,
    account_type: 2, // 0 当前, 1 达人, 2 商家, 3 机构
    account_app_id: `${APP_ID[appSmallName] || 1128}`,
  })
    .then(res => {
      const { code, data, msg } = res;
      if (code === 0) {
        location.href = genSchema({
          url: data?.im_url || 'https://alliance.jinritemai.com/pages/chat',
          status_bar_color: 'ffffff',
          from: IMChatFromType.LIVE_MERCH_PICK,
          friendId: data?.im_id || '',
          buyinId: data?.buyin_account_id || '',
          shopId,
          hide_nav_bar: 1,
          hide_bar: 1,
          should_full_screen: 1,
          status_font_dark: 1,
          experimental_fix_status_bar: 1,
          experimental_adjust_pan: 1,
        });
      } else {
        toast(msg || '网络异常');
      }
    })
    .catch(err => {
      toast(err?.msg || '网络异常');
    });
};

// 过滤出搜索可用的tab，以线上为准搬运过来的，线上逻辑见 apps/fe-alliance-webcast-legacy/src/pages/merch_picking/app.tsx 的searchTabs
export const getSearchTabList = (tabList?: PortalTab[]) => {
  if (!tabList || !tabList?.length) {
    return [];
  }
  return tabList?.filter(tab => SEARCH_MODE_TAB_KEYS?.includes(tab?.key));
};

/*
 * 保留decimal小数位不四舍五入
 */
function formatDecimal(num: number, decimal: number) {
  let res = num.toString();
  const index = res.indexOf('.');
  if (index !== -1) {
    res = res.substring(0, decimal + index + 1);
  } else {
    res = res.substring(0);
  }
  return parseFloat(res).toFixed(decimal);
}

/**
 * 格式化数量  xxx万, xxx亿，最多保留两位小数，截断处理（非四舍五入！！！）
 * @param {number} num 金额/数量 数字
 * @return {string, string} 格式化后字符串，{xxx，'万+'}, {xxx,'亿+'}
 */
// eslint-disable-next-line max-params
export function formatNumber(num: number, validDigit = 2): { stat: string; suffix: string } {
  if (typeof num !== 'number') {
    return { stat: '-', suffix: '' };
  }

  if (num >= 1e4 && num < 1e8) {
    return { stat: `${Number(formatDecimal(num / 1e4, validDigit))}`, suffix: '万+' };
  }
  if (num >= 1e8) {
    return { stat: `${Number(formatDecimal(num / 1e8, validDigit))}`, suffix: '亿+' };
  }
  return { stat: formatDecimal(num, 0), suffix: '' };
}

/*
 * 商品卡金额类数值规范
 * 个、十、百、千，均保留两位小数，为0不补零。（四舍五入）。
 * 超过万时缩略展示，保留两位小数，为0不补零，四舍五入
 */

export function formatPriceNumber(num: number): { integer: string; decimal: string; unit: string } {
  if (typeof num === 'undefined') return { integer: '-', decimal: '', unit: '' };
  let integer = '';
  let decimal = '';
  let numArr = '';
  let unit = '';
  // 小于 10000 保留两位小数，做四舍五入
  if (num < 1e4) {
    numArr = String(Number(num.toFixed(2)));
  }
  if (num >= 1e4 && num < 1e8) {
    numArr = String(Number((num / 1e4).toFixed(2)));
    unit = '万';
  }
  if (num >= 1e8) {
    numArr = String(Number((num / 1e8).toFixed(2)));
    unit = '亿';
  }

  integer = numArr?.split('.')?.[0];
  decimal = numArr?.split('.')?.[1];

  return {
    integer,
    decimal: decimal ? `.${decimal}` : ``,
    unit,
  };
}

export function openVersatile(schema: string, containerID?: string) {
  return new Promise(resolve => {
    const win = window;
    // @ts-ignore
    if (win?.ToutiaoJSBridge?.call) {
      // @ts-ignore
      win.ToutiaoJSBridge.call('closeAndOpen', { schema, containerID }, (res: JSBridgeResponse) => resolve(res));
    }
  });
}
