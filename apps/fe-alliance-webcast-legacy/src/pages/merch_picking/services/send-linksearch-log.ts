import { sendEvent } from '@alliance-mobile/event';
// import via from '@bridge/webcast';

// 链接识别来源 埋点用
export enum LinkSearchScene {
  mobileLive = '移动-直播',
  mobileVideo = '移动-短视频',
  mobileMerchPicking = '移动-选品广场',
  mobileLiveClipBoard = '移动-直播剪切板',
}

export const EVENTNAME = {
  LINKSEARCHCLICK: 'link_search_click',
};

export const sendLog = (eventName: string, otherParams?: Record<string, unknown>, user_id?: string) => {
  const basicParam = {
    data_type: 'commerce_data',
    local_time_ms: Date.now(),
    author_id: user_id,
  };
  const params = {
    ...basicParam,
    ...otherParams,
  };

  sendEvent(eventName, params);

  // via.app.sendLogV3({
  //   eventName,
  //   params,
  // });
};
