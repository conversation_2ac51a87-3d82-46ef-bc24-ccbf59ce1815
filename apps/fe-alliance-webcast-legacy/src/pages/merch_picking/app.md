# Merchandise Picking App Component

## Overview
Main application component for the merchandise picking interface, handling product selection, search functionality, and modal dialogs for a live e-commerce platform.

## Dependencies
- React and React Hooks (useRef, useState, useEffect, useMemo)
- Component libraries: Modal, Checkbox, Toast, ErrorBoundary
- Internal components: PageHeader, MainView, LinkSearchView, ComboSearchView
- Services: searchMerchList, searchMerchListByLink
- Utilities: env_detect, storage, event handling, schema generation
- Context: AppDelegate for state management

## State Management
Key state variables:
- `isopen`: Controls risk reminder modal visibility
- `isLazy`: Controls anti-fatigue checkbox state
- `userInfo`: Stores user authentication information
- `roomId`: Current live room identifier
- `appState`: Main application state from useAppController
- `tabType`: Active tab identifier
- `context`: Application context passed to child components

## Key Functionality
1. **Product Search & Selection**
   - Tab-based navigation between different product sources
   - Link-based and combo search functionality
   - Batch selection and addition of products

2. **Modal Dialogs**
   - Qualification upgrade prompts
   - Risk reminders and confirmations
   - Clipboard paste handling
   - Face recognition integration

3. **Header & Navigation**
   - Responsive header with search and selection controls
   - Platform-specific UI adaptations
   - Shopping bag with item count

4. **Event Handling**
   - Visibility change detection
   - External event subscriptions
   - Cross-module communication via event bus

## Usage
```tsx
<AppDelegate appState={appState} controller={$app} context={context}>
  {/* Component content */}
</AppDelegate>
```

## Notes
- Integrates with multiple platform-specific features (Aweme, SAAS)
- Handles product pairing limits and promotion rules
- Implements performance tracking with remewReport
- Supports both new and legacy UI versions via isMerchV2 flag