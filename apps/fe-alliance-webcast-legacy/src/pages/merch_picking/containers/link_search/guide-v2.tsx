import React from 'react';
import { openSchema } from '@src/common/bridge';
import { genSchema } from '@src/lib/util/merch_picking';
import { PublishContinuePushStream } from '@src/lib/util/event';

import './guide-v2.scss';
export default React.memo(() => {
  const gotoRulePage = () => {
    // 直播中 跳转全屏页需要主动发送 continue_push_stream 事件，否则直播画面会黑屏
    // PublishContinuePushStream里做了新版本和直播中状态的判断
    PublishContinuePushStream();
    openSchema({
      schema: genSchema({
        url: 'https://school.jinritemai.com/doudian/web/article/101805',
      }),
    });
  };
  const steps = [
    {
      content: (
        <>
          1. 仅支持添加
          <i className="link-search-guide__step1-icon link-search-guide__step1-icon-shop" />
          <i className="link-search-guide__step1-icon link-search-guide__step1-icon-cart" />
          我的小店或精选联盟商品链接
        </>
      ),
    },
    {
      content: (
        <>
          2. 需要严格遵守
          <span className="link-search-guide__link-text" onClick={gotoRulePage}>
            《发布违禁商品/信息》实施细则
          </span>
        </>
      ),
    },
  ];
  return (
    <div className="link-search-guide">
      {steps.map((step, index) => (
        <div className="link-search-guide__step" key={index}>
          <p className="link-search-guide__content">{step.content}</p>
        </div>
      ))}
    </div>
  );
});
