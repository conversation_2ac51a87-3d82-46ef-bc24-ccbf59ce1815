@import "~common/styles/merch_picking/mixins";

.link-search {
  background-color: #fff;

  .search-bar__main {
    border-radius: 8px;
    padding-left: 36px;
    background-repeat: no-repeat;
    background-size: 20px 20px;
    background-position: 8px center;
    background-image: url(~static/images/merch_picking/icon_search2.svg);
  }
}

.link-search-v2 {
  background-color: #fff;

  .link-search__content {
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .merch-list {
      height: auto;
    }
  }

  .search-bar {
    padding: 4px 16px;

    &--popup99 {
      margin-top: 12px;
    }
  }

  .search-bar__main {
    height: 36px;
    border-radius: 8px;
    padding-left: 36px;
    background-repeat: no-repeat;
    background-size: 20px 20px;
    background-position: 8px center;
    background-image: url(~static/images/merch_picking/icon_search.svg);
  }

  .search-bar__input {
    padding: 0;
    line-height: 36px;
  }

  .combo-search__help {
    font-size: 13px;
    color: rgba(254, 44, 85, 1);
    margin: -8px 0 0;
  }
}

.link-new-empty {
  flex: 1;
  height: 65vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 40px;

  .link-new-empty__title {
    font-size: 17px;
    font-weight: 500;
    line-height: 24px;
    margin: 8px 0 6px;
  }

  .link-new-empty__content {
    font-size: 14px;
    line-height: 20px;
    color: rgba(22, 24, 35, .6);
    text-align: center;
  }

  .link-new-empty__button {
    display: flex;
    width: 165px;
    height: 44px;
    margin-top: 40px;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    border: .5px solid #ff3b52;
    color: #fe2c55;
    text-align: center;
    font-family: "PingFang SC";
    font-size: 15px;
    font-style: normal;
    font-weight: 500;
    line-height: 22px;
  }
}
