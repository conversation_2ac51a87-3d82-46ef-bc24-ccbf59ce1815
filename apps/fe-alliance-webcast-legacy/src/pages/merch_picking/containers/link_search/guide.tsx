import React from 'react';
import { LinkSearchGuideOptions } from '../../types';
import './guide.scss';
export const PORTAL_LINK_SEARCH_GUIDE: LinkSearchGuideOptions = {
  linkType: '粘贴我的小店或精选联盟商品链接',
  linkSource: '',
};

export default React.memo(() => {
  const steps = [
    {
      content: `1. 仅支持添加我的小店或精选联盟商品链接；`,
    },
    {
      content: `2. 需要严格遵守【发布违禁商品/信息】实施细则：https://school.jinritemai.com/doudian/web/article/101805`,
    },
  ];
  return (
    <div className="link-search-guide">
      <h3 className="link-search-guide__header">粘贴直播商品链接流程</h3>
      {steps.map((step, index) => (
        <div className="link-search-guide__step" key={index}>
          <p className="link-search-guide__content">{step.content}</p>
        </div>
      ))}
    </div>
  );
});
