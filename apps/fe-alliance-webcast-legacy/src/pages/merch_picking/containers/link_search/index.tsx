import { LinkSearchGuideOptions, <PERSON>rch, Merch<PERSON><PERSON><PERSON><PERSON><PERSON>, PortalTab } from '../../types';

import React, { useEffect, use<PERSON><PERSON>back, Mouse<PERSON><PERSON><PERSON>and<PERSON>, useMemo, useState, useContext } from 'react';
import { ListFetchResponse } from 'components/list/state';
import showToast from 'components/toast/ecom_toast';
import { useLinkSearchState } from '../../modules/search';
import { TAOBAO_UNAUTHED_STATUS_CODE, PORTAL_LINK_SEARCH_GUIDE } from '../../constants';

import Modal from 'components/modal';
import EmptyView from 'components/empty';
import SearchBar from '../../components/search_bar';
import MerchList from '../../components/merch_list';
import SearchGuide from './guide';
import SearchGuideV2 from './guide-v2';
import TaobaoAuthEntry from '../../components/taobao_auth/embed';
import EmptyImage from '@src/static/images/merch_picking/search_empty_image.png';
import './index.scss';
import { PairLimitCheckCallback } from '../../components/pair_limit/types';
import { EVENTNAME, sendLog as sendLinkSearchLog, LinkSearchScene } from '../../services/send-linksearch-log';
import { myCoreLinkClient } from '@src/lib/util/mera/core-link';
import { usePersistCallback } from '@byted/hooks';
import { openSchema } from '@src/common/bridge';
import { genSchema } from '@src/lib/util/merch_picking';
import { isInAweme } from '@byted-flash/utils';
import { BetterPriceList } from '@alliance-mobile/platform-merchant-common/src/components/better-price-list';
import { getPmtCard4Kol } from '../../services/api';
import { AppContext } from '../../modules/context';
import { KolProductInfo } from '../../../../../../../libs/global/ferry/src/api/idl/thrift_idls/ecom.kol_summary.api/kol_product';
import { sendEvent } from '@alliance-mobile/event';

const EMPTY_URL = 'https://lf3-static.bytednsdoc.com/obj/eden-cn/lm-ljuhpssh/ljhwZthlaukjlkulzlp/empty.png';

interface Props {
  isOpen: boolean;
  guideOptions?: LinkSearchGuideOptions;
  onMerchPick: MerchEventHandler;
  onMerchClick: MerchEventHandler;
  onPairLimitCheck?: PairLimitCheckCallback;
  onHelpBtnClick: MouseEventHandler;
  searchHandler(link: string): Promise<ListFetchResponse<Merch>>;
  onNavBack(): void;
  onMachineAuditPopupShow?(): void;
  headerHint?: JSX.Element;
  isMerchV2?: boolean;
  userId: string;
}

export default React.memo((props: Props) => {
  const {
    isOpen,
    onMerchPick,
    onMerchClick,
    onHelpBtnClick,
    onPairLimitCheck,
    searchHandler,
    onNavBack,
    guideOptions = PORTAL_LINK_SEARCH_GUIDE,
    headerHint,
    isMerchV2,
    userId,
  } = props;

  const [state, commitSearchText, setSearchFailReason, setAsTaobaoMerchLink] = useLinkSearchState();
  const { appendedMerchList } = useContext(AppContext);
  const [sameListData, setSameListData] = useState<{ list?: KolProductInfo[]; originProductId?: string }>({});

  const [taolianEmptyTip, setTaolianEmptyTip] = useState('');

  const onSearchConfirm = useCallback(
    (searchText: string) => {
      myCoreLinkClient.sendCoreLinkEvent('live_link_add_search_click');

      if (!searchText) {
        showToast(`请输入我的小店或精选联盟商品链接`);
        return;
      }
      commitSearchText(searchText);
      sendLinkSearchLog(
        EVENTNAME.LINKSEARCHCLICK,
        { link_content: searchText, source: LinkSearchScene.mobileLive },
        userId
      );
    },
    [userId]
  );

  const searchMerchList = useCallback(() => {
    setSameListData({});
    const { searchText } = state.searchSession;
    return searchHandler(searchText)
      .then((res: any) => {
        // 这里缓存同款列表和原高价商品
        setSameListData({
          list: res.summary_promotions || [],
          originProductId: res.items?.[0].product_id,
        });
        return res;
      })
      .catch(error => {
        if (error.status_code === -5) {
          setTaolianEmptyTip(error.status_msg);
          return;
        }
        setSearchFailReason(error.status_msg);
        if (error.status_code === TAOBAO_UNAUTHED_STATUS_CODE) {
          setAsTaobaoMerchLink();
        }
        return Promise.reject(error);
      });
  }, []);

  const goToMerchPicking = usePersistCallback(() => {
    openSchema({
      schema: genSchema({
        url: 'https://lf-ecom-gr-sourcecdn.bytegecko.com/obj/byte-gurd-source-gr/merchPicking/gecko/h5Resource/alliance_merch_picking_h5/cn/html/merch-picking/index.html?_pia_=1',
        isWebCast: true,
        hide_nav_bar: 1,
        should_full_screen: 1,
        trans_status_bar: 1,
        status_bar_color: 'black',
        hide_loading: 1,
        web_bg_color: 'ffffff',
        loader_name: 'forest',
        disable_thread_spread: 1,
        disable_host_jsb_method: 1,
      }),
    });
  });

  const ListEmptyView = useCallback(() => {
    if (taolianEmptyTip) {
      return (
        <div className="link-new-empty">
          <img src={EMPTY_URL} width={80} height={80} />
          <div className="link-new-empty__title">暂无相关商品</div>
          <div className="link-new-empty__content">{taolianEmptyTip}</div>
          {isInAweme && (
            <div className="link-new-empty__button" onClick={goToMerchPicking}>
              去选品
            </div>
          )}
        </div>
      );
    }
    return (
      <EmptyView imgUrl={EmptyImage} hint={state.searchFailReason || '无法识别该商品'}>
        <p onClick={onHelpBtnClick} className="combo-search__help">
          仍有问题？查看帮助
        </p>
      </EmptyView>
    );
  }, [goToMerchPicking, onHelpBtnClick, state.searchFailReason, taolianEmptyTip]);

  useEffect(() => {
    // 弹窗关闭时重置搜索
    if (!isOpen) {
      commitSearchText('');
    }
  }, [isOpen]);

  // 新旧版本区分
  const SearchGuideComp = isMerchV2 ? SearchGuideV2 : SearchGuide;
  return (
    <Modal
      isOpen={isOpen}
      overlayClassName={`link-search ${isMerchV2 ? 'link-search-v2' : ''}`}
      className="link-search__content">
      <SearchBar
        searchIcon="icon-link"
        value={state.searchSession.searchText}
        onConfirm={onSearchConfirm}
        placeholder={`${guideOptions.linkType}`}
        onNavBack={onNavBack}
        submitText="搜索"
      />
      {headerHint}
      {state.searchSession.searchText || !isOpen ? null : <SearchGuideComp />}
      {!state.searchSession.searchText ? null : (
        <>
          <MerchList
            tab={{
              key: 'link_search',
            }}
            {...props}
            isHiddenLoadMore={true}
            session={state.searchSession}
            fetcher={searchMerchList}
            onMerchPick={(...params) => {
              myCoreLinkClient.sendCoreLinkEvent('live_add_search_promotion_add_btn_click');
              onMerchPick(...params);
            }}
            onMerchClick={onMerchClick}
            EmptyView={ListEmptyView}
            machineAudit={{}}
            isFromLinkSearch={true}
            noviceShopParams={{ page_name: '搜索商品结果页' }}
            onPairLimitCheck={onPairLimitCheck}
            isMerchV2={isMerchV2}
            isLinkSearch
          />
          <BetterPriceList
            sendEvent={sendEvent}
            data={sameListData.list || []}
            originProductId={sameListData.originProductId || ''}
            productFrom="live-control"
            logParams={{
              btm: 'a55989.b88328.c50564.d0',
              page_name: '直播中控链接添加',
            }}
            customRenderButton={v => {
              const isAdded = Boolean(appendedMerchList.find(vv => vv === v.promotion_id));

              return (
                <div
                  className={isAdded ? 'better-price-list-button' : 'better-price-list-button-primary'}
                  onClick={e => {
                    e.stopPropagation();

                    getPmtCard4Kol({
                      promotionId: v.promotion_id || '',
                      productId: v.product_id || '',
                    })
                      .then(info => {
                        if (!info?.data?.promotions?.length) {
                          showToast('操作失败');
                          return;
                        }

                        const promotion = info?.data?.promotions?.[0] || {};
                        onMerchPick(promotion, 0, false, false, true);

                        if (!isAdded) {
                          sendLinkSearchLog(
                            'pick_product',
                            {
                              commodity_id: promotion.promotion_id,
                              product_id: promotion.product_id,
                              edit_type: 'add_product',
                              is_cart: '选品车',
                              button_for: '添加',
                              btm: 'a55989.b88328.c50564.d0',
                              page_name: '直播中控链接添加',
                              source_product_id: sameListData.originProductId,
                              enter_from: 'live-control',
                            },
                            userId
                          );
                        }
                      })
                      .catch(() => {
                        showToast('操作失败');
                      });
                  }}>
                  {isAdded ? '取消' : '添加'}
                </div>
              );
            }}
          />
        </>
      )}
    </Modal>
  );
});
