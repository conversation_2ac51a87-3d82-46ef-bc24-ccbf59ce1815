import React from 'react';
import { AppContext as IAppContext, AppState } from '../../types';
import { AppController } from '../../modules/controller';
import { AppContext } from '../../modules/context';
import SearchHelpView from '../../components/search_help';
import TaobaoAuthConfirm from '../../components/taobao_auth/confirm';
import AllianceAuthConfirm from 'components/promoting/alliance_auth_confirm';
import YMaTouAuthConfirm from '../../components/ymatou_auth/confirm';
import LoadingMask from 'components/loading/mask';
import MachineAuditConfirm from '../../components/machine_audit_confirm';
import { EMachineAuditCode } from '../../constants';
import OpenOverseasModel from '../../components/overseas_modal/open_guide';
import FirstOverseasModal from '../../components/overseas_modal/overseas_first';
import GoodsLimitModal from '../../components/limit_modal/goods_limit_modal';

import './index.scss';

interface Props {
    appState: AppState;
    context: IAppContext;
    controller: AppController;
    children: React.ReactNode;
}

export default React.memo((props: Props) => {
    const { appState, controller, children, context } = props;
    const machineAuditTitle = React.useMemo(() => {
        const { reject_type } = appState.machineAudit;
        return [
            EMachineAuditCode.fresh,
            EMachineAuditCode.cosmetic,
            EMachineAuditCode.food
        ].includes(reject_type as number)
            ? '温馨提示'
            : '添加失败';
    }, [appState.machineAudit]);
    return (
        <AppContext.Provider value={context}>
            {children}
            <TaobaoAuthConfirm
                isOpen={appState.isTaobaoAuthConfirmActive}
                onConfirm={controller.onTaobaoAuthConfirm}
                onCancel={controller.onTaobaoAuthCancel}
            />
            <YMaTouAuthConfirm
                isOpen={appState.isYMaTouAuthConfirmActive}
                onConfirm={controller.onYMaTouAuthConfirm}
                onCancel={controller.onYMaTouAuthCancel}
            />
            <LoadingMask
                isOpen={appState.isLoadingMaskActive}
                content="商品审核中"
                className="loading_mask_content"
            />
            <LoadingMask
                isOpen={appState.isCancelBindToLiveModalShow}
                className="loading_mask_content"
            />
            <SearchHelpView
                isOpen={appState.isSearchHelpViewActive}
                onNavBack={controller.onSearchHelpBack}
            />
            <MachineAuditConfirm
                title={machineAuditTitle}
                isShouldHideApplyVerifyButton={true}
                onConfirm={controller.onPickFailedConfirm}
                onCancel={controller.onPickFailedConfirm}
                isOpen={appState.isPickFailedModalShow || false}
                machineAudit={appState.machineAudit}
                curJumpMachineAuditRule={controller.handleClickMachineAuditRule}
            />
            <OpenOverseasModel
                isOpen={appState.isShowOpenOverseasAccount}
                onConfirm={controller.onConfirmOverseasAccount}
                onCancel={controller.onCancelOverseasAccount}
            />
            <FirstOverseasModal
                isOpen={appState.isShowFirstAddOverseas}
                onConfirm={controller.onConfirmFirstShowOverseas}
            />
            {/* 添加商品的弹窗 */}
             <GoodsLimitModal
                isOpen={appState.isShowGoodsLimitModal}
                onConfirm={controller.onConfirmGoodsLimit}
                {...appState.limitModal}
            />
        </AppContext.Provider>
    );
});
