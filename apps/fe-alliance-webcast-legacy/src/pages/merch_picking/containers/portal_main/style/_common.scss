@mixin img-size($width, $height) {
  width: $width;
  height: $height;
}

@mixin action-style(
  $width,
  $height,
  $border-radius,
  $font-size,
  $font-weight,
  $color
) {
  display: block;
  width: $width;
  height: $height;
  border-radius: $border-radius;
  font-size: $font-size;
  font-weight: $font-weight;
  line-height: $height;
  color: $color;
}

.empty-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 114px 24px 24px;
  text-align: center;
}

.bottom-hint-style {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 9px 16px;
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 13px;
  line-height: 18px;
  color: #161823;
  background: #f8f8f8;

  &-content {
    display: inline-flex;
    align-items: center;

    &::before {
      content: "";
      margin-right: 8px;
      display: inline-block;
      width: 16px;
      height: 16px;
      background-image: url(~static/images/merch_picking/icon_bottom_hint.svg);
      background-size: cover;
    }
  }

  &-arrow {
    width: 12px;
    height: 12px;
    background-image: url(~static/images/merch_picking/icon_bottom_hint_arrow.svg);
    background-size: cover;
  }
}

// 新版ui 实验组 isMerchV2

.portal-main-tab-list__wrapper {
  padding: 0 16px;

  .tab--active::after {
    width: 24px;
    height: 3px;
  }
}
