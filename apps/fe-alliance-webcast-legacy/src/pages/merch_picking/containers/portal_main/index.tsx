import React, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>act<PERSON><PERSON>, use<PERSON>allback, useEffect, useMemo, useRef, useState } from 'react';
import webcast from '@bridge/webcast';
import via from '@ies/webcast-via';
import { Merch, MerchEventHandler, PortalTab } from '../../types';
import { genLiveTabDetailFetcher, fetchStarAtlasTabDetail } from '../../services/api';
import { TAB_KEYS, PORTAL_TAB_LIST_SKELETON_KEY } from '../../constants';
import MerchList from '../../components/merch_list';
import TabList from '../../components/tab_list';
import shopWindowEmptyView, { shopWindowProductListEmptyView } from './emptyview';
import { COMMON_STAT_PARAMS, isSameTab, sendLog } from '../../services/utils';
import MerchSectionList from '../../components/merch_section_list';
import { PairLimitCheckCallback } from '../../components/pair_limit/types';
import { Button, Checkbox } from '@ecom/auxo-mobile';
import Tooltip from 'rc-tooltip';
import debounce from 'lodash/debounce';
import BlockMerchantGuide from '../../components/block-merchant-guide';
import RecommendPromotionsGuide from '../../components/recommend-promotions-guide';
import { openSchema } from '@src/common/bridge';
import { formatQueryString } from '@src/lib/util';
import { PublishContinuePushStream } from '@src/lib/util/event';
import { myCoreLinkClient } from '@src/lib/util/mera/core-link';
import { genSchema } from '@src/lib/util/merch_picking';
import RecommendProducts from '../../components/recommend-products';
import { isAweme, isInAweme } from '@src/lib/env_detect';
import { AppController } from '../../modules/controller';

const SHOWN_SELECTALL_TOOLTIP = 'SHOWN_SELECTALL_TOOLTIP';
const recommend_guide_key = 'ADD_RECOMMEND_PROMOTION_GUIDE_KEY';

interface Props {
  isAB?: boolean;
  tabList: PortalTab[];
  activeTab: PortalTab;
  isAuthorityOutdated: boolean;
  showAddRecommend?: boolean;
  onMerchPick: MerchEventHandler;
  onMerchClick: MerchEventHandler;
  onPairLimitCheck?: PairLimitCheckCallback;
  onTabClick(tab: PortalTab): void;
  /** 剩余最大可添品数 */
  maxSelectCount: number;
  /** 当前已选列表 */
  selectedList: Merch[];
  /** 取消选中商品所属的tab */
  cancelSelectAllTab: string;
  /** 删除某个tab下的全选态 */
  onClearSelectList(tab: PortalTab): void;
  sendSelectAllLog(): void;
  onBatchSelectChange(merch: Merch, status: boolean, activeTab: PortalTab): void;
  onBatchAdd(selectAll: boolean): void;
  onMerchTitleClick(merch: Merch): void;
  onMerchBetterPriceClick(merch: Merch): void;
  isMerchV2?: boolean;
  autoSelectAll?: boolean;
  changeAutoSelectAll(flag: boolean): void;
  changeShowAddRecommend?(showAddRecommend: boolean): void;
  mixController?: AppController['mixController'];
}

export default React.memo((props: Props) => {
  const {
    isAB,
    tabList,
    activeTab,
    onTabClick,
    onMerchPick,
    onMerchClick,
    onPairLimitCheck,
    maxSelectCount,
    selectedList,
    cancelSelectAllTab = '',
    showAddRecommend,
    sendSelectAllLog,
    onBatchSelectChange,
    onClearSelectList,
    onBatchAdd,
    onMerchTitleClick,
    onMerchBetterPriceClick,
    isMerchV2,
    autoSelectAll,
    changeAutoSelectAll,
    changeShowAddRecommend,
    mixController,
  } = props;

  const [session, setSession] = useState({});
  const refresh = () => {
    // 通过更新session的方式来重置列表刷新
    setSession({});
  };
  useEffect(() => {
    // 监听直播商品管理页传来的刷新列表的事件
    webcast.app.subscribeEvent({
      eventName: 'ecom.anchor.refresh_promotions',
      callback: refresh,
    });
    return () => {
      webcast.app?.unSubscribeEvent({
        eventName: 'ecom.anchor.refresh_promotions',
        listener: refresh,
      });
    };
  }, []);
  const uidRef = useRef('');

  // 不能选中更多商品
  const cantSelectMore = useMemo(() => selectedList.length >= maxSelectCount, [selectedList, maxSelectCount]);

  const handleSelectChange = (merch, status) => {
    onBatchSelectChange?.(merch, status, activeTab);
  };

  const multiSelectConfig = useMemo(
    () => ({
      isMulti: true,
      maxSelectCount,
      selectedList,
      cantSelectMore,
      cancelSelectAllTab,
      onBatchAdd,
      sendSelectAllLog,
      onSelectChange: handleSelectChange,
      isMerchV2,
      mixController,
    }),
    [
      maxSelectCount,
      selectedList,
      cantSelectMore,
      cancelSelectAllTab,
      onBatchAdd,
      sendSelectAllLog,
      handleSelectChange,
      isMerchV2,
      mixController,
    ]
  );

  const isLiving = useMemo(() => {
    const { room_id } = formatQueryString();
    const roomId = Array.isArray(room_id) ? room_id[0] : room_id;
    return Boolean(roomId && Number(roomId));
  }, []);

  const roomId = useMemo(() => {
    const { room_id } = formatQueryString();
    return Array.isArray(room_id) ? room_id[0] : room_id;
  }, []);

  const sendRecommendLog = useCallback(
    async (event: string, params?: Record<string, unknown>) => {
      try {
        if (!uidRef.current) {
          const userInfo = await via.app.getUserInfo();
          uidRef.current = userInfo.user_id || '';
        }

        sendLog(event, {
          ...COMMON_STAT_PARAMS,
          user_id: uidRef.current,
          author_id: uidRef.current,
          live_type: roomId && Number(roomId) ? 'on' : 'off',
          platform: 'mobile',
          enter_from: roomId && Number(roomId) ? '直播中添品' : '直播添品',
          ...params,
        });
      } catch (error) {}
    },
    [roomId]
  );

  const handleJumpMerchCenter = useCallback(async () => {
    let uid = '';
    try {
      const userInfo = await via.app.getUserInfo();
      uid = userInfo.user_id || '';
      const { room_id } = formatQueryString();
      sendLog('product_suggestion_model_click', {
        ...COMMON_STAT_PARAMS,
        user_id: uid,
        author_id: uid,
        live_type: roomId && Number(roomId) ? 'on' : 'off',
        platform: 'mobile',
        type: '添品页',
        enter_from: roomId && Number(roomId) ? '直播中添品' : '直播添品',
        button_for: '前往选品广场',
      });
    } catch (error) {}
    // 直播中 跳转全屏页需要主动发送 continue_push_stream 事件，否则直播画面会黑屏
    // PublishContinuePushStream里做了新版本和直播中状态的判断
    PublishContinuePushStream();
    openSchema({
      schema: genSchema({
        url: 'https://lf-ecom-gr-sourcecdn.bytegecko.com/obj/byte-gurd-source-gr/merchPicking/gecko/h5Resource/alliance_merch_picking_h5/cn/html/merch-picking/index.html?_pia_=1',
        isWebCast: true,
        hide_nav_bar: 1,
        should_full_screen: 1,
        trans_status_bar: 1,
        status_bar_color: 'black',
        hide_loading: 1,
        web_bg_color: 'ffffff',
        loader_name: 'forest',
        disable_thread_spread: 1,
        disable_host_jsb_method: 1,
      }),
    });
  }, [roomId]);

  return (
    <>
      <TabList
        tabList={tabList}
        activeTab={activeTab}
        onTabClick={onTabClick}
        skeletonKey={PORTAL_TAB_LIST_SKELETON_KEY}
        wrapperClassName={isMerchV2 ? 'portal-main-tab-list__wrapper' : ''}
      />
      <div className="tabviews">
        {tabList.map(tab => {
          if (!tab.isRendered && !isSameTab(tab, activeTab)) {
            return null;
          }
          // 新手店铺标签埋点参数
          const noviceShopParams = {
            tab_name: tab.title,
            page_name: '添加商品首页',
          };
          const tabContentMap = {
            [TAB_KEYS.SHOPWINDOW]: (
              <MerchList
                key={tab.title}
                tab={tab}
                // 需要更新刷新的列表需加上session
                session={session}
                fetcher={genLiveTabDetailFetcher(tab.type)}
                onMerchPick={onMerchPick}
                onMerchClick={onMerchClick}
                cosFeeMode={false}
                noviceShopParams={noviceShopParams}
                EmptyView={shopWindowEmptyView}
                onPairLimitCheck={onPairLimitCheck}
                onClearSelectList={() => onClearSelectList(tab)}
                onMerchBetterPriceClick={onMerchBetterPriceClick}
                {...multiSelectConfig}
              />
            ),
            [TAB_KEYS.STORE]: (
              <MerchList
                key={tab.title}
                tab={tab}
                session={session}
                fetcher={genLiveTabDetailFetcher(tab.type)}
                onMerchPick={onMerchPick}
                onMerchClick={onMerchClick}
                cosFeeMode={false}
                noviceShopParams={noviceShopParams}
                EmptyView={shopWindowEmptyView}
                onPairLimitCheck={onPairLimitCheck}
                onClearSelectList={() => onClearSelectList(tab)}
                onMerchBetterPriceClick={onMerchBetterPriceClick}
                {...multiSelectConfig}
              />
            ),
            [TAB_KEYS.EXCLUSIVE]: (
              <MerchList
                key={tab.title}
                tab={tab}
                session={session}
                fetcher={genLiveTabDetailFetcher(tab.type)}
                onMerchPick={onMerchPick}
                onMerchClick={onMerchClick}
                cosFeeMode={false}
                noviceShopParams={noviceShopParams}
                EmptyView={shopWindowEmptyView}
                onPairLimitCheck={onPairLimitCheck}
                onClearSelectList={() => onClearSelectList(tab)}
                onMerchBetterPriceClick={onMerchBetterPriceClick}
                showBlockMerchant={true}
                refresh={refresh}
                {...multiSelectConfig}
              />
            ),
            [TAB_KEYS.ORIENTATION]: (
              <MerchList
                key={tab.title}
                tab={tab}
                session={session}
                fetcher={genLiveTabDetailFetcher(tab.type)}
                onMerchPick={onMerchPick}
                onMerchClick={onMerchClick}
                cosFeeMode={false}
                noviceShopParams={noviceShopParams}
                EmptyView={shopWindowEmptyView}
                onPairLimitCheck={onPairLimitCheck}
                onClearSelectList={() => onClearSelectList(tab)}
                onMerchBetterPriceClick={onMerchBetterPriceClick}
                showBlockMerchant={true}
                refresh={refresh}
                {...multiSelectConfig}
              />
            ),
            [TAB_KEYS.STAR_ATLAS]: (
              <MerchSectionList
                key={tab.title}
                tab={tab}
                session={session}
                onMerchPick={onMerchPick}
                onMerchClick={onMerchClick}
                cosFeeMode={true}
                noviceShopParams={noviceShopParams}
                EmptyView={shopWindowEmptyView}
                fetcher={fetchStarAtlasTabDetail}
                onClearSelectList={() => onClearSelectList(tab)}
                onMerchBetterPriceClick={onMerchBetterPriceClick}
                {...multiSelectConfig}
              />
            ),
            [TAB_KEYS.MAGPIE_BRIDGE]: (
              <MerchList
                key={tab.title}
                tab={tab}
                session={session}
                fetcher={genLiveTabDetailFetcher(tab.type)}
                onMerchPick={onMerchPick}
                onMerchClick={onMerchClick}
                cosFeeMode={false}
                noviceShopParams={noviceShopParams}
                EmptyView={shopWindowEmptyView}
                onPairLimitCheck={onPairLimitCheck}
                onClearSelectList={() => onClearSelectList(tab)}
                {...multiSelectConfig}
              />
            ),
            [TAB_KEYS.PROTOCOL]: (
              <MerchList
                key={tab.title}
                tab={tab}
                session={session}
                fetcher={genLiveTabDetailFetcher(tab.type)}
                onMerchPick={onMerchPick}
                onMerchClick={onMerchClick}
                cosFeeMode={false}
                noviceShopParams={noviceShopParams}
                EmptyView={shopWindowEmptyView}
                onPairLimitCheck={onPairLimitCheck}
                onClearSelectList={() => onClearSelectList(tab)}
                onMerchBetterPriceClick={onMerchBetterPriceClick}
                {...multiSelectConfig}
              />
            ),
            [TAB_KEYS.HISTORY_ADD]: (
              <MerchList
                key={tab.title}
                tab={tab}
                session={session}
                fetcher={genLiveTabDetailFetcher(tab.type)}
                onMerchPick={onMerchPick}
                onMerchClick={onMerchClick}
                cosFeeMode={false}
                noviceShopParams={noviceShopParams}
                EmptyView={shopWindowEmptyView}
                onPairLimitCheck={onPairLimitCheck}
                onClearSelectList={() => onClearSelectList(tab)}
                onMerchBetterPriceClick={onMerchBetterPriceClick}
                {...multiSelectConfig}
              />
            ),
            [TAB_KEYS.PICKING_CART]: (
              <MerchList
                key={tab.title}
                tab={tab}
                session={session}
                fetcher={genLiveTabDetailFetcher(tab.type)}
                onMerchPick={onMerchPick}
                onMerchClick={onMerchClick}
                cosFeeMode={false}
                noviceShopParams={noviceShopParams}
                EmptyView={shopWindowProductListEmptyView}
                onPairLimitCheck={onPairLimitCheck}
                onClearSelectList={() => onClearSelectList(tab)}
                onMerchBetterPriceClick={onMerchBetterPriceClick}
                changeShowAddRecommend={changeShowAddRecommend}
                showHeader
                isMerchV2={isMerchV2}
                {...multiSelectConfig}
                afterExposured={() => {
                  myCoreLinkClient.sendCoreLinkEvent('live_add_promotion_merchcart_tab_show');
                }}
              />
            ),
            [TAB_KEYS.RECOMMENDED_PRODUCT]: (
              <MerchList
                key={tab.title}
                tab={tab}
                session={session}
                fetcher={genLiveTabDetailFetcher(tab.type)}
                onMerchPick={onMerchPick}
                onMerchClick={onMerchClick}
                onMerchTitleClick={onMerchTitleClick}
                onMerchBetterPriceClick={onMerchBetterPriceClick}
                cosFeeMode={false}
                noviceShopParams={noviceShopParams}
                EmptyView={shopWindowEmptyView}
                onPairLimitCheck={onPairLimitCheck}
                onClearSelectList={() => onClearSelectList(tab)}
                showHeaderFilter
                needImpression
                bottomHintNode={
                  isAweme && (
                    <div className="bottom-hint-style" onClick={handleJumpMerchCenter}>
                      <div className="bottom-hint-style-content">以上为平台推荐精选商品，前往选品广场挑选更多</div>
                      <span className="bottom-hint-style-arrow" />
                    </div>
                  )
                }
                autoSelectAll={autoSelectAll}
                changeAutoSelectAll={changeAutoSelectAll}
                showRecommend
                sendRecommendLog={sendRecommendLog}
                afterExposured={() => {
                  sendRecommendLog('product_suggestion_model_show', {
                    type: '添品页推荐tab',
                  });
                }}
                isMerchV2={isMerchV2}
                {...multiSelectConfig}
              />
            ),
          };
          return (
            <div
              key={tab.title}
              className="tabview fcol f-fw-nw f-ai-s"
              style={{ display: isSameTab(tab, activeTab) ? 'flex' : 'none' }}>
              {tabContentMap[tab.key]}
            </div>
          );
        })}
      </div>
      {isInAweme && isAB ? (
        <>
          {activeTab?.key === TAB_KEYS.PICKING_CART && showAddRecommend ? (
            <RecommendProducts sendRecommendLog={sendRecommendLog} />
          ) : null}
        </>
      ) : (
        <RecommendPromotionsGuide
          storageKey={recommend_guide_key}
          visible={
            tabList.some(tab => tab.key === TAB_KEYS.RECOMMENDED_PRODUCT) &&
            !isLiving &&
            activeTab.key !== TAB_KEYS.RECOMMENDED_PRODUCT
          }
          jumpAction={() => {
            const recommendTab = tabList.find(item => item.key === TAB_KEYS.RECOMMENDED_PRODUCT);
            recommendTab && onTabClick(recommendTab);
            recommendTab && changeAutoSelectAll?.(true);
          }}
          sendRecommendLog={sendRecommendLog}
        />
      )}

      {activeTab.key === TAB_KEYS.EXCLUSIVE && <BlockMerchantGuide storageKey="liveMerchPicking" />}
    </>
  );
});
