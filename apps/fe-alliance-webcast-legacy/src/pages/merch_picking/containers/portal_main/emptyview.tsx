import React, { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from 'react';
import { appSmallName as appName } from '@src/lib/env_detect';
import { ListState } from 'components/list/state';
import EmptyImage from '@src/static/images/merch_picking/empty_image_v2.png';
import SearchEmptyImage from '@src/static/images/merch_picking/search_empty_image.png';
import { openSchema } from '@src/common/bridge';
import { genSchema } from '@src/lib/util/merch_picking';
import cx from 'classnames';

import './style/emptyview';

const EMPTY_IMG_MAP = {
  toutiao: 'https://sf1-cdn-tos.douyinstatic.com/obj/eden-cn/bvkeh7flrjhq/merch_picking/toutiao_no_goods.png',
  douyin: 'https://sf1-cdn-tos.douyinstatic.com/obj/eden-cn/bvkeh7flrjhq/merch_picking/douyin_no_goods.png',
  xigua: 'https://sf1-cdn-tos.douyinstatic.com/obj/eden-cn/bvkeh7flrjhq/merch_picking/xigua_network_error.png',
  huoshan: 'https://sf1-cdn-tos.douyinstatic.com/obj/eden-cn/bvkeh7flrjhq/merch_picking/huoshan_no_goods.png',
};
const FETCH_FAILED_IMG_MAP = {
  toutiao: 'https://sf1-cdn-tos.douyinstatic.com/obj/eden-cn/bvkeh7flrjhq/merch_picking/toutiao_no_goods.png',
  douyin: 'https://sf1-cdn-tos.douyinstatic.com/obj/eden-cn/bvkeh7flrjhq/merch_picking/douyin_network_error.png',
  xigua: 'https://sf1-cdn-tos.douyinstatic.com/obj/eden-cn/bvkeh7flrjhq/merch_picking/xigua_network_error.png',
  huoshan: 'https://sf1-cdn-tos.douyinstatic.com/obj/eden-cn/bvkeh7flrjhq/merch_picking/huoshan_network_error.png',
};

// TODO sass化
export const EmptyView = React.memo((props: Props) => {
  const EMPTY_IMG = EMPTY_IMG_MAP[appName];
  const { searchText } = props;
  const {
    imgUrl = EMPTY_IMG,
    title,
    hint,
    action,
    actionClassName,
    hintClassName,
    imgClassName,
    onActionClick,
  } = props;
  return (
    <div className="empty-view">
      {/* eslint-disable-next-line @ecom/ecom-rule/avoid-img */}
      {imgUrl ? <img src={imgUrl} className={cx('empty-view__img', imgClassName)} /> : null}
      {title ? <p className="empty-view__title">{title}</p> : null}
      {hint ? (
        <p className={cx(`${searchText ? 'empty-view__hint' : 'empty-view__hint--normal-list'}`, hintClassName)}>
          {hint}
        </p>
      ) : null}
      {!action || !onActionClick ? null : (
        <span className={cx('empty-view__action', actionClassName)} onClick={onActionClick}>
          {action}
        </span>
      )}
    </div>
  );
});

export default function shopWindowEmptyView<Item>(state: ListState<Item>, onRetry: MouseEventHandler) {
  const EMPTY_IMG = EMPTY_IMG_MAP[appName];
  const NETWORK_ERROR_IMG = FETCH_FAILED_IMG_MAP[appName];
  const searchText = state?.searchText;
  const DEFAULT_EMPTY_IMAGE = searchText ? SearchEmptyImage : EmptyImage;
  return (
    <EmptyView
      hint={state.isFetchFailed ? '' : '暂无商品'}
      imgUrl={state.isFetchFailed ? NETWORK_ERROR_IMG : EMPTY_IMG || DEFAULT_EMPTY_IMAGE}
      action={state.isFetchFailed ? '重新加载' : ''}
      onActionClick={onRetry}
      searchText={state?.searchText}
    />
  );
}

const goToMerchPicking = () => {
  openSchema({
    schema: genSchema({
      url: 'https://lf-ecom-gr-sourcecdn.bytegecko.com/obj/byte-gurd-source-gr/merchPicking/gecko/h5Resource/alliance_merch_picking_h5/cn/html/merch-picking/index.html?_pia_=1&mode=rich&enter_from=toolbox',
      should_full_screen: 1,
      status_font_dark: 1,
      status_bar_color: 'fff',
      isWebCast: true,
      hide_nav_bar: 1,
      trans_status_bar: 1,
      hide_loading: 1,
      web_bg_color: 'ffffff',
      loader_name: 'forest',
      disable_thread_spread: 1,
      disable_host_jsb_method: 1,
    }),
  });
};

export function shopWindowProductListEmptyView<Item>(state: ListState<Item>, onRetry: MouseEventHandler) {
  const EMPTY_IMG = 'https://lf3-static.bytednsdoc.com/obj/eden-cn/nupqphobvog/empty.svg';
  const NETWORK_ERROR_IMG = FETCH_FAILED_IMG_MAP[appName];
  const searchText = state?.searchText;
  const DEFAULT_EMPTY_IMAGE = searchText ? SearchEmptyImage : EmptyImage;
  const emptyConfig = (() => {
    return {
      title: '暂无商品',
      hint: '添品后开播自动同步小黄车',
      imgUrl: EMPTY_IMG || DEFAULT_EMPTY_IMAGE,
    };
  })();
  return (
    <EmptyView
      title={emptyConfig.title}
      hint={state.isFetchFailed ? '' : emptyConfig.hint}
      imgUrl={state.isFetchFailed ? NETWORK_ERROR_IMG : emptyConfig.imgUrl}
      action={state.isFetchFailed ? '重新加载' : undefined}
      onActionClick={state.isFetchFailed ? onRetry : undefined}
      searchText={state?.searchText}
      actionClassName="empty-view__button"
      hintClassName="empty-view__hint-v2"
      imgClassName="empty-view__img-v2"
    />
  );
}

interface Props {
  imgUrl: string;
  hint?: string | Element;
  title?: string | Element;
  onActionClick?: MouseEventHandler;
  action?: string;
  searchText?: string; // 搜索词
  actionClassName?: string;
  hintClassName?: string;
  imgClassName?: string;
}
