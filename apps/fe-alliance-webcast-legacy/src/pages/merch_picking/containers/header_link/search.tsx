/*
 * @Author: <EMAIL>
 * @Date: 2023-06-21 10:17:40
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-06-25 02:06:38
 * @Description: 搜索
 */
import React, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react';
import cx from 'classnames';
import './index.scss';

interface Props {
  onLinkToggleClick: MouseEventHandler;
}

export default React.memo((props: Props) => {
  const { onLinkToggleClick } = props;
  return (
    <div className="search-toggle-v2" onClick={onLinkToggleClick}>
      <div className={cx('search-toggle-v2-icon', 'search-toggle-v2-search-icon')} />
      <span className="search-toggle-v2--link">搜索</span>
    </div>
  );
});
