import React, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react';
import cx from 'classnames';
import './index.scss';

interface Props {
  onLinkToggleClick: MouseEventHandler;
}

export default React.memo((props: Props) => {
  const { onLinkToggleClick } = props;
  return (
    <div className="search-toggle-v2" onClick={onLinkToggleClick}>
      <div className={cx('search-toggle-v2-icon', 'search-toggle-v2-merch-icon')} />
      <span className="search-toggle-v2--link">选品</span>
    </div>
  );
});
