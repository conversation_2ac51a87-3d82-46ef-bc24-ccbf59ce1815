@import "~common/styles/merch_picking/mixins";

.rc-tooltip-placement-bottomRight {

  .rc-tooltip-content {

    .rc-tooltip-arrow {
      right: 12px;
      top: 3px;
      width: 12px;
      height: 6px;
      border-width: 0;
      border-color: transparent;
      background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAMCAYAAAB4MH11AAAAAXNSR0IArs4c6QAAAI5JREFUOBFjYKAxYCTBfC6gWlWo+ttA+hsxeom1wAtoWDYQgywBAZDhU4F4G4iDDzDjk4TKJQDpLCBmhfJBFIhtDcQgB14AYpyAkAUJQJ3xOHUzMBgA5fBags8CQobD7MVrCS4LiDWcoCXYLCDVcLyWoFtAruE4LUG2gFLDsVoCs4BahmNYArKA2oajWAIAoRsULoHWufoAAAAASUVORK5CYII=");
      background-size: contain;
    }
  }
}

.search-toggle {

  &--link {
    flex: 1;
    display: inline-block;
    // padding-top: 24px;
    // border-radius: 6px;
    color: #262626;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    // @include background($color: transparent, $size: 18px auto, $position: top 4px center);
  }

  &-v2 {
    display: flex;
    flex-direction: column;
    align-items: center;

    &--link {
      font-size: 11px;
      color: rgba(22, 24, 35, .80);
      line-height: 16px;

      &:not(:nth-last-child(1)) {
        margin-right: 12px;
      }
    }

    &-icon {
      width: 16px;
      height: 16px;
      margin-top: 2px;
      display: inline-block;
      background-size: cover;
      background-image: url(~static/images/merch_picking/link-icon.svg);
    }

    &-search-icon {
      background-image: url(~static/images/merch_picking/search-icon.svg);
    }

    &-merch-icon {
      background-image: url(~static/images/merch_picking/merch-icon.svg);
    }
  }
  // &--link {
  //     margin-left: 8px;
  //     background-image: url("data:image/svg+xml,%3Csvg width='14' height='14' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform='scale(-1 1) rotate(45 -1.41 -5.921)' fill='none' fill-rule='evenodd'%3E%3Cpath d='M0 4.958v-1.24C0 1.666 1.633 0 3.647 0c2.015 0 3.648 1.665 3.648 3.719v1.24m0 3.966v1.24c0 2.054-1.633 3.719-3.648 3.719-2.014 0-3.647-1.665-3.647-3.72V8.926' stroke='%23000' stroke-width='1.44' stroke-linecap='round'/%3E%3Crect fill='%23000' x='2.897' y='4.12' width='1.501' height='5.644' rx='.72'/%3E%3C/g%3E%3C/svg%3E");
  // }
  &--skeleton#{&}--link {
    color: transparent;
    background-image: none;
  }

  &__overlay {
    width: 136px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(255, 255, 255, 1);
    line-height: 20px;

    .rc-tooltip-inner {
      padding: 12px;
      font-size: 14px;
      background: rgba(0, 0, 0, .75);
      border-radius: 6px;
    }
  }
}
