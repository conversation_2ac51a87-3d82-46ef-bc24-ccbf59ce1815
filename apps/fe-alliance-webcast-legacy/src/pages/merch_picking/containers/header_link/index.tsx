import React, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react';
import cx from 'classnames';
import './index.scss';
import { myCoreLinkClient } from '@src/lib/util/mera/core-link';

interface Props {
  onLinkToggleClick: MouseEventHandler;
  isMerchV2?: boolean;
}

export default React.memo((props: Props) => {
  const { onLinkToggleClick, isMerchV2 } = props;
  return (
    <>
      {isMerchV2 ? (
        <div
          className="search-toggle-v2"
          onClick={e => {
            onLinkToggleClick(e);
            myCoreLinkClient.sendCoreLinkEvent('live_link_add_entry_btn_click');
          }}>
          <div className="search-toggle-v2-icon" />
          <span className={cx('search-toggle--link', { 'search-toggle-v2--link': isMerchV2 })}>链接</span>
        </div>
      ) : (
        <span
          onClick={e => {
            onLinkToggleClick(e);
            myCoreLinkClient.sendCoreLinkEvent('live_link_add_entry_btn_click');
          }}
          className={cx('search-toggle--link', { 'search-toggle-v2--link': isMerchV2 })}>
          {isMerchV2 ? '链接' : '粘贴链接'}
        </span>
      )}
    </>
  );
});
