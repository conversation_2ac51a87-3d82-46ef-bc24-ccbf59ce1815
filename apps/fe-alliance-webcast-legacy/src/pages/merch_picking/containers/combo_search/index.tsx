import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rch<PERSON>earchPara<PERSON>, PortalTab, MerchStarAtlasSection } from '../../types';

import React, { useEffect, use<PERSON><PERSON>back, <PERSON><PERSON>ventHand<PERSON> } from 'react';
import showToast from 'components/toast/ecom_toast';
import { PORTAL_TAB_LIST_SKELETON_KEY, TAB_KEYS } from '../../constants';
import { useComboSearchState } from '../../modules/search';
import { ListState, ListFetchResponse } from 'components/list/state';
import Modal from 'components/modal';
import SearchBar from '../../components/search_bar';
import MerchList from '../../components/merch_list';
import TabList from '../../components/tab_list';
import SearchHistory from '../../components/search_history';
import SearchHistoryV2 from '../../components/search_history/index-v2';
import { SearchParams, fetchStarAtlasTabDetail } from '../../services/api';
import { isSameTab } from '../../services/utils';
import EmptyView from '../portal_main/emptyview';
import MerchSectionList from '../../components/merch_section_list';

import './index.scss';
import { PairLimitCheckCallback } from '../../components/pair_limit/types';
import { myCoreLinkClient } from '@src/lib/util/mera/core-link';

interface Props {
  isOpen: boolean;
  tabList: PortalTab[];
  activeTab: PortalTab;
  placeholder?: string;
  onMerchPick: MerchEventHandler;
  onMerchClick: MerchEventHandler;
  onPairLimitCheck?: PairLimitCheckCallback;
  onNavBack: MouseEventHandler;
  searchConfirm?: () => void;
  onTabClick: (tab: PortalTab) => void;
  searchHandler(params: MerchSearchParams & SearchParams): Promise<ListFetchResponse<Merch>>;
  headerHint?: JSX.Element;
  isMerchV2?: boolean;
}

export default React.memo((props: Props) => {
  const {
    isOpen,
    onMerchPick,
    onMerchClick,
    searchHandler,
    onPairLimitCheck,
    placeholder = '搜索商品或店铺',
    onNavBack,
    tabList,
    activeTab,
    onTabClick,
    searchConfirm,
    headerHint,
    isMerchV2,
  } = props;

  const [state, commitSearchText, commitSortingOption, setSearchFailReason, clearSearchHistory] = useComboSearchState();

  const onSearchConfirm = useCallback((searchText: string) => {
    myCoreLinkClient.sendCoreLinkEvent('live_add_search_entry_btn_click');
    if (!searchText) {
      showToast('请输入商品名称');
      return;
    }
    if (searchConfirm && typeof searchConfirm === 'function') {
      searchConfirm();
    }
    commitSearchText(searchText);
  }, []);

  const searchMerchListFactory = useCallback(
    (tab_id: number) => (list: ListState<Merch>) => {
      const { searchSession } = state;
      const params: MerchSearchParams = {
        count: list.pageSize,
        offset: list.nextOffset,
        title: searchSession.searchText,
        commonFilter: list.commonFilter,
      };
      const { sortingOption } = searchSession;
      if (sortingOption && sortingOption.key) {
        params.order = sortingOption.key;
        params.sort = sortingOption.reverse ? 0 : 1;
      }
      return searchHandler({ ...params, page: list.page, tab_id }).catch(error => {
        setSearchFailReason(error.status_msg);
        return Promise.reject(error);
      });
    },
    []
  );

  const searchStarAtlasListFactory = useCallback(
    () => (list: ListState<MerchStarAtlasSection>) => {
      const { searchSession } = state;
      const params: MerchSearchParams = {
        count: list.pageSize,
        offset: list.nextOffset,
      };
      return fetchStarAtlasTabDetail({
        ...list,
        ...params,
        page: list.page,
        keyWord: searchSession.searchText,
        type: 'search',
      }).catch(error => {
        setSearchFailReason(error.status_msg);
        return Promise.reject(error);
      });
    },
    []
  );

  useEffect(() => {
    // 弹窗关闭时重置搜索
    if (!isOpen) {
      commitSearchText('');
    }
  }, [isOpen]);
  const SearchHistoryComp = isMerchV2 ? SearchHistoryV2 : SearchHistory;
  return (
    <Modal
      isOpen={isOpen}
      overlayClassName={`combo-search ${isMerchV2 ? 'combo-search-v2' : ''}`}
      className="combo-search__content">
      <SearchBar
        submitText="搜索"
        value={state.searchSession.searchText}
        placeholder={placeholder}
        onConfirm={onSearchConfirm}
        onNavBack={onNavBack}
      />
      {headerHint}
      {state.searchSession.searchText || !isOpen ? null : (
        <SearchHistoryComp items={state.searchHistory} onClear={clearSearchHistory} onItemClick={onSearchConfirm} />
      )}
      {!state.searchSession.searchText || tabList.length < 2 ? null : (
        <TabList
          tabList={tabList}
          activeTab={activeTab}
          skeletonKey={PORTAL_TAB_LIST_SKELETON_KEY}
          onTabClick={onTabClick}
          wrapperClassName={isMerchV2 ? 'combo_search-tab-list__wrapper' : ''}
        />
      )}
      {!state.searchSession.searchText ? null : (
        <div className="tabviews">
          {tabList.map(tab => {
            // 新手店铺标签埋点参数
            const noviceShopParams = {
              tab_name: tab.title,
              page_name: '搜索商品结果页',
            };
            const tabContentMap = {
              [TAB_KEYS.SHOPWINDOW]: (
                <MerchList
                  session={state.searchSession}
                  fetcher={searchMerchListFactory(tab.type)}
                  onMerchPick={onMerchPick}
                  onMerchClick={onMerchClick}
                  cosFeeMode={false}
                  lazyImg={false}
                  EmptyView={EmptyView}
                  interactive={true}
                  noviceShopParams={noviceShopParams}
                  onPairLimitCheck={onPairLimitCheck}
                  isMerchV2={isMerchV2}
                />
              ),
              [TAB_KEYS.STORE]: (
                <MerchList
                  fetcher={searchMerchListFactory(tab.type)}
                  session={state.searchSession}
                  onMerchPick={onMerchPick}
                  onMerchClick={onMerchClick}
                  cosFeeMode={false}
                  EmptyView={EmptyView}
                  interactive={true}
                  lazyImg={false}
                  noviceShopParams={noviceShopParams}
                  onPairLimitCheck={onPairLimitCheck}
                  isMerchV2={isMerchV2}
                />
              ),
              [TAB_KEYS.STAR_ATLAS]: (
                <MerchSectionList
                  key={tab.title}
                  session={state.searchSession}
                  onMerchPick={onMerchPick}
                  onMerchClick={onMerchClick}
                  cosFeeMode={false}
                  EmptyView={EmptyView}
                  interactive={true}
                  lazyImg={false}
                  fetcher={searchStarAtlasListFactory()}
                  noviceShopParams={noviceShopParams}
                  isMerchV2={isMerchV2}
                />
              ),
              [TAB_KEYS.MAGPIE_BRIDGE]: (
                <MerchList
                  session={state.searchSession}
                  fetcher={searchMerchListFactory(tab.type)}
                  onMerchPick={onMerchPick}
                  onMerchClick={onMerchClick}
                  cosFeeMode={false}
                  lazyImg={false}
                  EmptyView={EmptyView}
                  interactive={true}
                  noviceShopParams={noviceShopParams}
                  onPairLimitCheck={onPairLimitCheck}
                  isMerchV2={isMerchV2}
                />
              ),
              [TAB_KEYS.PROTOCOL]: (
                <MerchList
                  session={state.searchSession}
                  fetcher={searchMerchListFactory(tab.type)}
                  onMerchPick={onMerchPick}
                  onMerchClick={onMerchClick}
                  cosFeeMode={false}
                  lazyImg={false}
                  EmptyView={EmptyView}
                  interactive={true}
                  noviceShopParams={noviceShopParams}
                  onPairLimitCheck={onPairLimitCheck}
                  isMerchV2={isMerchV2}
                />
              ),
              [TAB_KEYS.PICKING_CART]: (
                <MerchList
                  session={state.searchSession}
                  fetcher={searchMerchListFactory(tab.type)}
                  onMerchPick={onMerchPick}
                  onMerchClick={onMerchClick}
                  cosFeeMode={false}
                  lazyImg={false}
                  EmptyView={EmptyView}
                  interactive={true}
                  noviceShopParams={noviceShopParams}
                  onPairLimitCheck={onPairLimitCheck}
                  isMerchV2={isMerchV2}
                  showHeader
                />
              ),
            };
            return (
              <div
                key={tab.title}
                className="tabview"
                style={{
                  display: isSameTab(tab, activeTab) ? 'block' : 'none',
                }}>
                {tabContentMap[tab.key]}
              </div>
            );
          })}
        </div>
      )}
    </Modal>
  );
});
