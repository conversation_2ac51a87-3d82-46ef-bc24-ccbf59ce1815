@import "~common/styles/merch_picking/mixins";

.combo-search {
  @include mask($top: 0);
  background-color: #fff;

  &__content {
    height: 100%;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    align-items: stretch;
  }

  &__help {
    font-size: 12px;
    color: #406599;
    margin: -8px 0 0;
  }
}


.combo-search-v2 {
  background-color: #fff;

  .combo-search__content {

    .search-bar {
      padding: 4px 16px;

      &--popup99 {
        margin-top: 12px;
      }
    }

    .search-bar__main {
      height: 36px;
      border-radius: 8px;
      padding-left: 36px;
      background-repeat: no-repeat;
      background-size: 20px 20px;
      background-position: 8px center;
      background-image: url(~static/images/merch_picking/icon_search.svg);
    }
    .search-bar__input {
      padding: 0;
      line-height: 36px;
    }
  }

}

.combo_search-tab-list__wrapper {
  padding: 0 16px;

  .tab--active::after {
    width: 30%;
  }
}
