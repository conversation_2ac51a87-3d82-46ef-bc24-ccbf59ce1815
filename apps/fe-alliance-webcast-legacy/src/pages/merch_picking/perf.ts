import { RemewReport } from '@alliance-mobile/perf';
export * from '@alliance-mobile/perf';
const isOnline = !(process.env.COMMIT_HASH || process.env.DEV_MODE);
export const remewReport = new RemewReport({
  samplingRate: isOnline ? 0.1 : 1,
});

// 直播填品所有页面状态
export enum ALL_PAGE_STATUS {
  MERCHED_LIVE_PAGE = 'MERCHED_LIVE_PAGE', // 直播添品页
}

// 每种页面状态首屏可交互需要ready的接口
export const PAGE_FETCHES = {
  [ALL_PAGE_STATUS.MERCHED_LIVE_PAGE]: ['fetchLiveTabList', 'fetchLiveTabDetail'],
};
