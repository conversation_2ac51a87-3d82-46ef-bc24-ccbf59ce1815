@import "~common/styles/merch_picking/mixins";

.tab-list {
    display: flex;
    margin: 0;
    padding: 0;
    list-style: none;
    flex-shrink: 0;
    overflow-x: scroll;
    scrollbar-width: none; /* firefox */
    -ms-overflow-style: none; /* IE 10+ */
    &::-webkit-scrollbar {
        display: none; /* Chrome Safari */
    }

    &__wrapper {
        &::after {
            background-color: #E0E0E0;
        }
    }
    
    &--skeleton {
        margin: 3px 12px;
        @include skeleton($height: 34px);
    }
}

.tab {
    position: relative;
    display: inline-block;
    padding: 11px 12px;
    text-align: center;
    flex: 1 0 auto;
    font-size: 15px;
    color: #737373;
    &--active {
        color: #1A1A1A;
        font-weight: 700;
        &::after {
            content: "";
            position: absolute;
            z-index: 10;
            bottom: 0.5px;
            transform: translateX(-50%);
            margin-left: 50%;
            left: 0%;
            width: 24px;
            height: 3px;
            border-radius: 1.5px;
            background-color: #ff264a;
        }
    }
}
