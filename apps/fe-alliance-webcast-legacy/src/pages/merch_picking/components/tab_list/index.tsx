import cx from 'classnames';
import { isLSItemEqual, isSameTab } from '../../services/utils';
import React, { useRef, useEffect, useMemo } from 'react';
import './style';

interface Tab {
  key: string;
  title: string;
}

interface Props<T extends Tab> {
  tabList: T[];
  activeTab: T | null;
  skeletonKey: string;
  wrapperClassName?: string; // tab wrapper自定义类名
  onTabClick(tab: T): void;
}

export default React.memo(function TabList<T extends Tab>(props: Props<T>) {
  const { tabList, activeTab, skeletonKey, onTabClick, wrapperClassName = '' } = props;
  const skeletonFlag = useRef<boolean>(useMemo(() => isLSItemEqual(skeletonKey, true), []));
  const tabScrollRef = useRef<HTMLUListElement>(null);
  useEffect(() => {
    const hasSkeleton = tabList?.length > 1;
    if (hasSkeleton !== skeletonFlag.current) {
      skeletonFlag.current = hasSkeleton;
      localStorage.setItem(skeletonKey, `${hasSkeleton}`);
    }
  }, [tabList]);

  useEffect(() => {
    if (activeTab && activeTab?.key && tabList.map(item => item.key).includes(activeTab.key)) {
      const tabDom = document.getElementById(`${activeTab.key}_scrollItem`);
      const windowOffsetWidth = window.innerWidth; // 屏幕宽度;
      if (tabDom) {
        tabScrollRef.current?.scrollTo({
          left: tabDom.offsetLeft - (windowOffsetWidth - tabDom.offsetWidth) / 2,
          behavior: 'smooth',
        });
      }
    }
  }, [activeTab, tabList]);

  if (tabList?.length <= 1) {
    return null;
  }

  return (
    <div className={`tab-list__wrapper hairline--b ${wrapperClassName}`}>
      <ul className="tab-list" ref={tabScrollRef}>
        {tabList?.map(tab => (
          <li
            key={tab?.title}
            id={`${tab?.key}_scrollItem`}
            className={cx('tab', 'common', isSameTab(tab, activeTab || {}) ? 'tab--active' : null)}
            onClick={() => onTabClick(tab)}>
            {tab?.title}
          </li>
        ))}
      </ul>
    </div>
  );
});
