import React, { useEffect, useMemo, useRef, useState } from 'react';
import cx from 'classnames';
import Merch, { Props } from '../merch';
import { MachineAuditResponse, Merch as MerchType, MerchEventHandler, PortalTab } from '../../types';
import { genMachineAuditRule } from '../../services/utils';
import { NoviceShopTagParams } from 'components/novice-shop-tag/types';
import ModalConfirm from './components/modal';
import showToast from 'components/toast/ecom_toast';
import './index.scss';
import Checkbox from 'components/checkbox';
import { setStorage, getStorage } from 'src/lib/util/index';
import { PairLimitCheckCallback } from '../pair_limit/types';
import {
  CommissionTicketTipType,
  computeNeedInvoiceShow,
  InvoiceModalShow,
} from '@src/pages/merch_picking/components/invoice-modal/invoiceNoticeModal';
import { isNil } from 'lodash';
import { isAweme } from '@src/lib/env_detect';
import { AppController } from '../../modules/controller';
interface MerchProps extends Props {
  tab?: PortalTab;
  onPick: MerchEventHandler;
  isAppended: boolean;
  isSelected: boolean;
  machineAudit?: MachineAuditResponse;
  noviceShopParams?: NoviceShopTagParams;
  isFromLinkSearch?: boolean;
  onPairLimitCheck?: PairLimitCheckCallback;
  isMulti?: boolean;
  showBlockMerchant?: boolean;
  refresh?: () => void;
  isMerchV2?: boolean;
  showRecommend?: boolean;
  mixController?: AppController['mixController'];
  isLinkSearch?: boolean;
  logExtra?: Record<string, unknown>;
}

enum ActionType {
  Cancel = 'cancel', // 取消
  Append = 'append', // 添加
  BlockAppend = 'blockAppend', // 挂车限制添加
}

export default React.memo((props: MerchProps) => {
  const {
    data,
    index,
    onPick,
    isAppended,
    onPairLimitCheck,
    isMulti = false,
    onSelectChange,
    showRecommend,
    mixController,
    tab,
    logExtra,
  } = props;

  const [isopen, setIsOpen] = useState(false); // 控制风险提醒弹框的显示隐藏
  const [isLazy, setIsLazy] = useState(false); // 控制用户是否勾选反疲劳选项
  const [isBlockBypassed, setIsBlockBybassed] = useState(false); // 商品是否豁免
  const isAggr = data.tag_codes?.includes('retail_cross_aggr_product');
  // 如果已豁免，则不展示“无联盟商达订单”
  const merchData = useMemo(() => {
    if (isBlockBypassed) {
      return { ...data, order_product: undefined } as MerchType;
    } else {
      return data;
    }
  }, [isBlockBypassed]);

  // 是否超卖的字段
  const oversoldRemind = data.oversold_remind || false;

  // 点击【添加/取消】按钮
  const handlePick = async (event: React.MouseEvent<HTMLSpanElement, MouseEvent>, actionType: ActionType) => {
    if (actionType === ActionType.BlockAppend) {
      return;
    }

    const { type, effecte_time, freeze_ratio } = data?.commission_ticket_tip_info || {};
    // 仅在添加的情况下，弹弹窗
    if (
      !isNil(type) &&
      type !== CommissionTicketTipType.Normal &&
      computeNeedInvoiceShow() &&
      actionType === ActionType.Append
    ) {
      // 当存在发票提醒，且提醒内容不是正常添品,且不在7天提醒时间段内时时
      InvoiceModalShow({
        type,
        effecte_time,
        freeze_ratio,
        confirmModal: () => {
          onPick(data, index);
        },
      });
      // 中断后续流程
      return;
    }

    if (actionType === ActionType.Append) {
      const now = new Date().getTime();
      const lastTime = await getStorage('PAYMENT_MINUS_INVENTORY');
      let isOverdue = true;
      if (lastTime && typeof lastTime === 'number') {
        isOverdue = now - Number(lastTime) > 86400000;
      }
      if (oversoldRemind && isOverdue) {
        setIsOpen(true);
        return;
      }
    }

    event.stopPropagation();

    onPick(data, index);
  };

  const renderAggrButton = useMemo(() => {
    if (!isAweme) {
      return null;
    }

    if (isAggr) {
      // 打开聚合品的关联商品列表
      return (
        <div
          className="aggr-open-button"
          onClick={() => {
            mixController?.onClickAggregateButton?.(data, {
              tabKey: tab?.key,
            });
          }}>
          关联商品
        </div>
      );
    }
  }, [data, isAggr, mixController, tab?.key]);

  // 设置操作按钮
  const actionTextObject = useMemo(() => {
    if (isAppended) {
      return {
        key: ActionType.Cancel,
        value: '取消',
      };
    } else {
      let key = ActionType.Append;
      const value = <>添加</>;

      if (data.live_add_enum || data.block_add_reason) {
        key = ActionType.BlockAppend;
        return {
          key,
          value,
        };
      }

      // 在添加商品前，判断是否需要挂车限制
      if (data.order_product) {
        key = isBlockBypassed ? ActionType.Append : ActionType.BlockAppend;
      }
      return { key, value };
    }
  }, [isAppended, isBlockBypassed]);

  // 商品卡片点击
  const onMerchClick = () => {
    if (actionTextObject.key === ActionType.BlockAppend) {
      if (data.block_add_reason) {
        showToast(data.block_add_reason);
        return;
      }

      if (data.live_add_enum) {
        // showToast(data.live_add_enum);
        return;
      }

      onPairLimitCheck?.(data, index, bypass => {
        setIsBlockBybassed(bypass);
        bypass && showToast('申请豁免成功');
      });
    } else {
      props.onClick?.(data, index);
    }
  };

  /**
   * 超卖风险提醒弹框的确认点击事件
   */
  const handleAdd = () => {
    if (isLazy) {
      const now = new Date().getTime();
      setStorage('PAYMENT_MINUS_INVENTORY', now);
    }
    setIsOpen(false);

    if (data.block_add_reason) {
      showToast(data.block_add_reason);
      return;
    }

    if (data.live_add_enum) {
      // showToast(data.live_add_enum);
      return;
    }
    // event.stopPropagation();
    onPick(data, index);
  };

  /**
   * 风险提醒弹框的反疲劳选项
   */
  const lazyJsx = (
    <div className="pay__reduce--lazy">
      <Checkbox
        label="24小时内不再提醒"
        isChecked={isLazy}
        onChange={value => {
          setIsLazy(value);
        }}
      />
    </div>
  );
  return (
    <>
      <Merch
        {...props}
        data={merchData}
        jumpMachineAuditRule={genMachineAuditRule}
        isShouldHideApplyVerifyButton={true}
        onClick={onMerchClick}
        onSelectChange={onSelectChange}
        showRecommend={showRecommend}
        showDataPart={!isAggr}
        logExtra={logExtra}>
        <div className="merch__actions">
          {renderAggrButton}
          {isMulti ? null : (
            <div
              className={cx(
                'merch__action',
                actionTextObject.key === ActionType.BlockAppend && 'merch__action--disable',
                actionTextObject.key === ActionType.Cancel && 'merch__action--remove'
              )}
              onClick={e => {
                handlePick(e, actionTextObject.key);
              }}>
              {actionTextObject.value}
            </div>
          )}
        </div>
      </Merch>

      <ModalConfirm
        title="超卖风险提醒"
        content="此商品为付款减库存（用户支付完成后减库存），实际下单数可能超过当前库存，有超卖风险，请与商家沟通确认"
        confirmText="我知道了"
        onConfirm={handleAdd}
        isOpen={isopen}
        children={lazyJsx}
      />
    </>
  );
});
