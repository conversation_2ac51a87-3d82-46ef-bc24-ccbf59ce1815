import Modal from "components/modal/index";
import React, { ReactN<PERSON>, MouseEventHandler } from "react";
import cx from "classnames";
import "./index.scss";

interface Props extends ReactModal.Props {
  /**
   * 标题
   */
  title?: ReactNode;
  /**
   * 内容
   */
  content?: ReactNode;
  /**
   * 点击取消按钮的处理事件
   */
  onCancel?: MouseEventHandler;
  /**
   * 点击确认按钮的处理事件
   */
  onConfirm: MouseEventHandler;
  /**
   * 取消按钮的文本
   */
  cancelText?: ReactNode;
  /**
   * 确认按钮的文本
   */
  confirmText?: ReactNode;
  /**
   * class类名
   */
  styleName?: string;
  /**
   * children
   */
  children?: ReactNode;
  /**
   * 是否显示反疲劳选项
   */
  isLazy?: boolean;
  /**
   * 反疲劳选项的处理事件
   */
  onLazy?: MouseEventHandler;
  /**
   * 是否显示关闭按钮
   */
  closable?: boolean;
  /**
   * 点击关闭按钮的处理事件
   */
  onClose?: <PERSON><PERSON>ventHand<PERSON>;
}

export default React.memo((props: Props) => {
  const {
    title,
    content,
    onConfirm,
    onCancel,
    styleName,
    children,
    cancelText = "取消",
    confirmText = "确定",
    isLazy = false,
    closable,
    onClose,
    ...modalProps
  } = props;

  return (
    <Modal
      {...modalProps}
      className={cx("confirm-live-modal", styleName ? styleName : "")}
      overlayClassName="confirm-live-modal__overlay"
      trackProps={{
        title,
        cancelText: cancelText as string,
        confirmText: confirmText as string,
      }}
    >
      {closable ? (
        <span className="confirm-live__close" onClick={onClose}>
          <img
            src="data:image/svg+xml;base64,P…jYXA9InJvdW5kIi8+LDwvc3ZnPg=="
            alt=""
          />
        </span>
      ) : null}
      {title ? (
        <h3 className={"confirm-live__title newConfirm-live__title"}>
          {title}
        </h3>
      ) : null}
      {content ? (
        <p className="confirm-live__body newConfirm-live__body">{content}</p>
      ) : null}
      {children ? children : null}
      <div className="confirm-live__footer">
        {cancelText && onCancel ? (
          <span className="confirm-live__cancel" onClick={onCancel}>
            {cancelText}
          </span>
        ) : null}
        <span className="confirm-live__confirm" onClick={onConfirm}>
          {confirmText}
        </span>
      </div>
    </Modal>
  );
});
