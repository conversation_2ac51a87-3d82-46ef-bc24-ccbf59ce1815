import React, { useState, useEffect, useCallback } from 'react';
import { getTabPmt, PmtCard4Kol } from './util';
import { MERCH_PICKING_RECOMMEND_PATH } from '../../constants';
import { openVersatile, formatPriceNumber } from '../../services/utils';
import { author_live_settings_get } from '../../services/api';
import { genSchema, isLiving } from '@src/lib/util/merch_picking';
import { getQuery, globalProps } from '@src/lib/env_detect';
import { RouteQuery } from '../../types';
import './index.scss';

interface IProps {
  sendRecommendLog?: (event: string, params?: Record<string, unknown>) => Promise<void>;
}

const query = getQuery<RouteQuery>();

const RecommendPromotionsGuide = (props: IProps) => {
  const { sendRecommendLog } = props;
  const [recommendProducts, setRecommendProducts] = useState<PmtCard4Kol[]>([]);

  /** 去添加点击事件回调 */
  const handleClickAdd = useCallback(() => {
    sendRecommendLog?.('recommend_product_click', {
      page_name: '直播准备页',
      living_type: isLiving ? 'on' : 'off',
    });

    const height_percent = globalProps?.queryItems?.height_percent || 90;

    const halfParams = {
      type: 'popup',
      gravity: 'bottom',
      height_percent,
      android_soft_input_mode: -2, // android不加这个参数 键盘会将页面顶起使输入框不可见
      animation_type: 'present', // ios上需要这个参数触发当前页面的生命周期 （不加的话ios打开半屏直播容器无法监听onPageVisibleChange事件）
      radius: 20,
      show_dim: 1,
      mask_alpha: 0.5,
    };

    const params = {
      url: MERCH_PICKING_RECOMMEND_PATH,
      isWebCast: true,
      hide_nav_bar: 1,
      web_bg_color: '#ffffff',
      loader_name: 'forest',
      room_id: query?.room_id || 0,
      from_picked: 1,
      enter_from: query?.enter_from,
      init_tab: query?.init_tab,
      pick_third_source: 'select_product',
      ...halfParams,
    };

    const url = `${genSchema(
      params
    )}&hide_close_btn=1&swipe_mode=2&hide_loading=1&web_bg_color=%23ffffff&hide_nav_bar=1&loader_name=forest&enter_from=${
      query?.enter_from
    }&room_id=${
      query?.room_id || 0
    }&init_tab=&from_picked=1&type=popup&gravity=bottom&height_percent=${height_percent}&android_soft_input_mode=-2&animation_type=present&radius=20&show_dim=1&mask_alpha=0.5&add_safe_area_height=0&use_real_screen_height=${
      isLiving ? 1 : 0
    }`;

    openVersatile(url, globalProps?.containerID);
  }, []);

  useEffect(() => {
    author_live_settings_get({
      setting_options_str: '2',
      source: 2,
    }).then(res => {
      if (!res?.data?.is_merchant_author) {
        getTabPmt({
          tab_id: 21,
          cursor: 0,
          count: 5,
          source: 'live',
        }).then(data => {
          setRecommendProducts(data.data.promotions?.slice(0, 5) || []);
        });
      }
    });
  }, []);

  useEffect(() => {
    sendRecommendLog?.('recommend_product_show', {});
  }, []);

  return recommendProducts.length > 0 ? (
    <div className="recommend-products">
      <div className="recommend-products_title">
        一键添品
        <div className="recommend-products_title-btn" onClick={handleClickAdd}>
          去添加
        </div>
      </div>
      <div className="recommend-products_pmts">
        {recommendProducts.map(item => {
          const cosFeeObject = formatPriceNumber((item?.cos_fee || 0) / 100);
          return (
            <div className="recommend-products_pmts-pmt" key={item.promotion_id}>
              <img src={item.cover?.url_list?.[0] as string} className="recommend-products_pmts-cover" />
              <div className="recommend-products_pmts-earn">
                赚
                <span className="recommend-products_pmts-price">
                  ¥{cosFeeObject?.integer}
                  {cosFeeObject?.decimal}
                  {cosFeeObject?.unit}
                </span>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  ) : null;
};

export default RecommendPromotionsGuide;
