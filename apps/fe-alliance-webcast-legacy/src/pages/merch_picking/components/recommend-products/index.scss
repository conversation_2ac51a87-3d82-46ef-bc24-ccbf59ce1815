.recommend-products {
  position: fixed;
  width: 358px;
  min-height: 68px;
  left: calc(50% - 179px);
  bottom: 84px;
  border-radius: 8px;
  background: #fff;
  display: flex;
  flex-direction: column;
  padding: 12px;
  z-index: 9;

  .recommend-products_title {
    display: flex;
    justify-content: space-between;
    color: #161823;
    font-family: "PingFang SC";
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 12px;

    .recommend-products_title-btn {
      color: rgba(22, 24, 35, .6);
      font-size: 12px;
      font-weight: 400;
      line-height: 16px;
      display: flex;
      align-items: center;

      &::after {
        content: "";
        width: 12px;
        height: 12px;
        background-image: url("https://lf3-static.bytednsdoc.com/obj/eden-cn/nupqphobvog/arrow-gray.svg");
        background-size: cover;
        margin-left: 2px;
      }
    }
  }

  .recommend-products_pmts {
    display: flex;

    .recommend-products_pmts-pmt {
      display: flex;
      flex-direction: column;
      align-items: center;

      &:not(:last-child) {
        margin-right: 11px;
      }
    }

    .recommend-products_pmts-cover {
      width: 56px;
      height: 56px;
      border-radius: 8px;
    }

    .recommend-products_pmts-earn {
      color: rgba(22, 24, 35, .7);
      font-size: 10px;
      font-weight: 400;
      line-height: 16px;
      margin-top: 4px;
    }

    .recommend-products_pmts-price {
      color: rgba(22, 24, 35, .70);
      font-family: "PingFang SC";
      font-size: 10px;
      font-weight: 500;
      line-height: 16px;
      margin-left: 3px;
    }
  }
}
