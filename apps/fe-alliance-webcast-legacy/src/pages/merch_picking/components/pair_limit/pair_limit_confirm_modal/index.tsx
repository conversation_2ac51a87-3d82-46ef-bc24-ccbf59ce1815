import { AppController } from '@src/pages/merch_picking/modules/controller';
import React, { useEffect, useState } from 'react';
import cx from 'classnames';
import toast from '@src/components/toast';
import ModalConfirm from '../../merch_item/components/modal';
import { IPairLimitCommonParams } from '../types';
import { getPairLimitRelease } from '@src/pages/merch_picking/services/api';
import { IBypassInfo } from '@src/pages/merch_picking/types';
import { toChatPageWithShop, gotoEcomSchool } from '@src/pages/merch_picking/services/utils';
import './index.scss';

export interface IPairLimitConfirmModal {
  $app: AppController;
  info: IPairLimitCommonParams;
  onClose: () => void;
}

export const PairLimitConfirmModal = (props: IPairLimitConfirmModal) => {
  const { info, onClose, $app } = props;
  const [isOpen, setIsOpen] = useState(true);
  const [bypassInfo, setBypassInfo] = useState<IBypassInfo>({
    total_count: 0,
    released_count: 0,
    reason_list: [],
  });

  const { can_apply } = info.data.order_product || {};
  const isBypassDisabled = can_apply && bypassInfo.released_count >= bypassInfo.total_count;

  const handleClose = () => {
    setIsOpen(false);
    onClose();
  };

  const handleRedirect = () => {
    gotoEcomSchool();
  };

  const handleContact = () => {
    toChatPageWithShop(info.data.shop_id);
    handleClose();
  };

  const handleChoice = () => {
    if (can_apply) {
      if (isBypassDisabled) {
        toast('本月免下单机会已达上限');
      } else {
        $app.openPairLimitSheets(info, bypassInfo);
        handleClose();
      }
    } else {
      handleClose();
    }
  };

  const handleRequest = () => {
    getPairLimitRelease()
      .then(data => {
        setBypassInfo({
          total_count: data?.total_times || 10,
          released_count: data?.used_exempt_times || 0,
          reason_list: data?.reason_list || [],
        });
      })
      .catch(() => {});
  };

  const buttonRender = () => {
    if (can_apply) {
      return (
        <span
          className={cx({
            'is-disabled': isBypassDisabled,
          })}>{`申请豁免 (${bypassInfo.released_count}/${bypassInfo.total_count})`}</span>
      );
    } else {
      return '知道了';
    }
  };

  useEffect(() => {
    can_apply && handleRequest();
  }, []);

  return (
    <ModalConfirm
      title="无法添加商品"
      confirmText="联系商家"
      isOpen={isOpen}
      closable={can_apply}
      content={
        <div>
          LV5及以上达人直播添加品牌商品前，需要商家在精选联盟-达人广场发起合作订单。如遇特殊情况，可申请豁免
          <div className="text-more" onClick={handleRedirect}>
            了解详情 <b>&gt;</b>
          </div>
        </div>
      }
      cancelText={buttonRender()}
      onConfirm={handleContact}
      onCancel={handleChoice}
      onClose={handleClose}
    />
  );
};
