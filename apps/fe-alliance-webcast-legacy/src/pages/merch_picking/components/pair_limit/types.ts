import { Merch, ReasonList } from "../../types";

export type PairLimitCheckCallback = (
  data: Merch,
  index: number,
  callback: (bypass: boolean) => void
) => void;

export interface IPairLimitCommonParams {
  data: Merch;
  index: number;
  callback: (bypass: boolean) => void;
}

export interface IPairLimitSheetParams extends IPairLimitCommonParams {
  total_count: number;
  released_count: number;
  reason_list: ReasonList[];
}
