import React from "react";
import {
  getPairLimitTipsCache,
  gotoEcomSchool,
  setPairLimitTipsCache,
} from "../../../services/utils";
import "./index.scss";

export interface IPairLimitHint {
  info: {
    isLimitNotice: boolean;
    isLimitForced: boolean;
    timeText: string;
  } | null;
  onClose: () => void;
}

export const PairLimitHint = (props: IPairLimitHint) => {
  const isClosed = getPairLimitTipsCache();
  if (!props.info || isClosed) return <></>;

  const handleRedirect = () => {
    gotoEcomSchool();
  };

  const handleClose = () => {
    setPairLimitTipsCache();
    props.onClose();
  };

  return (
    <div className="pair-limit-tips is-closable">
      <div className="pair-limit-tips__notice"></div>
      <div className="pair-limit-tips__content" onClick={handleRedirect}>
        <span>
          为保障品牌商家与LV5及以上达人合作履约，商家需在精选联盟-达人广场向合作达人发起订单，达人接单后才可直播添加品牌商品
        </span>
        <span className="is-more">详情&gt;</span>
      </div>
      <div className="pair-limit-tips__close" onClick={handleClose}></div>
    </div>
  );
};
