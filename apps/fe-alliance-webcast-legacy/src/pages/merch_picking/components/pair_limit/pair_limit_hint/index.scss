.pair-limit-tips {
  position: relative;
  padding: 10px 12px 8px;
  color: #fe2c55;
  background: #fff3f5;
  font-weight: normal;
  font-size: 12px;
  line-height: 18px;
  display: flex;
  align-items: flex-start;

  &.is-closable {
    padding-right: 50px;
  }

  .pair-limit-tips__notice {
    width: 18px;
    height: 18px;
    margin-right: 4px;
    background-image: url("../../../../../static/images/merch_picking/icon_notice.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 16px 16px;
    flex-shrink: 0;
  }

  .pair-limit-tips__content {
    font-weight: normal;
    font-size: 12px;
    line-height: 18px;
    color: #fe2c55;
    .is-more {
      font-weight: 500;
      margin-left: 4px;
    }
  }

  .pair-limit-tips__close {
    position: absolute;
    width: 50px;
    top: 0;
    right: 0;
    bottom: 0;
    background-image: url("../../../../../static/images/merch_picking/icon_close_red.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 20px;
    flex-shrink: 0;
    z-index: 1;
  }
}
