import { Button, Sheets } from '@ecom/auxo-mobile';
import toast from '@src/components/toast';
import { applyPairLimitRelease } from '@src/pages/merch_picking/services/api';
import { useAndriodKeyboardResize } from '@src/pages/merch_picking/services/hooks';
import React, { useEffect, useRef, useState } from 'react';
import { IPairLimitSheetParams } from '../types';
import './index.scss';

export interface IPairLimitSheets {
  info: IPairLimitSheetParams;
  onClose: () => void;
  isMerchV2?: boolean;
}

const CustomReasonId = 0;

export const PairLimitSheets = (props: IPairLimitSheets) => {
  const { info, onClose, isMerchV2 } = props;
  const { reason_list = [], total_count = 10, released_count = 0, data } = info || {};

  const [isOpen, setIsOpen] = useState(true);
  const [isShowBottom, setIsShowBottom] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [checkedReasonIds, setCheckedReasonIds] = useState<number[]>([]);
  const [inputReason, setInputReason] = useState<string>('');
  const [inputError, setInputError] = useState<string>('');
  const [isFocus, setIsFocus] = useState(false);
  const wrapperRef = useRef<HTMLDivElement>(null);

  const handleClickReason = (id: number) => {
    if (checkedReasonIds.includes(id)) {
      setCheckedReasonIds(checkedReasonIds.filter(it => it !== id));
    } else {
      setCheckedReasonIds([...checkedReasonIds, id]);
    }
  };

  const checkInputError = (text: string) => {
    if (text.length === 0) {
      return '输入内容不得为空';
    } else if (text.length > 100) {
      return '超出字数限制';
    } else {
      return '';
    }
  };

  const handleInputChange = (text: string) => {
    setInputError(checkInputError(text));
    setInputReason(text);
  };

  const handleClose = (bypass: boolean) => {
    setIsOpen(false);
    onClose();
    info.callback(bypass);
  };

  const handleConfirm = async () => {
    if (isSubmitting) return;
    if (!checkedReasonIds.length) return toast('至少选择一种豁免原因', true);
    if (checkedReasonIds.includes(CustomReasonId)) {
      const inputError = checkInputError(inputReason);
      if (inputError) {
        setInputError(inputError);
        return;
      }
    }

    setIsSubmitting(true);
    try {
      const reasonText = checkedReasonIds.includes(CustomReasonId) ? inputReason : '';
      await applyPairLimitRelease({
        reason_list: checkedReasonIds.join(','),
        reason: reasonText,
        product_id: data.product_id,
      });
      setIsSubmitting(false);
    } catch (error) {
      setIsSubmitting(false);
      return toast(`申请失败，请稍后重试: ${error}`, true);
    }

    handleClose(true);
  };

  useAndriodKeyboardResize((isKeyboardOpen: boolean) => {
    setIsShowBottom(!isKeyboardOpen);
  });

  useEffect(() => {
    // 当键盘打开时，内容滚动到最底部
    if (!isShowBottom) {
      wrapperRef.current?.scrollTo({ top: 1000 });
    }
  }, [isShowBottom]);

  // // 滚动textArea到可视区域
  const textAreaFocusIn = () => {
    if (isMerchV2) {
      setIsFocus(true);
      const sheetsWrapper = document.querySelector('.pair-limit-sheets__wrapper');
      if (sheetsWrapper) {
        // 延时是为了防止占位元素未渲染完成
        setTimeout(() => {
          sheetsWrapper.scrollTop = sheetsWrapper.scrollHeight;
        }, 400);
      }
    } else {
      setIsShowBottom(false);
    }
  };

  const textAreaFocusOut = () => {
    if (isMerchV2) {
      setIsFocus(false);
    } else {
      setIsShowBottom(true);
    }
  };

  return (
    <div className={`pair-limit-sheets ${isMerchV2 ? 'pair-limit-sheets--v2' : 'pair-limit-sheets--prevVersion'}`}>
      <Sheets
        title="申请免下单挂车原因"
        visible={isOpen}
        onClose={() => handleClose(false)}
        onDismiss={() => handleClose(false)}>
        {/* 新版浮层形式的添品页，此sheets滚动的元素为pair-limit-sheets__wrapper */}
        <div
          ref={wrapperRef}
          className="pair-limit-sheets__wrapper"
          style={{ maxHeight: isShowBottom ? '90vh' : '300px' }}>
          <div className="pair-limit-sheets__tip">
            {`本月可用 ${total_count} 个免下单挂车的机会，当前剩余 ${total_count - released_count} 个机会`}
          </div>
          <div className="pair-limit-sheets__list">
            {reason_list?.map(it => {
              return (
                <div
                  className="pair-limit-sheets__item"
                  key={`${it?.reason_id}`}
                  onClick={() => handleClickReason(it?.reason_id as number)}>
                  <span className="pair-limit-sheets__text">{it?.text}</span>
                  {checkedReasonIds?.includes(it?.reason_id as number) && (
                    <span className="pair-limit-sheets__checked" />
                  )}
                </div>
              );
            })}
            {checkedReasonIds?.includes(CustomReasonId) && (
              <div>
                <textarea
                  id="search-test2"
                  className="pair-limit-sheets__input"
                  placeholder="请描述具体原因"
                  value={inputReason}
                  onFocus={() => {
                    textAreaFocusIn();
                  }}
                  onBlur={() => {
                    textAreaFocusOut();
                  }}
                  onChange={e => handleInputChange(e.target.value)}
                />
                <div className="pair-limit-sheets__hint">
                  <span className="pair-limit-sheets__error">{inputError}</span>
                  <span className="pair-limit-sheets__count">{`${inputReason.length}/100`}</span>
                </div>
              </div>
            )}
          </div>
          {isShowBottom && (
            <div className="pair-limit-sheets__button">
              <Button
                disabled={isSubmitting || !checkedReasonIds.length}
                size="large"
                text="确定"
                onClick={handleConfirm}
              />
            </div>
          )}
          {/* 占位元素 */}
          <div
            className={`pair-limit-sheets__bottom-placeholder ${
              isMerchV2 && isFocus ? 'pair-limit-sheets__bottom-placeholder--visible' : ''
            }`}
          />
        </div>
      </Sheets>
    </div>
  );
};
