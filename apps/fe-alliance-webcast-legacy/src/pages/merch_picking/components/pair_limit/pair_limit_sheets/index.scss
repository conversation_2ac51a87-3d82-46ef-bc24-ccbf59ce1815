.pair-limit-sheets {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 200;

  .douyin-sheets {
    padding-top: 16px;
  }



  &.pair-limit-sheets--prevVersion {

    .douyin-sheets {
      padding-top: 16px;
      height: auto !important;
    }
  }

  .douyin-sheets-close {
    width: 24px;
    height: 24px;
    line-height: 24px;
    margin-top: 4px;

    .am-icon-14 {
      width: 12px;
      height: 12px;
    }
  }



  &__wrapper {
    overflow: auto;

    &.textareaFocus {
      padding-bottom: 80px;
    }
  }

  &__tip {
    color: rgba(22, 24, 35, .5);
    font-weight: normal;
    font-size: 14px;
    line-height: 20px;
  }

  &__item {
    height: 52px;
    line-height: 52px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
  }

  &__text {
    font-weight: normal;
    font-size: 15px;
    line-height: 21px;
    color: #161823;
  }

  &__input {
    background: rgba(22, 24, 35, .06);
    width: 100%;
    height: 144px;
    color: #161823;
    padding: 12px;
    outline: 0;
    border: 0;

    &::placeholder {
      color: rgba(22, 24, 35, .34);
    }
  }

  &__hint {
    display: flex;
    justify-content: space-between;
  }

  &__count {
    font-weight: normal;
    font-size: 12px;
    line-height: 17px;
    color: rgba(22, 24, 35, .34);
  }

  &__error {
    font-weight: normal;
    font-size: 13px;
    line-height: 18px;
    color: #fe3824;
  }

  &__button {
    padding: 16px 0;

    .douyin-button-wrapper {
      width: 100%;
    }
  }

  &__checked {
    display: inline-block;
    width: 18px;
    height: 18px;
    background-image: url("../../../../../static/images/merch_picking/icon_check_red.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
  }
}


// 新版浮层独有的

.pair-limit-sheets--v2 {

  .pair-limit-sheets {

    &__wrapper {
      height: 373px;
    }
  }
}

.pair-limit-sheets__bottom-placeholder {
  width: 100%;
  height: 130px;
  display: none;

  &--visible {
    display: block;
  }
}
