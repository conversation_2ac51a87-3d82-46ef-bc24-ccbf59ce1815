import React, { <PERSON><PERSON><PERSON>Hand<PERSON> } from 'react';
import Confirm from 'components/modal/confirm';

interface Props {
    isOpen: boolean;
    onConfirm: MouseEventHandler;
    onCancel?: MouseEventHandler;
    title: string;
    content: string;
    confirm_text: string;
    cancel_text: string;
}

export default React.memo((props: Props) => {
    const { content, cancel_text } = props;
    return (
        <Confirm
            body={content}
            confirmText={cancel_text}
            {...props}
        />
    );
});
