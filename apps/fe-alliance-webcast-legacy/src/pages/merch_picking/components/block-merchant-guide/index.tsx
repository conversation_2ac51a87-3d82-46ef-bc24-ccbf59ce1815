import React, { useState } from 'react';
import cx from 'classnames';
import './index.scss';
interface IProps {
  storageKey: string;
}
const BlockMerchantGuide = (props: IProps) => {
  const { storageKey } = props;
  const [showGuide, setShowGuide] = useState(Boolean(!localStorage.getItem(storageKey)));
  return showGuide ? (
    <div
      className={cx('block-merchant-guide-wrapper')}
      onClick={() => {
        localStorage.setItem(storageKey, 'true');
        setShowGuide(false);
      }}>
      <div className={cx('block-merchant-guide-image')} />
      <div className={cx('block-merchant-guide-tip')}>长按商品卡片可【屏蔽商家】</div>
    </div>
  ) : null;
};

export default BlockMerchantGuide;
