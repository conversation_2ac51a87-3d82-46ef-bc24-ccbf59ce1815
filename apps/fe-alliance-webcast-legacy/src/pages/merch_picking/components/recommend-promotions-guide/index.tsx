import React, { useState, useEffect, useCallback, useReducer, useRef } from 'react';
import cx from 'classnames';
import './index.scss';
import closeIcon from 'static/images/merch_picking/icon_close.svg';
import { getTabPmt, PmtCard4Kol } from './util';
interface IProps {
  storageKey: string;
  jumpAction: () => void;
  visible: boolean;
  sendRecommendLog?: (event: string, params?: Record<string, unknown>) => Promise<void>;
}

const RecommendPromotionsGuide = (props: IProps) => {
  const { storageKey, jumpAction, visible, sendRecommendLog } = props;
  const [showGuide, setShowGuide] = useState(Boolean(!localStorage.getItem(storageKey)));
  const [recommendProducts, setRecommendProducts] = useState<PmtCard4Kol[]>([]);
  const sendExposureLogRef = useRef(false);
  useEffect(() => {
    getTabPmt({
      tab_id: 21,
      cursor: 0,
      count: 3,
      source: 'live',
    }).then(res => {
      setRecommendProducts(res.data.promotions?.slice(0, 3) || []);
    });
  }, []);

  const clickAction = useCallback(
    (closeOnly = false) => {
      setShowGuide(false);
      if (closeOnly) {
        localStorage.setItem(storageKey, 'true');
        sendRecommendLog?.('product_suggestion_model_click', {
          type: '添品页引导条',
          button_for: '关闭',
        });
        return;
      }
      sendRecommendLog?.('product_suggestion_model_click', {
        type: '添品页引导条',
        button_for: '查看',
      });
      jumpAction();
    },
    [jumpAction, sendRecommendLog, storageKey]
  );

  useEffect(() => {
    if (showGuide && visible && recommendProducts.length > 0 && !sendExposureLogRef.current) {
      sendExposureLogRef.current = true;
      sendRecommendLog?.('product_suggestion_model_show', {
        type: '添品页引导条',
      });
    }
  }, [recommendProducts.length, sendRecommendLog, showGuide, visible]);

  return showGuide && visible && recommendProducts.length > 0 ? (
    <div className={cx('recommend-promotions-guide-wrapper')}>
      <div
        className="img_container"
        onClick={() => {
          clickAction(false);
        }}>
        {recommendProducts.map(item => {
          return (
            <div className="img_container-single_card" key={item.promotion_id}>
              <img src={item.cover?.url_list?.[0] as string} className="cover" />
              <div className="mask" />
              <div className="earn">赚¥{(item.cos_fee || 0) / 100}</div>
            </div>
          );
        })}
      </div>
      <div
        className="txt_container"
        onClick={() => {
          clickAction(false);
        }}>
        <div className="txt_container-title">直播开单爆品推荐</div>
        <div className="txt_container-desc">
          <div className="txt_container-desc-info">促进用户自主选购</div>

          <div className="txt_container-desc-action">去添加</div>
        </div>
      </div>
      <img
        src={closeIcon}
        className="close"
        onClick={() => {
          clickAction(true);
        }}
      />
    </div>
  ) : null;
};

export default RecommendPromotionsGuide;
