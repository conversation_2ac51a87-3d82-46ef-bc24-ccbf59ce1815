.recommend-promotions-guide-wrapper {
  position: fixed;
  width: 358px;
  min-height: 68px;
  left: calc(50% - 179px);
  bottom: 102px;
  border-radius: 12px;
  border: 1px solid #fff;
  background: rgba(255, 255, 255, .85);
  box-shadow: 0 3px 19px 0 rgba(22, 24, 35, .14);
  backdrop-filter: blur(15px);
  z-index: 8;
  display: flex;
  align-items: center;
  padding: 12px;

  .img_container {
    display: flex;

    &-single_card {
      width: 44px;
      height: 44px;
      border-radius: 8px;
      position: relative;
      margin-right: 8px;

      .mask {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 44px;
        height: 22px;
        border-radius: 0 0 8px 8px;
        background: linear-gradient(180deg, rgba(0, 0, 0, .00) 0%, #000 100%);
        opacity: .7;
      }

      .cover {
        width: 44px;
        height: 44px;
        border-radius: 8px;
      }

      .earn {
        position: absolute;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        bottom: 3.5px;
        left: 0;
        width: 100%;
        color: #fff;
        text-align: center;
        font-family: PingFang SC;
        font-size: 10px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
      }

    }

    &-single_card:nth-of-type(3) {
      margin-right: 0;
    }
  }

  .txt_container {
    margin-left: 12px;
    align-self: start;
    margin-top: 3px;

    &-title {
      color: #161823;
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }

    &-desc {
      display: flex;    
      margin-top: 4px;

      &-info {
        color: rgba(22, 24, 35, .70);
        font-family: PingFang SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }

      &-action {
        margin-left: 4px;
        color: var(--system-primary-dark, #fe2c55);
        font-family: PingFang SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
      }
    }
  }

  .close {
    position: absolute;
    width: 16px;
    height: 16px;
    right: 16px;
    top: calc(50% - 8px);
  }
    
}
