import { API_BASE as AWEME_API_BASE, SAAS_BASE } from 'common/constants/index';
import { isInSAAS } from '@src/lib/env_detect';
import fetch from '@src/lib/request/fetch';
import { ApiResponse } from 'types/request';
const API_BASE = isInSAAS ? SAAS_BASE : AWEME_API_BASE;

export interface RedPoint {
  name?: string; // 红点名称
  num?: number; // 显示数字
}
export interface TabStruct {
  tab_id?: number;
  name?: string;
  red_point?: RedPoint;
}
export interface TabListData {
  tab_list?: TabStruct[]; // Tablist,包括 1专属推广、2定向计划、3我的店铺；短视频来的固定有：4我的橱窗 5我的收藏
  user_level?: number; // 达人等级
  log_id?: string; // log_id
}

export interface Url {
  uri?: string;
  url_list?: string[];
  width?: number;
  height?: number;
}
export interface LeaderBoard {
  order_cnt?: number; // 昨日支付订单量
  gmv?: number; // 昨日支付gmv
  good_eval_cnt?: number; // 好评数
}
export interface LeaderBoardRank {
  plan_id?: string; // 投放计划id
  plan_name?: string; // 投放计划名称
  rule_id?: string; // 子榜单id
  rule_title?: string; // 子榜单主标题
  rank?: number; // 排名
}
export interface NewGoodsTagInfo {
  banner_type?: string; // nice_goods：精选好物 new_arrival：首发新品  main_new_arrival：主推新品
  icon?: Url; // 图片
}
export interface OrientPlanApply {
  cos_ratio?: number; // 定向佣金率
  valid_time?: string; // 申请有效期
  reason?: string; // 申请理由
  status?: string; // 申请状态
}
export interface PrivilegeInfoDetail {
  tag_id: string; // 权益id标识
  title: string; // 权益标题(eg:正品保障)
  content: string; // 详细权益信息(100%正品保障，假一赔十，放心选购)
  icon?: Url; // 详细信息中的权益icon
  small_icon?: Url; // small icon
  show_small_icon?: boolean; // 是否展示small_icon
  link?: string; // 跳转链接
}
export interface PromotionAdditional {
  label?: string[];
  elastic_images?: Url[];
  elastic_title?: string;
  elastic_introduction?: string;
  third_part?: number; // 标识特殊的三方视频绑定的商品   1: 星图
}
export interface SalesInfo {
  campaign_type?: string;
  title?: string;
  pre_begin_time?: number;
  begin_time?: number;
  end_time?: number;
}
export interface ShopLevelLabel {
  tag?: string; // 店铺标示
  status?: number; // 限制状态
  text?: string; // desc
  url_text?: string; // 具体规则
  url?: string; // 规则链接
}
export interface Activity {
  activity_icon?: string;
  activity_type?: number;
  activity_id?: number;
  activity_banner?: string;
  activity_banner_small?: string;
  activity_banner_landscape?: string;
  width?: number;
  height?: number;
}
export interface CaptainCouponTag {
  coupon_meta_id?: string;
  meta_id?: string;
  discount_info?: string;
}
export interface PrivilegeInfo {
  privilege_id?: number; // 权益id(1:安心购，2:其他权益)
  icon?: Url; // 权益icon(只有安心购才会有icon)
  platform_public_url?: string; // 平台公示
  privilege_info_detail?: PrivilegeInfoDetail[]; // 权益详情
}
export interface GoodsSourceTagInfo {
  banner_type?: string; // global_shopping：全球购 brand_shop：品牌  base_selection：基地甄选
  icon?: Url; // 图片
}

export interface TagListIem {
  type: number;
  title: string;
}
export interface NoWorryInfo {
  tag_id?: number; // 标签id(1:无忧带)
  icon?: Url; // 权益icon(无忧带)
}
export interface PmtCard4Kol {
  promotion_id?: string; // 推广id
  product_id?: string; // 商品id
  title?: string; // 标题
  cover?: Url; // 商品图片
  price?: number; // 价格
  market_price?: number; // 原价
  cos_fee?: number; // 佣金
  cos_ratio?: number; // 佣金率
  detail_url?: string; // 详情页链接
  item_type?: number; // 平台来源
  sales?: number; // 销量
  icon?: string; // 小店标识icon
  activity?: Activity; // 平台活动(eg:宠粉节)
  sales_infos?: SalesInfo[]; // 促销活动(eg:满100减10,新人券等)
  new_goods_tag_infos?: NewGoodsTagInfo[]; // 新好货标签
  is_fav?: boolean; // 是否收藏
  cos_ratio_text?: string; // 佣金展示前缀(赚 ¥)
  shop_id?: number; // 店铺id
  shop_name?: string; // 店铺名称
  shop_level_label?: ShopLevelLabel; // 店铺等级信息
  can_contact_shop?: boolean; // 是否可建联
  activity_text?: string; // 活动文案,展示活动剩余时间
  images?: Url[]; // 商品图片,前端机审用
  recommend?: string; // 推荐理由
  exp_score?: string; // 店铺体验分
  recall_source?: string; // 运营坑位标记
  is_haitao?: boolean; // 是否为海淘商品
  is_in_window?: boolean; // 是否加橱窗
  leader_board?: LeaderBoard; // 榜单数据信息
  orient_plan_apply?: OrientPlanApply; // 定向申请
  leader_board_rank?: LeaderBoardRank; // 榜单排名信息
  privilege_info?: PrivilegeInfo; // 权益信息
  new_cos_ratio_text?: string; // 新版商品卡佣金展示(赚¥)
  real_cos_ratio?: number; // 高精度的佣金率
  goods_source_tag_info?: GoodsSourceTagInfo[]; // 货源标签
  is_leader?: boolean; // 是否是团长商品
  captain_coupon?: CaptainCouponTag[]; // 团长优惠券
  addtional?: PromotionAdditional; // 达人编辑的信息
  // 注入的埋点信息
  log_id?: string; // log_id
  search_id?: string; // 搜索的log_id
  tiered_cos?: TieredCosStruct; // 阶梯佣金信息
  tiered_cos_tag?: boolean; // 公开阶梯佣金标签
  douyin_goods_info?: {
    icon: Url;
  }; // 抖in好物
  pending_cos_ratio?: number;
  tag_list?: TagListIem[];
  no_worry_info?: NoWorryInfo; // 无忧带标签
  is_pk_competition?: boolean; // pk战队赛标签
  commission_ticket_tag?: boolean; // 是否展示发票tag
  commission_ticket_tip_info?: CommissionTicketTipInfo; // 发票提醒信息
  /** 大标签 */
  pic_product_tag?: Array<MainProductTag>;
  text_product_tag?: Array<MainProductTag>;
  risk_warning?: string[];
}

/** 小标签 */
export interface MainProductTag {
  /** 标签类型，在tcc配置 */
  type?: string;
  /** pc端前置图片 or 大标签图片 */
  pic?: string;
  /** 主文字，券类型的左端文字 */
  text?: TextTag;
  /** 券类型的右端文字 */
  text_right?: TextTag;
  height?: number;
  width?: number;
  /** 边框颜色 */
  border_color?: string;
}

export interface TextTag {
  text?: string;
  /** 文字颜色 */
  color?: string;
  /** 背景颜色 */
  bg_color?: string;
}
export interface Hover {
  coupon_hover?: Array<CouponHover>;
}

/** 多个券的hover */
export interface CouponHover {
  text_left?: TextTag;
  text_right?: TextTag;
  commission_ticket_tag?: boolean; // 是否展示发票tag
  commission_ticket_tip_info?: CommissionTicketTipInfo; // 发票提醒信息
}

export interface CommissionTicketTipInfo {
  type?: CommissionTicketTipType;
  effecte_time?: string; // 商家发票设置生效时间
  freeze_ratio?: number; // 达人冻佣比例
}

export enum CommissionTicketTipType {
  Normal = 1, // 正常添品
  ShopTicketPending = 2, // 商家发票逻辑待生效，提醒
  FreezeCommission = 3, // 带货冻结佣金提醒
}

export interface CosStruct {
  cos_ratio?: string;
  cos_fee?: number;
}
export enum TieredCosStatus {
  PreStatus = 1, // 未生效
  Active = 2, // 生效中
}
export interface TieredCosStruct {
  tiered_cos_list?: CosStruct[]; // 阶梯佣金,0:base， 1:top
  status?: TieredCosStatus; // 生效状态
  threshold?: number[]; // 阶梯佣金门槛
  start_time?: string; // 开始时间
  end_time?: string; // 结束时间
  plan_id?: number; // 阶梯计划Id
}

export interface TabData {
  count?: number; // 商品数
  promotions?: PmtCard4Kol[]; // 商品
  has_more?: boolean; // 是否还有更多(true:是,false:否)
  log_id?: string; // log_id
  tab_id?: string; // 区分单次查询
}

export interface PromotionSearchData {
  promotions?: PmtCard4Kol[]; // 推广商品列表
  total?: number;
}

export interface GetTabPmtResp extends ApiResponse {
  data: TabData;
}

// 视频添加商品获取tab data
export async function getTabPmt(params: {
  cursor?: number;
  count?: number;
  tab_id?: number; // tab对应的id
  source?: string; // 来源 //"media":短视频
}) {
  const res = await fetch<GetTabPmtResp>({
    url: `${API_BASE}/selection_api/selection_square/get_tab_pmt`,
    params,
  });
  return res;
}
