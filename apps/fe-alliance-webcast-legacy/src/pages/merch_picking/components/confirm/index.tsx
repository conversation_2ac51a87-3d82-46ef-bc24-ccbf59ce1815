
import React, { <PERSON><PERSON>N<PERSON>, <PERSON><PERSON>ventHandler } from 'react';
import Modal from 'components/modal/index';
import cx from 'classnames';
import './style/index';

interface Props extends ReactModal.Props {
    img?: string;
    title?: ReactNode;
    content?: string;
    htmlStr?: string;
    onCancel?: MouseEventHandler;
    onConfirm: MouseEventHandler;
    cancelText?: string;
    confirmText?: string;
    styleName?: string;
    children?: ReactNode;
}

export default React.memo((props: Props) => {
    const {
        img,
        title,
        content,
        htmlStr,
        onConfirm,
        onCancel,
        styleName,
        children,
        cancelText = '取消',
        confirmText = '确定',
        ...modalProps
    } = props;

    return (
        <Modal
            {...modalProps}
            className= {cx('confirm-live-modal', styleName ? styleName : '' )}
            overlayClassName="confirm-live-modal__overlay"
            trackProps={{
              title,
              img,
              cancelText,
              confirmText,
            }}
        >
            {!img ? null : <img src={img} className="confirm-live__img" />}
            {title ? <h3 className={"confirm-live__title".concat(htmlStr ? '-html' : '')}>{title}</h3> : null}
            {content ? <p className="confirm-live__body">{content}</p> : null}
            {htmlStr ? <p className="confirm-live__html_body" dangerouslySetInnerHTML={{__html: htmlStr}}></p> : null}
            {children ? children : null}
            <div className="confirm-live__footer">
                {cancelText && onCancel ? (
                    <span className="confirm-live__cancel" onClick={onCancel}>
                        {cancelText}
                    </span>
                ) : null}
                {confirmText && onConfirm ? (
                    <span className="confirm-live__confirm" onClick={onConfirm}>
                        {confirmText}
                    </span>
                ) : null}
            </div>
        </Modal>
    );
});
