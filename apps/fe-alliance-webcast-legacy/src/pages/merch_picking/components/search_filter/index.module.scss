/* stylelint-disable declaration-no-important */

.search-filter-option {
  border-radius: 6px !important;
  border: none !important;
  position: relative;
}

.search-filter-input {
  @extend .search-filter-option;
}

.search-filter-input-wrapper {
  padding-right: 0;
}


.search-filter-input--sheets-mode {
  @extend .search-filter-option;
  width: auto!important;
  max-width: 160px;
}

.search-filter-sheets {

  &--popup99 {

    :global {

      .custom-sheets__header {
        margin-top: 12px;
      }
    }
  }
}
