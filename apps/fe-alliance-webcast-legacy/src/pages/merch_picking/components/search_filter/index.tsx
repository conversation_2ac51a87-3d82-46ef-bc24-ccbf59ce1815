import { app } from '@bridge/webcast';
import { useMounted, useUpdateEffect } from '@byted/hooks';
import type { Filter, Option, SubmitTools, QuickFilter } from '@lib/platform-merchant-common';
import { FilterDrawerFactory, QuickFilterViewFactory, RangeInput, DropdownOption } from '@lib/platform-merchant-common';
import React, { useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { getPageMode } from '@lib/util/scene';
import { CommonFilter, FilterSelectType, FilterSource, GetFilterResponse, fetchFilter } from '../../services/api';
import { FILTER_OPTION_SPAN_V1, FILTER_OPTION_SPAN_V2 } from '../../constants';
import cx from 'classnames';
import styles from './index.module.scss';

type OptionFilterValue = {
  sub_fields: string[];
};

type RangeFilterValue = {
  range: {
    min?: string;
    max?: string;
  };
};

export type FilterValue = Partial<OptionFilterValue & RangeFilterValue>;
const { FilterDrawer, useFilterDrawerRef } = FilterDrawerFactory.create();
const { QuickFilterView, useQuickFilterViewRef } = QuickFilterViewFactory.create();

type RenderParams = Parameters<NonNullable<Filter['Render']>>;

type CustomRenderProps = {
  commonFilter: CommonFilter;
  filterKey: string;
  emit: RenderParams[0];
  result: RenderParams[1];
  tools: RenderParams[2];
  isMerchV2?: boolean;
};

const RangeCustomRender: React.FC<CustomRenderProps> = ({
  commonFilter,
  filterKey,
  emit,
  tools: { usePrevResult, beforeClose, onReset, validateField },
  isMerchV2,
}) => {
  const [leftValue, setLeftValue] = useState('');
  const [rightValue, setRightValue] = useState('');

  const prevResult = usePrevResult();
  beforeClose(hasClickedSubmitt => {
    if (hasClickedSubmitt) {
      return;
    }
    const [prevLeftValue = '', prevRightValue = ''] = (prevResult[filterKey] as [string, string]) || [];
    setLeftValue(prevLeftValue);
    setRightValue(prevRightValue);
  });

  onReset(() => {
    setLeftValue('');
    setRightValue('');
  });

  const handleBlur = ([min, max]: [string, string]) => {
    if (min && max) {
      validateField();
    }
  };

  return (
    <RangeInput
      inputWrapperClassName={isMerchV2 ? styles['search-filter-input-wrapper'] : ''}
      inputClassName={cx(styles['search-filter-input'], {
        [styles['search-filter-input--sheets-mode']]: isMerchV2,
      })}
      left={{
        value: leftValue,
        inputMode: 'numeric',
        placeholder: commonFilter.range_pair?.min || '',
        suffix: commonFilter.range_pair?.unit_name,
        onInput: (val, { extractNumStr }) => {
          const numVal = extractNumStr(val);
          setLeftValue(numVal);
          emit(filterKey, [numVal, rightValue]);
        },
        onBlur: (val, { extractNumStr }) => {
          const numVal = extractNumStr(val);
          handleBlur([numVal, rightValue]);
        },
      }}
      right={{
        value: rightValue,
        inputMode: 'numeric',
        placeholder: commonFilter.range_pair?.max || '',
        suffix: commonFilter.range_pair?.unit_name,
        onInput: (val, { extractNumStr }) => {
          const numVal = extractNumStr(val);
          setRightValue(numVal);
          emit(filterKey, [leftValue, numVal]);
        },
        onBlur: (val, { extractNumStr }) => {
          const numVal = extractNumStr(val);
          handleBlur([leftValue, numVal]);
        },
      }}
    />
  );
};

type Props = {
  visible: boolean;
  searchText?: string;
  filterSource: FilterSource;
  getQuickFilterContainer?: () => HTMLElement | null;
  onVisibleChange: (visible: boolean) => void;
  onSubmit: (result: Record<string, unknown>, hasSelectedValue: boolean) => void;
  isMerchV2?: boolean;
  sendRecommendLog?: (event: string, params?: Record<string, unknown>) => Promise<void>;
};

const SearchFilter: React.FC<Props> = ({
  visible,
  searchText,
  filterSource,
  getQuickFilterContainer,
  onVisibleChange,
  onSubmit,
  isMerchV2,
  sendRecommendLog,
}) => {
  const [filters, setFilters] = useState<Filter[]>([]);
  const [quickFilters, setQuickFilters] = useState<QuickFilter[]>([]);
  const filterDrawerRef = useFilterDrawerRef();
  const quickFilterViewRef = useQuickFilterViewRef();
  const optionKeys = useRef<string[]>([]);
  const rangeKeys = useRef<string[]>([]);
  const pageMode = getPageMode();

  const quickFilterContainer = getQuickFilterContainer?.();

  const getFilters = async () => {
    let res: GetFilterResponse;
    try {
      res = await fetchFilter({ filter_source: filterSource, search_text: searchText });
    } catch (err) {
      return { common: [], quick: [] };
    }

    const { common_filter, quick_filter } = res.data || {};
    optionKeys.current = [];
    rangeKeys.current = [];
    return {
      quick: (quick_filter || []).map<QuickFilter>(qf => {
        let filterType: QuickFilter['type'] = 'single';
        switch (qf.select_type) {
          case FilterSelectType.SINGLE:
            filterType = 'single';
            break;
          case FilterSelectType.MULTI:
          default:
            filterType = 'multi';
            break;
        }
        if (qf.text_pair) {
          switch (qf.select_type) {
            case FilterSelectType.SINGLE:
              filterType = 'dropdown-single';
              break;
            case FilterSelectType.MULTI:
              filterType = 'dropdown-multi';
              break;
            default:
          }
        }
        return {
          key: qf.parent_field || '',
          label: qf.name || '',
          type: filterType,
          ...(qf.text_pair
            ? { options: qf.text_pair?.map<DropdownOption>(i => ({ label: i.field || '', value: i.name || '' })) || [] }
            : { value: qf.field || '' }),
        };
      }),
      common: (common_filter || []).map<Filter>(cf => {
        const filterKey = cf.field || '';
        let filterType: Filter['type'] = 'single';
        switch (cf.select_type) {
          case FilterSelectType.SINGLE:
          case FilterSelectType.SINGLE_ONE_LINE:
            filterType = 'single';
            break;
          case FilterSelectType.MULTI:
            filterType = 'multi';
            break;
          default:
            filterType = 'custom';
        }

        const additionalProps: Pick<Filter, 'options' | 'Render' | 'getValue' | 'validator'> = {};
        if (filterType !== 'custom') {
          // 存一下单选和多选的key
          optionKeys.current.push(filterKey);
          // 旧版使用drawer模式，span=4（一行展示3个标签），新版使用sheets模式 span=3（一行展示4个标签）
          const spanNum = isMerchV2 ? FILTER_OPTION_SPAN_V2 : FILTER_OPTION_SPAN_V1;
          additionalProps.options = (cf.sub_text_filter?.text_pair || []).map<Option>(pair => {
            return {
              key: pair.field,
              label: pair.name || '',
              value: pair.field,
              span: cf.select_type === FilterSelectType.SINGLE_ONE_LINE ? 12 : spanNum,
              align: cf.select_type === FilterSelectType.SINGLE_ONE_LINE ? 'left' : undefined,
            };
          });
        } else if (cf.select_type === FilterSelectType.RANGE) {
          rangeKeys.current.push(filterKey);
          additionalProps.Render = (emit, result, tools) => (
            <RangeCustomRender
              commonFilter={cf}
              filterKey={filterKey}
              emit={emit}
              result={result}
              tools={tools}
              isMerchV2={isMerchV2}
            />
          );
          additionalProps.validator = ([min, max]: [string, string]) => {
            if ([min, max].filter(Boolean).length <= 1) {
              return true;
            }
            const leftNum = Number(min);
            const rightNum = Number(max);
            if (leftNum > rightNum) {
              app.toast({
                text: `${cf.name ? `【${cf.name}】的` : ''}最小值不能大于最大值`,
                type: 'prompt',
              });
              return false;
            }
            if (leftNum === rightNum) {
              app.toast({
                text: `${cf.name ? `【${cf.name}】的` : ''}最小值不能等于最大值`,
                type: 'prompt',
              });
              return false;
            }
            return true;
          };
        }

        return {
          key: filterKey,
          title: cf.name || '',
          type: filterType,
          enableCollapse: Boolean(cf.sub_text_filter?.is_fold),
          collapse: Boolean(cf.sub_text_filter?.default_fold),
          ...additionalProps,
        };
      }),
    };
  };

  const handleSubmit = (result: Record<string, unknown>, { validateResult }: SubmitTools) => {
    return validateResult().then(pass => {
      if (!pass) {
        return false;
      }
      quickFilterViewRef.current?.setFields(result, { triggerChange: false });
      const processed = {};
      optionKeys.current.forEach(key => {
        if (result[key]) {
          const optionValue = (result[key] instanceof Array ? result[key] : [result[key]]) as string[];
          if (optionValue.length > 0) {
            processed[key] = { sub_fields: optionValue } as OptionFilterValue;
          }
        }
      });
      rangeKeys.current.forEach(key => {
        if (result[key]) {
          const [min, max] = result[key] as [string, string];
          if ([min, max].filter(Boolean).length > 0) {
            const rangeValue: RangeFilterValue = { range: {} };
            min && (rangeValue.range.min = min);
            max && (rangeValue.range.max = max);
            processed[key] = rangeValue;
          }
        }
      });
      onSubmit(processed, Boolean(filterDrawerRef.current?.hasSelectedValue()));
    });
  };

  const handleQuickChange = (quickValues: Record<string, unknown>) => {
    filterDrawerRef.current?.setFields({ ...quickValues });
  };

  const handleQuickFilterClick = (originData?: QuickFilter) => {
    sendRecommendLog?.('product_suggestion_model_click', {
      type: '添品页推荐tab',
      button_for: originData?.label || '',
    });
  };

  const handleDrawerFilterClick = (originData?: Option) => {
    sendRecommendLog?.('product_suggestion_model_click', {
      type: '添品页推荐tab',
      button_for: originData?.label || '',
    });
  };

  const init = async () => {
    const { common, quick } = await getFilters();
    setFilters(common);
    setQuickFilters(quick);
  };

  useMounted(() => void init());

  useUpdateEffect(() => void init(), [searchText]);

  useUpdateEffect(() => void filterDrawerRef.current?.[visible ? 'open' : 'close'](), [visible]);

  return (
    <>
      {quickFilterContainer
        ? ReactDOM.createPortal(
            <QuickFilterView
              ref={quickFilterViewRef}
              commonFilters={filters}
              quickFilters={quickFilters}
              onChange={handleQuickChange}
              afterFilterClick={handleQuickFilterClick}
            />,
            quickFilterContainer
          )
        : null}
      <FilterDrawer
        ref={filterDrawerRef}
        optionClassName={styles['search-filter-option']}
        filters={filters}
        onSubmit={handleSubmit}
        onClose={() => onVisibleChange(false)}
        afterFilterClick={handleDrawerFilterClick}
        afterInitial={result => quickFilterViewRef.current?.setFields(result, { triggerChange: false })}
        mode={isMerchV2 ? 'sheets' : 'drawer'}
        className={cx({
          [styles[`search-filter-sheets--${pageMode}`]]: pageMode,
        })}
      />
    </>
  );
};

export default SearchFilter;
