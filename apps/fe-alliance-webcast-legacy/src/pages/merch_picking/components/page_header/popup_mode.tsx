/*
 * @Author: <EMAIL>
 * @Date: 2023-06-20 21:50:02
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-07-13 12:42:40
 * @Description: 浮层形式下的header
 */
import React from 'react';
import { getPageMode } from '@lib/util/scene';
import PageHeader, { Props } from 'components/page_header';
import cx from 'classnames';
import './index-v2.scss';

export default React.memo((props: Props) => {
  const { title, onNavBack, children, ...rest } = props || {};
  // 实验组 按照页面模式区分ui，对照组 保持旧版本
  const pageMode = getPageMode();
  return (
    <PageHeader
      className={`page-header--${pageMode || ''}`}
      title={title}
      onNavBack={onNavBack}
      backIcon={
        <span
          className={cx('page-header__back-icon', {
            [`page-header__back-icon--${pageMode}`]: pageMode,
          })}
        />
      }
      {...rest}>
      {children}
    </PageHeader>
  );
});
