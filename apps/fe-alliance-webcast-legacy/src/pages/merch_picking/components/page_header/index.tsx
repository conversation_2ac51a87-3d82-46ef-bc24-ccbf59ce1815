import React from 'react';
import { queryParamsFromGlobal as query } from '@src/lib/env_detect';
import { isLiving } from '@src/lib/util/merch_picking';
import PageHeader, { Props } from 'components/page_header';
import './index.scss';

export default React.memo((props: Props) => {
  const { title, onNavBack, children, ...rest } = props;
  const LivingIcon = <span className="page-header__icon" />;
  return (
    <PageHeader title={title} onNavBack={onNavBack} titleIcon={isLiving ? LivingIcon : null} {...rest}>
      {children}
    </PageHeader>
  );
});
