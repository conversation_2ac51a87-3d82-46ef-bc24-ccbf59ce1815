import React from 'react';
import cx from 'classnames';
import './index.scss';

interface Props {
    numberOfGoods: number;
    onPress: () => void;
}

export default React.memo((props: Props) => {
    const { numberOfGoods, onPress } = props;
    return (
        <div className="shopping-bag frow" onClick={onPress}>
            <span className={cx('shopping-bag__count', {'over-10': numberOfGoods > 9})}>{numberOfGoods}</span>
            <span className="shopping-bag__icon"></span>
        </div>
    );
});
