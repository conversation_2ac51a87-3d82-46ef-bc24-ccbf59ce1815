import React, {
  useRef,
  useState,
  useEffect,
  useCallback,
  ChangeEvent,
  KeyboardEvent,
  MouseEventHandler,
  ReactNode,
} from 'react';
import cx from 'classnames';
import { isInAweme, osPlatform } from '@src/lib/env_detect';
import { getPageMode } from '@lib/util/scene';

import './index.scss';

interface Props {
  value?: string;
  setValue?: (value: string) => void;
  placeholder: string;
  submitText?: ReactNode;
  onConfirm: (value: string) => void;
  blurOnScroll?: boolean;
  onNavBack: MouseEventHandler;
  preInputNode?: ReactNode;
}

export default React.memo((props: Props) => {
  const {
    value = '',
    placeholder,
    submitText = '确定',
    onConfirm,
    blurOnScroll = true,
    onNavBack,
    preInputNode,
    setValue,
  } = props;

  const [inputValue, setInputValue] = useState(value);
  useEffect(() => {
    setValue?.(inputValue);
  }, [inputValue, setValue]);
  // 实验组 按照页面模式区分ui
  const pageMode = getPageMode();
  const inputRef = useRef<HTMLInputElement>(null);
  const touchmoveHandler = useCallback(() => {
    const input = inputRef.current as HTMLInputElement;
    if (input && input === document.activeElement) {
      input.blur();
    }
  }, []);

  const onSubmit = () => {
    onConfirm(inputValue);
  };

  const onChange = (event: ChangeEvent<HTMLInputElement>) => {
    setInputValue(event.target.value);
  };

  const onKeydown = (event: KeyboardEvent) => {
    if (event.keyCode === 13) {
      onSubmit();
      (inputRef.current as HTMLInputElement).blur();
    }
  };

  const onClearClick = () => {
    setInputValue('');
    (inputRef.current as HTMLInputElement).focus();
  };

  const onFocus = () => {
    if (blurOnScroll) {
      document.addEventListener('touchmove', touchmoveHandler);
    }
  };

  const onBlur = () => {
    document.removeEventListener('touchmove', touchmoveHandler);
  };

  useEffect(() => {
    (inputRef.current as HTMLInputElement).focus();
  }, []);

  useEffect(() => {
    setInputValue(value);
  }, [value]);

  return (
    <div
      className={cx('search-bar frow f-ai-c', {
        'in-aweme': isInAweme,
        [`search-bar--${pageMode}`]: pageMode,
      })}>
      <span className="search-bar__back-icon" onClick={onNavBack} />
      {preInputNode}
      <div
        className={cx('search-bar__main f-1 frow f-ai-c', {
          'in-aweme': isInAweme,
        })}>
        <form action="javascript:;" className="f-1 frow f-ai-c">
          <input
            type="search"
            ref={inputRef}
            className="search-bar__input f-1"
            placeholder={placeholder}
            value={inputValue}
            onChange={onChange}
            onKeyDown={onKeydown}
            onFocus={onFocus}
            onBlur={onBlur}
            autoFocus
          />
        </form>
        {!inputValue ? null : <span className="search-bar__clear" onClick={onClearClick} />}
      </div>
      <span
        className={cx('search-bar__btn', {
          'in-aweme': isInAweme,
        })}
        onClick={onSubmit}>
        {submitText}
      </span>
    </div>
  );
});
