@import "~common/styles/merch_picking/mixins";

.search-bar {
  padding: 6px 12px 6px 10px;
  flex-shrink: 0;
  display: flex;
  align-items: center;

  &__back-icon {
    width: 24px;
    height: 24px;
    margin-right: 4px;
    display: inline-block;
    @include background(
      $size: 9px auto,
      $position: center,
      $image: url("data:image/svg+xml,%3Csvg width='20' height='36' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cpath d='M-36-26h320v88H-36z'/%3E%3Cpath fill='none' d='M-4-6h48v48H-4z'/%3E%3Cpath d='M4.783 18l14.92 14.877a1 1 0 0 1 .003 1.415l-1.412 1.416a1 1 0 0 1-1.414.002L.68 19.558a2.202 2.202 0 0 1 0-3.116L16.88.29a1 1 0 0 1 1.414.002l1.412 1.416a1 1 0 0 1-.002 1.415L4.784 18z' fill='%23161823' fill-rule='nonzero'/%3E%3C/g%3E%3C/svg%3E")
    ); }

  &__main {
    padding: 0 8px;
    height: 32px;
    border-radius: 16px;
    overflow: hidden;
    background-color: #f2f2f2;
    display: flex;
    align-items: center;
  }

  &__main.in-aweme {
    border-radius: 12px;
    height: 36px;
  }

  &__input {
    padding: 6px 0;
    font-size: 14px;
    border: none;
    outline: none;
    background-color: transparent;

    &[type="search"] {
      -webkit-appearance: none;
    }

    &::-webkit-search-cancel-button {
      display: none;
    }

    &[type="search"] {
      -webkit-appearance: none;
    }
  }

  &__clear {
    display: inline-block;
    width: 20px;
    height: 20px;
    @include background(
      $size: 14px auto,
      $position: center,
      $image: url("data:image/svg+xml,%3Csvg width='14' height='14' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7 0a7 7 0 1 1 0 14A7 7 0 0 1 7 0zm3.094 3.906a.875.875 0 0 0-1.238 0L7.001 5.762 5.144 3.906a.875.875 0 0 0-1.238 1.238L5.763 7 3.906 8.856a.875.875 0 0 0 1.238 1.238L7 8.237l1.856 1.857a.875.875 0 0 0 1.238-1.238L8.238 7l1.856-1.855a.875.875 0 0 0 0-1.238z' fill='%23BDBDBD' fill-rule='evenodd'/%3E%3C/svg%3E")
    );
  }

  &__btn {
    padding: 6px 0 6px 16px;
    font-size: 15px;
    color: #161823;
  }

  // &__btn.in-aweme {
  //   color: #fe2c55;
  // }
}
// 抖音也按默认黑色色值
// .aweme .search-bar__btn {
//     color: #fe2c55;
// }

.search-bar:not(.search-bar.in-aweme) {
  padding-bottom: 12px;
}
