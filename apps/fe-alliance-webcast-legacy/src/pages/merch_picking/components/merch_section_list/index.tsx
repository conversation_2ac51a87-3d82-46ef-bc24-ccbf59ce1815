import React, { use<PERSON>onte<PERSON>t, Mouse<PERSON>vent<PERSON>andler, useState, useRef, useEffect } from 'react';
import MerchItem from '../merch_item';
import createList, { ListRef } from 'components/list';
import { AppContext } from '../../modules/context';
import { ListState, ListFetcher } from 'components/list/state';
import MerchSkeleton from '../merch/skeleton';
import { MerchStarAtlasSection, Merch, MerchEventHandler, MAX_SELECT_COUNT, PortalTab } from '../../types';
import { NoviceShopTagParams } from 'components/novice-shop-tag/types';
import SelectAll from '../select_all';
import { canSelect, isAppended } from '../merch/helper';

import './index.scss';

const List = createList<MerchStarAtlasSection>();

interface Props {
  tab?: PortalTab;
  session?: object;
  cosFeeMode?: boolean;
  className?: string;
  interactive?: boolean;
  lazyImg?: boolean;
  noviceShopParams: NoviceShopTagParams;
  isMulti?: boolean;
  fetcher: ListFetcher<MerchStarAtlasSection> | null;
  onMerchPick: MerchEventHandler;
  onMerchClick: MerchEventHandler;
  EmptyView?(state: ListState<Merch>, onRetry: MouseEventHandler): React.ReactNode;
  onSelectChange?(merch: Merch, status: boolean): void;
  selectedList?: Merch[];
  cantSelectMore?: boolean;
  maxSelectCount?: number;
  onBatchAdd?: any;
  cancelSelectAllTab?: string;
  onClearSelectList(): void;
  sendSelectAllLog(status: boolean): void;
  isMerchV2?: boolean;
}

export default React.memo((props: Props) => {
  const {
    tab,
    className,
    cosFeeMode = true,
    onMerchPick,
    onMerchClick,
    lazyImg,
    noviceShopParams,
    isMulti = false,
    selectedList,
    onSelectChange,
    cantSelectMore,
    onClearSelectList,
    maxSelectCount = MAX_SELECT_COUNT,
    cancelSelectAllTab = '',
    onBatchAdd,
    sendSelectAllLog,
    isMerchV2,
  } = props;

  const [selectAll, setSelectAll] = useState(false);
  const listRef = useRef([]);
  const listCompRef = useRef<ListRef<Merch> | null>(null);

  const { appendedMerchList } = useContext(AppContext);

  /** 筛选出商品列表中的可选商品 */
  const getCanSelectList = (list: MerchStarAtlasSection[]) => {
    let canSelectList = [];
    list.forEach(task => {
      const { promotions = [] } = task;
      const filterProms = promotions?.filter(merch => canSelect(merch) && !isAppended(appendedMerchList, merch));
      canSelectList = canSelectList.concat(filterProms);
    });
    return canSelectList;
  };

  /** 当全选状态变更时，需要批量处理商品选中态 */
  const handleSelectAll = () => {
    if (!isMulti) {
      return;
    }
    const newStatus = !selectAll;
    setSelectAll(newStatus);
    sendSelectAllLog?.(newStatus);

    if (!newStatus) {
      // 取消全选，将该tab下的所有商品都取消选中
      return onClearSelectList?.();
    }

    const canSelectList = getCanSelectList(listRef.current || []);
    canSelectList.forEach(item => onSelectChange?.(item, true));
  };

  /** 如果是全选状态，那么翻页的时候需要把新加载出来的内容加入选中 */
  const handleListUpdate = list => {
    listRef.current = list;
    if (!selectAll) {
      return;
    }
    const canSelectList = getCanSelectList(list);
    canSelectList.forEach(item => onSelectChange?.(item, true));
  };

  const handleSelectChange = (merch, status) => {
    // 如果有商品取消选中，则取消全选状态
    if (!status) {
      setSelectAll(false);
    }
    onSelectChange?.(merch, status);
  };

  useEffect(() => {
    // 如果在别的tab里取消了该tab下也已经添加的商品，则取消全选态
    if (!tab?.key || (cancelSelectAllTab.indexOf(tab.key) === -1 && cancelSelectAllTab.indexOf('all') === -1)) {
      return;
    }
    setSelectAll(false);
  }, [cancelSelectAllTab, tab]);

  const renderMerchItem = (data: MerchStarAtlasSection, index: number) => {
    const hasAppendedMerch = appendedMerchList.length > 0;
    const { promotions, task_name } = data;
    return (
      <div className="merch-section__list">
        <span className="merch-section__list-title">{task_name}</span>
        {promotions.map(item => {
          const isAppended = hasAppendedMerch && Boolean(appendedMerchList.find(m => m === item.promotion_id));
          const isSelected =
            isMulti &&
            !isAppended &&
            (selectedList || []).some(selectedItem => selectedItem.promotion_id === item.promotion_id);
          const cantSelect = !isSelected && cantSelectMore;
          return (
            <MerchItem
              key={item.promotion_id}
              data={item}
              noviceShopParams={noviceShopParams}
              index={index}
              onPick={onMerchPick}
              onClick={onMerchClick}
              isAppended={isAppended}
              cosFeeMode={cosFeeMode}
              lazyImg={lazyImg}
              isMulti={isMulti}
              isSelected={isSelected}
              onSelectChange={handleSelectChange}
              cantSelect={cantSelect}
              isMerchV2={isMerchV2}
            />
          );
        })}
      </div>
    );
  };
  return (
    <>
      <List
        {...props}
        ItemSkeleton={MerchSkeleton}
        className={className}
        renderItem={renderMerchItem}
        onListUpdate={handleListUpdate}
      />
      {isMulti && (
        <SelectAll
          selectAll={selectAll}
          maxSelectCount={maxSelectCount}
          selectedList={selectedList || []}
          onBatchSelectAllChange={handleSelectAll}
          onBatchAdd={onBatchAdd}
          isMerchV2={isMerchV2}
          listData={listCompRef.current?.listData}
        />
      )}
    </>
  );
});
