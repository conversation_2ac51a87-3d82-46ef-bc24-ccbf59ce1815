import React, { useState, useEffect } from 'react';
import via from '@ies/webcast-via';
import { isAweme, isAndroid } from '@src/common/kit/env';
import { formatQueryString } from '@src/lib/util';
import { PublishContinuePushStream } from '@src/lib/util/event';
import Modal from 'components/modal/index';
import showToast from 'components/toast/ecom_toast';
import { sendLog } from '../../services/utils';
import { setAnXinGou } from '../../services/api';
import { openSchema } from '@src/common/bridge';
import { genSchema } from '@lib/util/merch_picking';
import { queryParamsFromGlobal } from '@src/lib/env_detect';
import { AnXinGouModalInfo } from '../../types';
import './index.scss';

const SHOWN_KEY = 'ecom_ip_has_shown_guide';

interface Props {
  ModalInfo: AnXinGouModalInfo;
}

export default React.memo((props: Props) => {
  const { ModalInfo = {} as AnXinGouModalInfo } = props;
  const { room_id, anchor_id } = formatQueryString();
  const roomId = Array.isArray(room_id) ? room_id[0] : room_id;
  const anchorId = Array.isArray(anchor_id) ? anchor_id[0] : anchor_id;
  const liveInfo = {
    room_id: roomId || 0,
    anchor_id: anchorId || '',
    live_status: roomId && Number(roomId) ? 'on' : 'off',
  };
  const canUseRefresh = queryParamsFromGlobal.enter_from === 'webcast_living';

  const [isOpen, setIsOpen] = useState(false);

  const handleClickProtocol = (url: string) => {
    // 直播中 跳转全屏页需要主动发送 continue_push_stream 事件，否则直播画面会黑屏
    // PublishContinuePushStream里做了新版本和直播中状态的判断
    PublishContinuePushStream();
    openSchema({
      schema: genSchema({ url }),
    });
  };

  useEffect(() => {
    const setShown = () =>
      window.ToutiaoJSBridge && window.ToutiaoJSBridge.call('x.setStorageItem', { key: SHOWN_KEY, data: 1 }, () => {});
    window.ToutiaoJSBridge &&
      window.ToutiaoJSBridge.call('x.getStorageItem', { key: SHOWN_KEY }, res => {
        const { code, data } = res;
        if (!code || data === 1 || (isAndroid && data?.data === 1)) return;
        setIsOpen(true);
        if (canUseRefresh) {
          via.business.refreshPromotions({ need_refresh: true });
        }
        setShown();
        sendLog('livesdk_securebuy_guide_show', {
          EVENT_ORIGIN_FEATURE: 'TEMAI',
          ...liveInfo,
        });
      });
  }, []);

  const handleClick = (type: 'skip' | 'open' | 'close') => {
    sendLog('livesdk_securebuy_guide_click', {
      EVENT_ORIGIN_FEATURE: 'TEMAI',
      ...liveInfo,
      button_for: type,
    });
    setIsOpen(false);
    if (type !== 'open') return;
    // 开启安心购
    setAnXinGou({ operate: 1 })
      .then(res => {
        if (canUseRefresh) {
          via.business.refreshPromotions({ need_refresh: true });
        }
        window.location.reload();
      })
      .catch(error => {
        showToast(error?.msg || '网络错误，请重试');
      });
    return;
  };

  if (!isAweme || JSON.stringify(ModalInfo) === '{}') return null;

  const {
    title,
    text,
    confirm_button: confirm = '暂不开启',
    cancel_button: cancel = '开启安心购',
    switch_on_info: info,
    switch_on_constraint: constraint,
    agreement_content: protocol,
    agreement_url: protocolUrl,
  } = ModalInfo;

  return (
    <Modal
      trackProps={{
        title: info?.title,
        cancelText: cancel,
        confirmText: confirm,
      }}
      isOpen={isOpen}
      className="anxingou"
      overlayClassName="anxingou__overlay"
      onRequestClose={() => handleClick('close')}>
      <div className="anxingou__container">
        <div className="anxingou-header">
          <span className="anxingou-header__close" onClick={() => handleClick('close')} />
          <div className="anxingou-header__title" style={{ backgroundImage: `url(${title})` }} />
          <div className="anxingou-header__sub">
            {text.map(item => (
              <span key={item} className="anxingou-header__sub__item">
                {item}
              </span>
            ))}
          </div>
        </div>
        <div className="anxingou-detail">
          <div className="anxingou-detail__title">{info.title}</div>
          {info.switch_on_content?.map(item => {
            return (
              <div className="anxingou-detail-list" key={item.item_title}>
                <div
                  className="anxingou-detail-list__icon"
                  style={item.item_icon ? { backgroundImage: `url(${item.item_icon})` } : {}}
                />
                <div className="anxingou-detail-list__text">
                  <div className="anxingou-detail-list__title">{item.item_title || ''}</div>
                  <div className="anxingou-detail-list__sub">{item.item_text || ''}</div>
                </div>
              </div>
            );
          })}
        </div>
        <div className="anxingou-rule">
          <div className="anxingou-rule__title">{constraint.title}</div>
          {constraint.switch_on_constraint?.map(item => (
            <div className="anxingou-rule-list" key={`${item?.item_title}-${item?.item_first_text}`}>
              <span>{item.item_first_text}</span>
              <span className="anxingou-rule-list__highlight">{item.item_second_text}</span>
            </div>
          ))}
          {protocol ? (
            <div className="anxingou-rule-protocol">
              <span>阅读并同意</span>
              <span className="anxingou-rule-protocol__link" onClick={() => handleClickProtocol(protocolUrl)}>
                {protocol}
              </span>
            </div>
          ) : null}
        </div>
        <div className="anxingou-footer">
          <div className="anxingou-footer__btn" onClick={() => handleClick('skip')}>
            {cancel}
          </div>
          <div className="anxingou-footer__btn confirm" onClick={() => handleClick('open')}>
            {confirm}
          </div>
        </div>
      </div>
    </Modal>
  );
});
