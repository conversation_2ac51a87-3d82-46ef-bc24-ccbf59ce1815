@import '~common/styles/merch_picking/mixins';

.ReactModal {
  &__Overlay {
    z-index: 100;
    @include mask();
    &--after-open {
      animation: fade-in 0.2s ease-in-out forwards;
    }
    &--before-close {
      animation: fade-out 0.4s ease-in-out forwards;
    }
  }
  &__Content {
    &:focus {
      outline: none !important;
    }
  }
}

.anxingou {
  font-family: PingFang SC;
  font-style: normal;

  &__overlay {
    z-index: 999999999;
    background: rgba(0, 0, 0, 0.45);
  }

  &__container {
    position: absolute;
    left: 0;
    right: 0;
    width: 100%;
    top: 27%;
    bottom: 0;
    background-color: white;
    border-radius: 8px 8px 0px 0px;
    overflow: scroll;

  }

  &-header {
    padding: 24px 0 12px;
    background: rgba(22, 24, 35, 0.2);
    background: rgba(255, 98, 38, 0.05);
    font-weight: 500;
    font-size: 17px;
    line-height: 24px;
    text-align: center;
    color: #161823;
    background-image: url(./bg_anxingou_header.svg);
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
    &__close {
      position: absolute;
      background-image: url('./icon_cancel.svg');
      background-repeat: no-repeat;
      background-size: cover;
      width: 24px;
      height: 24px;
      display: inline-block;
      right: 16px;
      top: 16px;
    }
    &__title {
      background-size: cover;
      width: 166px;
      height: 22px;
      margin: 0 auto 4px;
    }
    &__sub {
      font-size: 12px;
      line-height: 17px;
      color: #cc591f;
      opacity: 0.9;
      text-shadow: 0px 2px 4px rgba(205, 90, 31, 0.1);
      &__item {
        margin-right: 4px;
      }
    }
  }

  &-detail {
    background: rgba(255, 98, 38, 0.05);
    padding: 0 16px;
    &__title {
      background-color: white;
      font-weight: 500;
      font-size: 13px;
      line-height: 22px;
      color: #161823;
      padding: 18px 16px 4px;
      border-radius: 4px 4px 0 0;
    }
    &-list {
      display: flex;
      padding: 12px 16px;
      background-color: white;
      &:last-child {
        border-radius: 0 0 4px 4px;
      }
      &__icon {
        height: 36px;
        width: 36px;
        background-repeat: no-repeat;
        background-size: cover;
        flex-shrink: 0;
        margin-right: 12px;
      }
      &__text {
        flex-grow: 1;
      }
      &__title {
        font-weight: 500;
        font-size: 12px;
        line-height: 17px;
        color: #262626;
      }
      &__sub {
        font-size: 11px;
        line-height: 15px;
        color: #262626;
        opacity: 0.8;
        margin-top: 4px;
        padding-bottom: 12px;
        border-bottom: 0.5px solid rgba($color: #c79777, $alpha: 0.2);
        .anxingou-detail-list:last-child & {
          border-bottom: unset;
        }
      }

      &-tips {
        font-size: 11px;
        line-height: 40px;
        color: rgba(22, 24, 35, 0.5);
        text-align: center;
        &__link {
          color: #04498d;
          margin: 0 4px;
          display: inline-flex;
          align-items: center;
          .icon-arrow-right {
            font-size: 8px;
          }
        }
      }
    }
  }

  &-rule {
    background: rgba(255, 98, 38, 0.05);
    padding: 20px 32px 70px;
    &__title {
      font-weight: 500;
      font-size: 13px;
      line-height: 22px;
      color: #262626;
      margin-bottom: 8px;
    }
    &-list {
      font-size: 11px;
      line-height: 20px;
      color: #262626;
      &__highlight {
        color: #CC591F;
      }
    }
    &-protocol {
      font-size: 11px;
      line-height: 20px;
      color: rgba(38, 38, 38, 0.6);
      &__link {
        color: #04498D;
      }
    }
  }

  &-footer {
    position: fixed;
    left: 0;
    right: 0;
    background-color: #fff;
    padding: 8px 16px 0;
    display: flex;
    @include safearea(bottom, padding-bottom);
    bottom: calc(16px + constant(safe-area-inset-bottom));
    bottom: calc(16px + env(safe-area-inset-bottom));
    &__btn {
      font-weight: 500;
      font-size: 15px;
      line-height: 21px;
      text-align: center;
      color: #262626;
      flex: 1;
      border: 0.5px solid rgba(38, 38, 38, 0.2);
      border-radius: 2px;
      padding: 11px 0;
      &.confirm {
        color: #ffffff;
        margin-left: 7px;
        background: #FE2C55;
        border-color: #FE2C55;
      }
    }
  }
}
