/* eslint-disable @typescript-eslint/ban-ts-comment */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/ban-types */
/* eslint-disable @typescript-eslint/member-ordering */
import cx from 'classnames';
import React, { use<PERSON>ontext, MouseEventHandler, useEffect, useState, useRef } from 'react';
import MerchItem from '../merch_item';
import createList, { ListRef } from 'components/list';
import { AppContext } from '../../modules/context';
import { ListState, ListFetcher } from 'components/list/state';
import MerchSkeleton from '../merch/skeleton';
import { Merch, MerchEventHandler, MachineAuditResponse, MAX_SELECT_COUNT, PortalTab } from '../../types';
import { NoviceShopTagParams } from 'components/novice-shop-tag/types';
import { PairLimitCheckCallback } from '../pair_limit/types';
import SelectAll from '../select_all';
import { canSelect, isAppended as getIsAppended } from '../merch/helper';
import HeaderBar from '../header-bar';
import HeaderFilter from '../header-filter';
import './index.scss';
import { TAB_KEYS } from '../../constants';
import { myCoreLinkClient } from '@src/lib/util/mera/core-link';
import QuickFilterFn from './quick-filter';
import { AppController } from '../../modules/controller';

const List = createList<Merch>();

interface Props {
  tab?: PortalTab;
  isMulti?: boolean;
  session?: object;
  cosFeeMode?: boolean;
  className?: string;
  interactive?: boolean;
  lazyImg?: boolean;
  /** 是否是推荐页面，不展示月销，展示库存 */
  isShowSales?: boolean;
  fetcher: ListFetcher<Merch> | null;
  machineAudit?: MachineAuditResponse;
  isHiddenLoadMore?: boolean;
  isFromLinkSearch?: boolean;
  noviceShopParams: NoviceShopTagParams;
  onMerchPick: MerchEventHandler;
  onMerchClick: MerchEventHandler;
  onPairLimitCheck?: PairLimitCheckCallback;
  EmptyView?(state: ListState<Merch>, onRetry: MouseEventHandler): React.ReactNode;
  onSelectChange?(merch: Merch, status: boolean): void;
  selectedList?: Merch[];
  cantSelectMore?: boolean;
  maxSelectCount?: number;
  onBatchAdd?: any;
  cancelSelectAllTab?: string;
  onClearSelectList(): void;
  sendSelectAllLog(status: boolean): void;
  showBlockMerchant?: boolean;
  refresh?: () => void;
  showHeader?: boolean;
  showHeaderFilter?: boolean;
  needImpression?: boolean;
  bottomHintNode?: React.ReactNode;
  onMerchTitleClick(merch: Merch): void;
  onMerchBetterPriceClick(merch: Merch): void;
  isMerchV2?: boolean;
  autoSelectAll?: boolean;
  changeAutoSelectAll?(flag: boolean): void;
  showRecommend?: boolean;
  sendRecommendLog?: (event: string, params?: Record<string, unknown>) => Promise<void>;
  afterExposured?: () => void;
  changeShowAddRecommend?: (show: boolean) => void;
  mixController?: AppController['mixController'];
  isLinkSearch?: boolean;
}

export default React.memo((props: Props) => {
  const {
    tab,
    isMulti = false,
    className,
    cosFeeMode = true,
    onMerchPick,
    machineAudit,
    onMerchClick,
    lazyImg,
    isShowSales,
    isFromLinkSearch,
    noviceShopParams,
    onPairLimitCheck,
    onSelectChange,
    selectedList,
    cantSelectMore = false,
    maxSelectCount = MAX_SELECT_COUNT,
    onBatchAdd,
    cancelSelectAllTab = '',
    onClearSelectList,
    sendSelectAllLog,
    showBlockMerchant = false,
    refresh,
    showHeader = false,
    session,
    showHeaderFilter = false,
    needImpression = false,
    bottomHintNode,
    onMerchTitleClick,
    onMerchBetterPriceClick,
    isMerchV2,
    autoSelectAll,
    changeAutoSelectAll,
    showRecommend,
    sendRecommendLog,
    afterExposured,
    changeShowAddRecommend,
    isLinkSearch,
  } = props;

  const [selectAll, setSelectAll] = useState(false);
  const listRef = useRef([]);
  const listCompRef = useRef<ListRef<Merch> | null>(null);

  const { appendedMerchList } = useContext(AppContext);

  /** 筛选出商品列表中的可选商品 */
  const getCanSelectList = (list: Merch[]) =>
    list?.filter(merch => canSelect(merch) && !getIsAppended(appendedMerchList, merch));

  /** 当全选状态变更时，需要批量处理商品选中态 */
  const handleSelectAll = () => {
    if (!isMulti) {
      return;
    }
    const newStatus = !selectAll;
    setSelectAll(newStatus);
    sendSelectAllLog?.(newStatus);

    if (!newStatus) {
      // 取消全选，将该tab下的所有商品都取消选中
      return onClearSelectList?.();
    }

    const canSelectList = getCanSelectList(listRef.current || []);
    canSelectList.forEach(item => onSelectChange?.(item, true));
  };

  const listEmptyRef = useRef<boolean | undefined>(undefined);
  /** 如果是全选状态，那么翻页的时候需要把新加载出来的内容加入选中 */
  const handleListUpdate = (list: Merch[]) => {
    if (!listEmptyRef.current) {
      listEmptyRef.current = Boolean(list?.length);
      if (listEmptyRef.current) {
        myCoreLinkClient.sendCoreLinkEvent('live_add_promotion_merchcart_tab_notempty');
      }
    }
    listRef.current = list;
    if (!selectAll) {
      return;
    }
    const canSelectList = getCanSelectList(list);
    canSelectList.forEach(item => onSelectChange?.(item, true));
  };

  const handleSelectChange = (merch, status) => {
    // 如果有商品取消选中，则取消全选状态
    if (!status) {
      setSelectAll(false);
    } else {
      myCoreLinkClient.sendCoreLinkEvent('live_add_promotion_checkbox_click');
    }
    onSelectChange?.(merch, status);
  };

  const [totalNum, setTotalNum] = useState(0);
  const handleListStateChange = ({ total }: ListState<Merch>) => {
    setTotalNum(total);
  };

  const handleFilterSubmit = (result: Record<string, unknown>) => {
    listCompRef.current?.setListFetchParams({ commonFilter: result });
    listCompRef.current?.fetchListData({ reset: true });
  };

  useEffect(() => {
    // 如果在别的tab里取消了该tab下也已经添加的商品，或者点击确认添加后，取消全选态
    if (!tab?.key || (cancelSelectAllTab.indexOf(tab.key) === -1 && cancelSelectAllTab.indexOf('all') === -1)) {
      return;
    }
    setSelectAll(false);
  }, [cancelSelectAllTab]);
  useEffect(() => {
    afterExposured?.();
  }, []);

  const renderMerchItem = (data: Merch, index: number) => {
    const hasAppendedMerch = appendedMerchList.length > 0;
    const isAppended = hasAppendedMerch && Boolean(appendedMerchList.find(m => m === data.promotion_id));

    const isSelected =
      isMulti && !isAppended && (selectedList || []).some(item => item.promotion_id === data.promotion_id);
    const cantSelect = !isSelected && cantSelectMore;

    return (
      <MerchItem
        tab={tab}
        mixController={props.mixController}
        key={data.promotion_id + index}
        isMulti={isMulti}
        data={data}
        index={index}
        onPick={onMerchPick}
        onClick={onMerchClick}
        isAppended={isAppended}
        isSelected={isSelected}
        cosFeeMode={cosFeeMode}
        lazyImg={lazyImg}
        isShowSales={isShowSales}
        machineAudit={machineAudit}
        noviceShopParams={noviceShopParams}
        isFromLinkSearch={isFromLinkSearch}
        onPairLimitCheck={onPairLimitCheck}
        onSelectChange={handleSelectChange}
        cantSelect={cantSelect}
        showBlockMerchant={showBlockMerchant}
        refresh={refresh}
        needImpression={needImpression}
        onMerchTitleClick={onMerchTitleClick}
        onMerchBetterPriceClick={onMerchBetterPriceClick}
        isMerchV2={isMerchV2}
        showRecommend={showRecommend}
        isLinkSearch={isLinkSearch}
      />
    );
  };
  return (
    <>
      {showHeader && (
        <HeaderBar
          total={totalNum}
          searchText={(session as { searchText?: string } | undefined)?.searchText}
          onFilterSubmit={handleFilterSubmit}
          isMerchV2={isMerchV2}
        />
      )}
      {showHeaderFilter && (
        <HeaderFilter onFilterSubmit={handleFilterSubmit} isMerchV2={isMerchV2} sendRecommendLog={sendRecommendLog} />
      )}
      <QuickFilterFn tab={tab} handleFilterSubmit={handleFilterSubmit} />
      <List
        ref={listCompRef}
        {...props}
        ItemSkeleton={MerchSkeleton}
        className={cx('merch-list', className)}
        renderItem={renderMerchItem}
        onListUpdate={handleListUpdate}
        onStateChange={handleListStateChange}
        changeShowAddRecommend={changeShowAddRecommend}
      />
      {isMulti && (
        <SelectAll
          selectAll={selectAll}
          maxSelectCount={maxSelectCount}
          selectedList={selectedList || []}
          onBatchSelectAllChange={handleSelectAll}
          onBatchAdd={onBatchAdd}
          hintNode={bottomHintNode}
          isMerchV2={isMerchV2}
          listData={listCompRef.current?.listData}
          autoSelectAll={autoSelectAll && tab?.key === TAB_KEYS.RECOMMENDED_PRODUCT}
          changeAutoSelectAll={changeAutoSelectAll}
        />
      )}
    </>
  );
});
