import React, { useRef, useState, useEffect } from 'react';
import cx from 'classnames';
import { PortalTab, BindSource } from '../../../types';
import { QuickFilter, fetchFilter, FilterSource } from '../../../services/api';

interface Props {
  tab?: PortalTab;
  handleFilterSubmit: (p: Record<string, unknown>) => void;
}

export default React.memo((props: Props) => {
  const { tab, handleFilterSubmit } = props;
  const [quickFilter, setQuickFilter] = useState<QuickFilter[]>([]);
  const [quickFilterRules, setQuickFilterRules] = useState<QuickFilter>({});

  useEffect(() => {
    if (tab?.type === BindSource.RecentlyGoods || tab?.type === BindSource.ExclusiveCooperate) {
      let filterParams = { filter_source: FilterSource.RECENTLYGOODS };
      if (tab?.type === BindSource.ExclusiveCooperate) {
        filterParams = { filter_source: FilterSource.ExclusiveCooperate };
      }
      fetchFilter?.(filterParams)?.then(res => {
        const { quick_filter = [] } = res?.data || {};
        setQuickFilter(quick_filter);
        setQuickFilterRules(quick_filter?.[0]);
        const field = quick_filter?.[0]?.field;
        const params = {
          [field]: {
            sub_fields: [field],
          },
        };
        handleFilterSubmit({ ...params });
      });
    }
  }, [tab]);

  return (
    <>
      {(tab?.type === BindSource.RecentlyGoods || tab?.type === BindSource.ExclusiveCooperate) && (
        <div className="swiper-genre-tabs">
          {quickFilter?.map(item => {
            return (
              <div
                key={item?.field}
                className={cx('swiper-genre-tab', item?.field === quickFilterRules?.field && 'swiper-genre-tab-active')}
                onClick={() => {
                  const params = {
                    [item?.field]: {
                      sub_fields: [item?.field],
                    },
                  };
                  setQuickFilterRules(item);
                  handleFilterSubmit({ ...params });
                }}>
                {item?.name}
              </div>
            );
          })}
        </div>
      )}
    </>
  );
});
