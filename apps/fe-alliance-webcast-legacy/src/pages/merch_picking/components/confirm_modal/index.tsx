
import React, { <PERSON><PERSON>N<PERSON>, <PERSON>EventHandler } from 'react';
import Modal from 'components/modal/index';
import cx from 'classnames';
import './style/index';

interface Props extends ReactModal.Props {
    img?: string;
    title?: ReactNode;
    content?: string;
    htmlStr?: string;
    onCancel?: MouseEventHandler;
    onConfirm: MouseEventHandler;
    cancelText?: string;
    confirmText?: string;
    styleName?: string;
    children?: ReactNode;
}

export default React.memo((props: Props) => {
    const {
        img,
        title,
        content,
        onConfirm,
        onCancel,
        styleName,
        children,
        cancelText = '取消',
        confirmText = '确定',
        ...modalProps
    } = props;

    return (
        <Modal
            {...modalProps}
            className= {cx('confirm-live-qualification-modal', styleName ? styleName : '' )}
            overlayClassName="confirm-live-qualification-modal__overlay"
            trackProps={{
              title,
              img,
              cancelText,
              confirmText,
            }}
        >
            {!img ? null : <img src={img} className="confirm-live-qualification__img" />}
            {title ? <h3 className="confirm-live-qualification__title">{title}</h3> : null}
            {content ? <p className="confirm-live-qualification__content">{content}</p> : null}
            {children ? children : null}
            <div className="confirm-live-qualification__footer">
                {cancelText && onCancel ? (
                    <span className="confirm-live-qualification__cancel" onClick={onCancel}>
                        {cancelText}
                    </span>
                ) : null}
                <span className="confirm-live-qualification__confirm" onClick={onConfirm}>
                    {confirmText}
                </span>
            </div>
        </Modal>
    );
});
