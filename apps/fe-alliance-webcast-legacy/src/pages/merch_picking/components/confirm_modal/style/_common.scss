@import '~common/styles/merch_picking/mixins';
@import '~common/styles/merch_picking/visual/hairline';
@import '~common/styles/merch_picking/modules/keyframe';

.ReactModal {
    &__Overlay {
        z-index: 100;
        @include mask();
        &--after-open {
            animation: fade-in .2s ease-in-out forwards;
        }
        &--before-close {
            animation: fade-out .4s ease-in-out forwards;
        }
    }
    &__Content {
        &:focus {
            outline: none !important;
        }
    }
}

.confirm-live-qualification {
    &-modal {
        @include absolute-center();
        width: 300px;
        text-align: center;
        background-color: #ffffff;
        &__overlay {
            @include mask();
            z-index: 1000;
            background-color: rgba(0,0,0,0.5);
        }
        border-radius: 4px;
    }
    &__img {
        width: 100%;
        height: 140px;
        border-radius: 4px 4px 0px 0px;
        margin-top: -1px;
    }
    &__title {
        margin-top: 32px;
        color: #000;
        font-size: 17px;
        font-weight: 500;
        padding: 0 22px;
        &--lg {
            margin: 32px 0;
            color: #353535;
            font-size: 16px;
        }
    }
    &__title-html {
        margin: 24px 0 14px;
        color: #000;
        font-size: 17px;
        padding: 0 22px;
        font-weight: 500;
    }
    &__html_body {
        text-align: left;
        margin-top: -9%;
        margin-bottom: 0;
        a {
            color: #387cdf;
        }
    }
    &__body {
        // padding: 0 24px;
        // margin: 32px 0;
        font-size: 14px;
        line-height: 20px;
        white-space: pre-wrap;
        color: rgba(22, 24, 35, 0.75);
    }
    &__footer {
        display: flex;
        flex-direction: row;
        margin-bottom: 24px;
        padding: 0 24px;
    }
    
}
.confirm-live-qualification__content {
    margin: 0 20px 32px;
    font-size: 14px;
    line-height: 20px;
    color: rgba(22, 24, 35, 0.75);
    text-align: center;
}