@import './common';

.confirm-live-qualification {
  &-modal {
    @include absolute-center();
    width: 280px;
    text-align: center;
    background-color: #ffffff;
    &__overlay {
      @include mask();
      z-index: 1000;
      background-color: rgba(0, 0, 0, 0.5);
    }
    border-radius: 4px;
    &__width {
      width: 260px;
    }
  }
  &__footer {
    display: flex;
    flex-direction: row;
    padding: 0;
    margin-bottom: 0px;
    border-top: 0.5px solid rgba(22, 24, 35, 0.12);
  }
  &__cancel,
  &__confirm {
    flex: 1;
    padding: 15px 0;
    font-size: 15px;
    line-height: 21px;
    background-color: #fff;
    border-radius: 4px;
  }
  &__cancel {
    border-right: 0.5px solid rgba(22, 24, 35, 0.12);
    font-weight: normal;
    color: rgba($color: #161823, $alpha: 0.75);
  }
  &__confirm {
    color: #161823;
    font-weight: bold;
  }
}
