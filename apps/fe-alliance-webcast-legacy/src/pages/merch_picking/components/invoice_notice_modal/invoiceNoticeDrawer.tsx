import React, { useMemo, useState, useEffect } from 'react';
import LazyImg from 'components/lazy_img';
import IMAGE_GOODS from '@src/pages/merch_picking/components/add_result_modal/img/img_default_small.png';
import { Dialog } from '@ecom/auxo-mobile';
import { CommissionTicketTipType, Merch, NoticeItem } from '@src/pages/merch_picking/types';
import { createPortal, render, unmountComponentAtNode } from 'react-dom';
import InvoiceNoticeDetail from '@src/pages/merch_picking/components/invoice_notice_modal/invoiceNoticeDetail';
import Checkbox from 'components/checkbox';
import './index.scss';
import { hookDialog } from "@alliance-mobile/event";
hookDialog(Dialog);
interface IProps {
  isOpen?: boolean;
  noticeItems: NoticeItem[];
  closeModal?: () => void;
  confirmModal?: () => void;
}

const MAX_PIC_COUNT = 4;
// 渲染弹窗节点
let div_wrap: HTMLDivElement | null = null;

export const computeNeedInvoiceShow = () => {
  const lastNoticeShowTime = Number(localStorage.getItem('invoiceNoticeModalShowTime') || 0);
  const nowTime = new Date().valueOf();
  return nowTime - lastNoticeShowTime > 7 * 24 * 60 * 60 * 1000;
};

const InvoiceNoticeDrawer: React.FC<IProps> = props => {
  const { noticeItems, isOpen = false, closeModal, confirmModal } = props;
  const [localVisible, setLocalVisible] = useState(false);
  const [checked, setChecked] = useState(false);
  const [showDetail, setShowDetail] = useState(false);

  useEffect(() => {
    setLocalVisible(isOpen);
  }, [isOpen]);

  const handleShowTime = () => {
    if (checked) {
      localStorage.setItem('invoiceNoticeModalShowTime', new Date().valueOf().toString());
    }
  };

  const body = useMemo(() => {
    return (
      <>
        以下{noticeItems.length}
        个商品的商家已开启「需要佣金发票」，设置在生效中或即将生效；你推广该商家的商品10%佣金将被冻结，至你为商家开具相应发票后才可提现。
        <div
          className="flex-container"
          onClick={() => {
            setLocalVisible(false);
            setShowDetail(true);
          }}
        >
          {noticeItems.map((item, index) => {
            if (index > MAX_PIC_COUNT - 1) {
              return null;
            }
            return (
              <LazyImg
                lazy={false}
                src={item.main_img || ''}
                key={item.product_id}
                className="invoice-goods"
                failure={IMAGE_GOODS}
              />
            );
          })}
          {noticeItems.length > MAX_PIC_COUNT && (
            <span className="invoice-more">+{noticeItems.length - MAX_PIC_COUNT}</span>
          )}
          <svg
            style={{ marginLeft: '8px' }}
            width="7"
            height="10"
            viewBox="0 0 7 10"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M4.97496 4.99999L0.842279 1.24301C0.663491 1.08048 0.650315 0.803781 0.812849 0.624993C0.975383 0.446206 1.25208 0.43303 1.43087 0.595564L5.86877 4.63002C6.08699 4.8284 6.08699 5.17158 5.86877 5.36996L1.43087 9.40442C1.25208 9.56696 0.975383 9.55378 0.812849 9.37499C0.650315 9.19621 0.663491 8.91951 0.842278 8.75698L4.97496 4.99999Z"
              fill="#FE2C55"
            />
          </svg>
        </div>
        <div className="flex-container">
          <Checkbox
            label="七天内不再提示"
            isChecked={checked}
            onChange={value => {
              setChecked(value);
            }}
          />
        </div>
      </>
    );
  }, [checked]);

  const domEl = document.body;
  if (!domEl) {
    return null;
  }

  const handleCancel = () => {
    handleShowTime();
    closeModal?.();
    setLocalVisible(false);
  };

  const handleConfirm = () => {
    setLocalVisible(false);
    handleShowTime();
    confirmModal?.();
  };

  return createPortal(
    <>
      <Dialog
        title="添加结果提示"
        show={localVisible}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
        okText="确认添加"
        cancelText="暂不推广"
        renderBody={body}
      />
      <InvoiceNoticeDetail
        visible={showDetail}
        noticeItems={noticeItems}
        onClose={() => {
          setShowDetail(false);
          setLocalVisible(true);
        }}
      />
    </>,
    domEl
  );
};

export const InvoiceDrawerShow = (props: IProps) => {
  // 判断节点是否为空，如果不为空，先卸载掉之前的弹窗再重新创建节点，避免之前的状态残留
  if (div_wrap !== null) {
    unmountComponentAtNode(div_wrap);
    div_wrap && document.body.removeChild(div_wrap);
  }
  // 创建新的节点并添加到body
  div_wrap = document.createElement('div');
  document.body.appendChild(div_wrap);
  render(<InvoiceNoticeDrawer isOpen={true} {...props} />, div_wrap);
};

export const makeNoticeItems = (merchList: Merch[]) => {
  return merchList
    .filter(item => {
      // 首先过滤出所有发票生效中和待生效的商品
      const type = item?.commission_ticket_tip_info?.type;
      return CommissionTicketTipType.ShopTicketPending === type || CommissionTicketTipType.FreezeCommission === type;
    })
    .map(item => {
      const { title, product_id, cover: main_img, commission_ticket_tip_info } = item;
      let bind_msg: string | undefined;
      bind_msg =
        commission_ticket_tip_info?.type === CommissionTicketTipType.ShopTicketPending ?
          '商家已开启「需要佣金发票」，待生效' :
          '商家已开启「需要佣金发票」，生效中';
      return { title, product_id, main_img, bind_msg };
    });
};

export default InvoiceNoticeDrawer;
