import React, { ReactNode } from 'react';
import { NoticeItem } from '@src/pages/merch_picking/types';
import LazyImg from 'components/lazy_img';
import IMAGE_GOODS from '@src/pages/merch_picking/components/add_result_modal/img/img_default_large.png';
import { Sheets } from '@ecom/auxo-mobile';

interface IProps {
  visible: boolean;
  noticeItems: NoticeItem[];
  onClose: () => void;
}

const Card = (cardProps: { children?: ReactNode; img: string; title: string; id: string }) => {
  const { children, img, title, id } = cardProps;
  return (
    <div className="detail-card">
      <div className="detail-card-main">
        <LazyImg lazy={false} src={img} className="detail-card-main__goods" failure={IMAGE_GOODS} />
        <div>
          <div className="detail-card-main__title">{title}</div>
          <div className="detail-card-main__id">ID:{id}</div>
        </div>
      </div>
      {children}
    </div>
  );
};

const InvoiceNoticeDetail: React.FC<IProps> = props => {
  const { visible, onClose, noticeItems } = props;
  return (
    <div className="result-detail__container">
      <Sheets visible={visible} height={'auto'} title="添加商品详情" onClose={onClose} onDismiss={onClose}>
        <div className="result-detail">
          {noticeItems.map(item => (
            <Card title={item.title} id={item.product_id} img={item.main_img || ''}>
              <div className="detail-card-reason">
                <div className="detail-card-reason__main">
                  <span className="detail-card-reason__title">提醒:</span>
                  <span className="detail-card-reason__detail">{item?.bind_msg}</span>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </Sheets>
    </div>
  );
};

export default InvoiceNoticeDetail;
