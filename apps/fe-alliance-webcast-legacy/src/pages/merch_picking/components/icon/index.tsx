/*
 * @Author: <EMAIL>
 * @Date: 2023-07-13 11:09:56
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-07-13 11:25:48
 * @Description: 提示条新规范的图标
 */
import React, { FC } from 'react';

export const AlertInfoIcon: FC<{ color?: string }> = props => {
  const { color = '#FF1C49' } = props;

  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <path
        d="M14.6663 8.00004C14.6663 11.6819 11.6816 14.6667 7.99967 14.6667C4.31778 14.6667 1.33301 11.6819 1.33301 8.00004C1.33301 4.31814 4.31778 1.33337 7.99967 1.33337C11.6816 1.33337 14.6663 4.31814 14.6663 8.00004ZM7.99967 4.66671C7.63306 4.66671 7.33736 4.96673 7.34268 5.3333L7.39203 8.73448C7.39685 9.06664 7.66748 9.33337 7.99967 9.33337C8.33187 9.33337 8.6025 9.06664 8.60732 8.73448L8.65667 5.3333C8.66199 4.96673 8.36629 4.66671 7.99967 4.66671ZM8.73302 10.7334C8.73302 10.3284 8.4047 10 7.99969 10C7.59468 10 7.26636 10.3284 7.26636 10.7334C7.26636 11.1384 7.59468 11.4667 7.99969 11.4667C8.4047 11.4667 8.73302 11.1384 8.73302 10.7334Z"
        fill={color}
      />
    </svg>
  );
};

export const AlertCloseIcon: FC<{ color?: string; fillOpacity?: string }> = props => {
  const { color = '#FF6F00', fillOpacity = '0.8' } = props;

  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
      <path
        d="M10.0659 3.06564C10.3783 2.75322 10.3783 2.24669 10.0659 1.93427C9.75346 1.62185 9.24693 1.62185 8.93451 1.93427L6.0002 4.86858L3.06588 1.93427C2.75346 1.62185 2.24693 1.62185 1.93451 1.93427C1.62209 2.24669 1.62209 2.75322 1.93451 3.06564L4.86882 5.99995L1.93451 8.93427C1.62209 9.24669 1.62209 9.75322 1.93451 10.0656C2.24693 10.3781 2.75346 10.3781 3.06588 10.0656L6.0002 7.13132L8.93451 10.0656C9.24693 10.3781 9.75346 10.3781 10.0659 10.0656C10.3783 9.75322 10.3783 9.24669 10.0659 8.93427L7.13157 5.99995L10.0659 3.06564Z"
        fill={color}
        fillOpacity={fillOpacity}
      />
    </svg>
  );
};

export const AlterGoIcon = (props: { color?: string; fillOpacity?: string }) => {
  const { color = '#FF6F00', fillOpacity = '0.8' } = props;
  return (
    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_734_5390)">
        <path
          d="M4.21967 1.21967C4.51256 0.926777 4.98744 0.926777 5.28033 1.21967L9.53033 5.46967C9.82322 5.76256 9.82322 6.23744 9.53033 6.53033L5.28033 10.7803C4.98744 11.0732 4.51256 11.0732 4.21967 10.7803C3.92678 10.4874 3.92678 10.0126 4.21967 9.71967L7.93934 6L4.21967 2.28033C3.92678 1.98744 3.92678 1.51256 4.21967 1.21967Z"
          fill={color}
          fillOpacity={fillOpacity}
        />
      </g>
    </svg>
  );
};
