import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Checkbox } from '@ecom/auxo-mobile';
import { isIOS } from '@byted-flash/utils';
import Tooltip from 'rc-tooltip';
import 'rc-tooltip/assets/bootstrap.css';
import debounce from 'lodash/debounce';
import { globalProps } from '@src/lib/env_detect';
import { px2vw } from '@alliance-mobile/utils/index';
import { Merch } from '../../types';

import './index.scss';
import { TAB_KEYS } from '../../constants';
import showToast from 'components/toast/ecom_toast';
import { myCoreLinkClient } from '@src/lib/util/mera/core-link';

const SHOWN_SELECTALL_TOOLTIP = 'SHOWN_SELECTALL_TOOLTIP';

interface Props {
  /** 剩余最大可添品数 */
  maxSelectCount: number;
  /** 当前已选列表 */
  selectedList: Merch[];
  /** 是否全选 */
  selectAll: boolean;
  /** 全选框上面的提示条 */
  hintNode?: React.ReactNode;
  isMerchV2?: boolean; // 版本标识
  listData?: Merch[]; // 列表数据
  autoSelectAll?: boolean; // 是否默认全选
  onBatchSelectAllChange(): void;
  onBatchAdd(selectAll: boolean): void;
  changeAutoSelectAll?(flag: boolean): void;
}

export default React.memo((props: Props) => {
  const {
    maxSelectCount,
    selectedList,
    selectAll,
    hintNode,
    onBatchSelectAllChange,
    onBatchAdd,
    isMerchV2,
    listData,
    autoSelectAll = false,
    changeAutoSelectAll,
  } = props || {};

  const selectedListRef = useRef(selectedList);
  selectedListRef.current = selectedList;

  // 显示全选提示tooltip
  const [showSelectAllTooltip, setShowSelectAllTooltip] = useState(false);
  // 全选置灰
  const disableSelectAll = useMemo(
    () => selectedList?.length >= maxSelectCount && !selectAll,
    [selectedList?.length, maxSelectCount, selectAll]
  );
  // 如果没有商品，全选也置灰
  const disableSelectAllV2 = useMemo(
    () => (selectedList?.length >= maxSelectCount && !selectAll) || !listData?.length,
    [selectedList?.length, maxSelectCount, selectAll, listData?.length]
  );

  const disableSelectAllFinal = isMerchV2 ? disableSelectAllV2 : disableSelectAll;

  const checkSelectAllTooltip = useCallback(() => {
    if (localStorage.getItem(SHOWN_SELECTALL_TOOLTIP) === '1') {
      return;
    }
    setShowSelectAllTooltip(true);
    localStorage.setItem(SHOWN_SELECTALL_TOOLTIP, '1');
    setTimeout(() => {
      setShowSelectAllTooltip(false);
    }, 3000);
  }, []);

  const onCheckboxChange = () => {
    if (disableSelectAllFinal) {
      return;
    }
    checkSelectAllTooltip();
    onBatchSelectAllChange();
  };

  // 切换到推荐商品tab触发默认全选的case（点击底部推荐商品浮层 or 直播准备页->推荐商品 进入）
  useEffect(() => {
    if (autoSelectAll && listData?.length) {
      const preSelectedLength = selectedListRef.current.length;
      !selectAll && onBatchSelectAllChange();
      changeAutoSelectAll?.(false);
      !selectAll &&
        setTimeout(() => {
          const outerLength = selectedListRef.current.length;
          showToast(`为你选中${outerLength - preSelectedLength}个推荐商品`);
        }, 100);
    }
  }, [autoSelectAll, changeAutoSelectAll, listData?.length, onBatchSelectAllChange, selectAll]);

  const handleBatchAdd =
    onBatchAdd &&
    debounce((...params) => {
      onBatchAdd(...params);
      myCoreLinkClient.sendCoreLinkEvent('live_add_promotion_confirm_btn_click');
    }, 500);
  // 此项目未设置viewport-fit=cover，因此css里设置的padding-bottom安全距离没生效
  // viewport-fit=cover的话担心其他页面展示有问题，但又因为发现的比较晚，没有测试同学可以额外支持回归其他页面
  // 就先使用容器传来的安全距离设置一下（对ios额外定制的样式）
  const iosStyles = {
    paddingBottom: px2vw(16 + globalProps?.safeArea?.marginBottom - 5), // 多减去5是因为ux同学感觉加上安全距离后 视觉距离过大 因此减去一些
  };
  return (
    <Tooltip
      visible={showSelectAllTooltip}
      placement="topLeft"
      overlayClassName="select-all-toggle"
      overlay={<span>试试向上滑动选择更多</span>}>
      <>
        {hintNode}
        <div className={`select-all ${isMerchV2 ? 'select-all-v2' : ''}`} style={isIOS ? iosStyles : {}}>
          <div className="select-all-checkbox" onClick={onCheckboxChange}>
            <Checkbox type="round" checked={selectAll} disable={disableSelectAllFinal} />
            <span className="select-all-checkbox__text">全选</span>
          </div>
          <div className="select-all-btn">
            <div className="select-all-btn__text">
              已选
              <span className="select-all-btn__text-highlight">{selectedList?.length || 0}</span>/{maxSelectCount}
            </div>
            {isMerchV2 ? (
              <Button
                className="select-all-btn__add"
                text="添加"
                disabled={!selectedList?.length}
                onClick={() => handleBatchAdd(selectAll)}
              />
            ) : (
              <Button text="确认添加" disabled={!selectedList?.length} onClick={() => handleBatchAdd(selectAll)} />
            )}
          </div>
        </div>
      </>
    </Tooltip>
  );
});
