import React, { useEffect, useState, useCallback, useRef } from 'react';
import LoadingMask from 'components/loading/mask';
import ModalConfirm from '../confirm';
import { isInAweme, queryParamsFromGlobal } from '@src/lib/env_detect';
import via from '@ies/webcast-via';
import { fetchKolRiskVerify } from '../../services/api';
import { KolRiskVerifyResponse, RiskVerifyStatusEnum } from '../../types';

interface IProps {
  isShow: boolean;
  //   onConfirm: () => void;
}
interface IState {
  isFetchSuccess: boolean;
  isFaceRecognizeModalShow: boolean;
  isFetchErrorModalShow: boolean;
  errorMessage: string;
}
export default React.memo((props: IProps) => {
  const [state, setState] = useState<IState>({
    isFetchSuccess: false,
    isFaceRecognizeModalShow: false,
    isFetchErrorModalShow: false,
    errorMessage: '',
  });

  const { isShow } = props;

  const isGotoFaceRecognize = useRef(false);

  useEffect(() => {
    if (isInAweme && queryParamsFromGlobal.enter_from !== 'webcast_living') {
      getShopRiskVerify();
    }
  }, []);

  useEffect(() => {
    document.addEventListener('visibilitychange', function () {
      if (document.visibilityState === 'visible') {
        if (!isGotoFaceRecognize.current) {
          return;
        }
        isGotoFaceRecognize.current = false;
        setState(pre => ({ ...pre, isFetchSuccess: false }));
        window.setTimeout(() => {
          getShopRiskVerify();
        }, 300);
      }
    });
    return () => {
      document.removeEventListener('visibilitychange', () => {});
    };
  }, []);

  const getShopRiskVerify = useCallback(async () => {
    try {
      const res = await fetchKolRiskVerify();
      const { data: { risk_verify_status: riskVerifyStatus = undefined } = {} } = res;
      if (riskVerifyStatus !== RiskVerifyStatusEnum.Pass) {
        throw res;
      }
      setState({
        ...state,
        isFaceRecognizeModalShow: false,
        isFetchSuccess: true,
        isFetchErrorModalShow: false,
      });
    } catch (err) {
      const {
        data: { risk_verify_status: riskVerifyStatus = undefined, risk_verify_msg: riskVerifyMsg = undefined } = {},
        msg,
      } = (err as KolRiskVerifyResponse) ?? {};

      const showFaceRecognizeModal = riskVerifyStatus === RiskVerifyStatusEnum.NeedFaceIdentification;
      setState({
        ...state,
        isFetchSuccess: true,
        isFaceRecognizeModalShow: showFaceRecognizeModal,
        isFetchErrorModalShow: !showFaceRecognizeModal,
        errorMessage: riskVerifyMsg || msg || '账号存在风险，暂不支持直播添品',
      });
    }
  }, []);

  const onConfirmRecognize = useCallback(() => {
    setState({
      ...state,
      isFetchSuccess: true,
      isFaceRecognizeModalShow: false,
    });
    isGotoFaceRecognize.current = true;
    via.app.openWebview({
      url: 'https://webcast.amemv.com/falcon/webcast_douyin/page/certification/index.html?enter_from=e_commerce_select&hide_nav_bar=1&hide_status_bar=0&__live_platform__=webcast&status_bar_color=black&status_bar_bg_color=%23ffffff&web_bg_color=%23ffffff',
      title: '实名认证',
    });
  }, []);

  const cancelModalAndClosePage = useCallback(() => {
    setState({
      ...state,
      isFaceRecognizeModalShow: false,
      isFetchErrorModalShow: false,
    });
    via.app.close();
  }, []);

  if (!isInAweme || (isInAweme && queryParamsFromGlobal.enter_from === 'webcast_living')) {
    return null;
  } else {
    return (
      <>
        <LoadingMask isOpen={!state.isFetchSuccess} />;
        <ModalConfirm
          isOpen={isShow && state.isFetchErrorModalShow}
          content={state.errorMessage || '网络错误，请重试'}
          onConfirm={cancelModalAndClosePage}
          confirmText="确认"
        />
        <ModalConfirm
          isOpen={isShow && state.isFaceRecognizeModalShow}
          content="为了帐号安全，需进行人脸识别，通过后才可进行商品分享操作"
          onConfirm={onConfirmRecognize}
          onCancel={cancelModalAndClosePage}
          confirmText="去认证"
          cancelText="取消"
        />
      </>
    );
  }
});
