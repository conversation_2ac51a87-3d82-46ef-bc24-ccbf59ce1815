.aggregatePromotionModal {
  height: 100%;
  position: relative;
  background-color: #fff;
  padding-top: 8px;

  &.content {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: stretch;

    :global {

      .search-bar__main {
        border-radius: 8px;
        padding-left: 36px;
        background-repeat: no-repeat;
        background-size: 20px 20px;
        background-position: 8px center;
        background-image: url(~static/images/merch_picking/icon_search2.svg);
      }

      .search-bar__input {
        width: 0;
      }
    }
    
    .contentTop {
      margin: 8px 12px;
      font-size: 12px;
      line-height: 18px;
      color: rgba(22, 24, 35, .5);
    }

    .contentMain {
      position: relative;
      flex-grow: 1;
      overflow-y: auto;
      padding-bottom: 64px;

      &::-webkit-scrollbar {
        display: none;
      }

      .card {
        position: relative;

        :global {

          .inner-kol-product-card-tags {
            margin-top: 2px;
          }

          // .title.note-color {
          //   color: rgba(22, 24, 35, .5);
          // }
        }
      }

    
    }

  }

  .empty {
    position: relative;
    display: flex;
    margin-top: 30vh;
    flex-direction: column;
    align-items: center;

    .emptyImg {
      width: 80px;
      height: 80px;
    }

    .emptyTitle {
      margin-top: 16px;
      font-size: 17px;
      font-weight: 500;
      color: #161823;
    }

    .emptySubTitle {
      margin-top: 6px;
      font-size: 14px;
      color: rgba(22, 24, 35, .6);
    }
  }
}
