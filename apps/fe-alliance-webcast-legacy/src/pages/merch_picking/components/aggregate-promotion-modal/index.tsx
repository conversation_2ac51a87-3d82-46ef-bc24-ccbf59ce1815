import React, { useEffect, useMemo, useState } from 'react';
import styles from './index.module.scss';
import { usePersistCallback } from '@byted/hooks';
import Modal from 'components/modal';
import SearchBar from '../../components/search_bar';
import { IAggregatePromotionModalState } from '../../types';
import { IMixController } from '../../modules/controller';
import { px2vw } from '@alliance-mobile/utils/index';
import cs from 'classnames';
import {
  EKolProductCardType,
  KolProductCard,
  EImgCoverSize,
  EClipPosition,
  selectionSquare,
} from '@alliance-mobile/platform-merchant-common/src/components/kol-summary-card';
import { Loading, Empty } from '@ecom/auxo-mobile';
import { KolProductInfo } from '../../services/apis/type';
import { AggrProductList } from '../../services/apis/aggr_product_list';
import toast from '@src/components/toast/ecom_toast';
const { SelectionSquareDataPart, ESelectionSquareDataRenderKey, CardShop } = selectionSquare;

interface IProps {
  aggregatePromotionModalState: IAggregatePromotionModalState | undefined;
  closeAggrPromotion: IMixController['closeAggrPromotion'];
}

const AggregatePromotionModal = (props: IProps) => {
  const { aggregatePromotionModalState, closeAggrPromotion } = props;
  const { data, options } = aggregatePromotionModalState || {};
  const { getTipTitle = () => '' } = options || {};
  const [searchText, setSearchText] = useState('');
  const [promotionList, setPromotionList] = useState<KolProductInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [emptyText, setEmptyText] = useState<{ title?: string; subTitle?: string }>();
  const modalVisible = useMemo(() => {
    return Boolean(data?.product_id);
  }, [data?.product_id]);

  const fetchAggregatePromotions = usePersistCallback(async () => {
    setLoading(true);
    setEmptyText(undefined);
    try {
      const res = await AggrProductList({
        aggr_id: data?.product_id || '',
        search_text: searchText,
      });
      const list = res?.data?.aggr_product || [];
      setPromotionList(list);
      if (list.length === 0) {
        setEmptyText({ title: '搜索结果为空', subTitle: '暂无匹配商品' });
      }
    } catch (e) {
      if (e.status_msg) {
        setEmptyText({ title: e.status_msg });
      }
    } finally {
      setLoading(false);
    }
  });
  const onClose = usePersistCallback(() => {
    setSearchText('');
    setPromotionList([]);
    setLoading(false);
    setEmptyText(undefined);
  });
  useEffect(() => {
    if (modalVisible) {
      fetchAggregatePromotions();
    } else {
      onClose();
    }
  }, [modalVisible]);

  const renderContent = useMemo(() => {
    if (loading) {
      return <> {loading && <Loading />}</>;
    }
    if (emptyText) {
      return (
        <div className={styles.empty}>
          {/* eslint-disable-next-line @ecom/ecom-rule/avoid-img */}
          <img
            className={styles.emptyImg}
            src={'https://lf3-static.bytednsdoc.com/obj/eden-cn/lm-ljuhpssh/ljhwZthlaukjlkulzlp/empty/noSearch.svg'}
          />
          <div className={styles.emptyTitle}>{emptyText?.title || '搜索结果为空'}</div>
          <div className={styles.emptySubTitle}>{emptyText?.subTitle}</div>
        </div>
      );
    }
    return (
      <>
        <div className={styles.contentTop}>{getTipTitle({ length: promotionList.length })}</div>
        <div className={styles.contentMain}>
          {promotionList.map(promotion => {
            return (
              <div
                key={promotion.promotion_id}
                style={{
                  margin: `${px2vw(0)} ${px2vw(4)}`,
                }}>
                <KolProductCard
                  customClassName={styles.card}
                  kolProductInfo={promotion}
                  cardType={EKolProductCardType.BasicA}
                  coverConfig={{
                    imgSize: EImgCoverSize.L108,
                  }}
                  titleConfig={{
                    tagsLimit: 1,
                    linesLimit: 1,
                  }}
                  clipOptions={[
                    {
                      position: EClipPosition.dataNode,
                      node: (
                        <SelectionSquareDataPart
                          dataPartConfig={{
                            defaultHighlight: true,
                          }}
                          kolProductInfo={promotion}
                          keyRenderList={[
                            {
                              key: ESelectionSquareDataRenderKey.price,
                            },
                            {
                              key: ESelectionSquareDataRenderKey.cos,
                            },
                            {
                              key: ESelectionSquareDataRenderKey.sales,
                            },
                          ]}
                        />
                      ),
                    },
                    {
                      position: EClipPosition.bottomLine,
                      node: promotion.base_model?.shop_info && (
                        <CardShop shopInfo={promotion.base_model?.shop_info} arrowIcon={<></>} />
                      ),
                    },
                  ]}
                />
              </div>
            );
          })}
        </div>
      </>
    );
  }, [getTipTitle, loading, promotionList, emptyText]);

  return (
    <>
      <Modal
        style={{
          overlay: {
            backgroundColor: '#fff',
          },
        }}
        isOpen={modalVisible}
        className={cs(styles.aggregatePromotionModal, styles.content)}>
        <SearchBar
          value={searchText}
          setValue={setSearchText}
          onConfirm={fetchAggregatePromotions}
          placeholder={`请输入店铺或商品ID`}
          onNavBack={closeAggrPromotion}
          submitText="搜索"
          preInputNode={
            <div
              style={{
                fontSize: px2vw(18),
                fontWeight: '600',
                color: '#161823',
                marginRight: px2vw(8),
              }}>
              关联商品
            </div>
          }
        />
        {renderContent}
      </Modal>
    </>
  );
};
export default AggregatePromotionModal;
