.topAlert {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  font-size: 13px;
  line-height: 18px;
  color: #ff1c49;
  background: linear-gradient(0deg, rgba(254, 44, 85, .06), rgba(254, 44, 85, .06)), #fff;
  margin-bottom: 4px;

  .icon {
    flex-shrink: 0;
    width: 16px;
    height: 16px;
  }

  .content {
    display: inline-flex;
    align-items: center;
    margin-left: 8px;

    .button {
      padding: 0 8px;
      flex-shrink: 0;
      font-weight: 500;
      font-size: 12px;
      line-height: 16px;
      text-align: center;
      color: #ff1c49;
    }
  }

  .close {
    flex-shrink: 0;
    width: 12px;
    height: 12px;
  }
}

.panel {
  --safe-height: constant(safe-area-inset-bottom);
  --safe-height: env(safe-area-inset-bottom);
  padding: 0;
  padding-bottom: var(--safe-height);

  .header {
    width: 100%;
    height: 188px;
    background-image: url(./images/img_sheets.png);
    background-size: cover;
    border-radius: 8px 8px 0 0;
  }

  .title {
    margin-top: 24px;
    padding: 0 24px;
    font-weight: 500;
    font-size: 17px;
    line-height: 24px;
    text-align: center;
    color: #161823;
  }

  .content {
    margin-top: 8px;
    padding: 0 24px;
    font-size: 14px;
    line-height: 20px;
    text-align: center;
    color: rgba(22, 24, 35, .75);
  }

  .buttons {
    display: flex;
    margin-top: 24px;
    padding: 0 16px;
    justify-content: space-between;

    .button {
      flex: 1;
      min-width: unset;
      font-weight: 500;
      border-radius: 8px;

      &:not(:first-child) {
        margin-left: 8px;
      }

      &.cancel {
        background: rgba(22, 24, 35, .05);
        border: none;
      }
    }
  }
}
