/**
 * @description 多步通知
 */

import React, { useEffect, useState } from 'react';
import styles from './index.module.scss';
import { usePersistCallback } from '@byted/hooks';
import cx from 'classnames';
import { Button, Sheets } from '@ecom/auxo-mobile';
import { app } from '@bridge/webcast';
import { genSchema } from '@src/lib/util/merch_picking';
import { PublishContinuePushStream } from '@src/lib/util/event';
import {
  EventType,
  ProductMigrateSource,
  Button as ButtonInfo,
  productMigrate,
  NoticeScene,
  getNotice,
  NoticeType,
} from '../../services/api';
// import { AlertInfoIcon, AlertCloseIcon } from '../icon';
import { MERCH_PICKING_CART_MOVE_MODAL } from '../../constants';

interface AlertInfo {
  title: string;
  buttonInfo: ButtonInfo;
}

interface SheetsInfo {
  title: string;
  content: string;
  picUrl?: string;
  buttons?: ButtonInfo[];
}

interface Props {
  scene: NoticeScene;
}

export default (props: Props) => {
  const { scene } = props;

  // const [showAlert, setShowAlert] = useState(false);
  // const [alertInfo, setAlertInfo] = useState<AlertInfo>({} as AlertInfo);
  const [showSheets, setShowSheets] = useState(false);
  const [sheetsInfo, setSheetsInfo] = useState<SheetsInfo>({} as SheetsInfo);

  // const handleCloseAlert = usePersistCallback(() => setShowAlert(false));

  const handleClickNext = usePersistCallback((info?: ButtonInfo) => {
    const { content, sub_content, pic_url } = info?.event || {};
    setSheetsInfo({
      title: content || '',
      content: sub_content || '',
      picUrl: pic_url || '',
      buttons: [
        { button_text: '无需同步', event_type: 0 },
        { button_text: '立即同步', event_type: 1 },
      ],
    });
    localStorage.setItem('MERCH_PICKING_CART_MOVE_MODAL', MERCH_PICKING_CART_MOVE_MODAL);
    setShowSheets(true);
  });

  const handleClickSheetsButton = (type: EventType) => {
    if (type === EventType.None) {
      setShowSheets(false);
      return;
    }
    productMigrate({ source: ProductMigrateSource.Cupboard })
      .then(res => {
        if (res.data?.is_synced) {
          setShowSheets(false);
          PublishContinuePushStream();
          app.openScheme({
            url: genSchema({
              url: 'https://lf-ecom-gr-sourcecdn.bytegecko.com/obj/byte-gurd-source-gr/merchPicking/gecko/h5Resource/alliance_merch_picking_h5/cn/html/merch-picking/index.html?_pia_=1&tab=cart&migrate=已成功将橱窗商品迁移至选品车',
              isWebCast: true,
              hide_nav_bar: 1,
              should_full_screen: 1,
              trans_status_bar: 1,
              status_bar_color: 'black',
              hide_loading: 1,
              web_bg_color: 'ffffff',
              loader_name: 'forest',
              disable_thread_spread: 1,
              disable_host_jsb_method: 1,
            }),
          });
          return;
        }
        app.toast({ text: '系统错误，请重试', type: 'prompt' });
      })
      .catch(error => app.toast({ text: error?.msg || '系统错误，请重试', type: 'prompt' }));
  };

  useEffect(() => {
    if (!scene || localStorage.getItem('MERCH_PICKING_CART_MOVE_MODAL')) return;
    getNotice({ scene }).then(res => {
      const { notice = [] } = res.data || {};
      for (let i = 0; i < notice.length; i++) {
        const noticeInfo = notice?.[i] || {};
        if (noticeInfo.notice_type === NoticeType.StickyTop) {
          handleClickNext(noticeInfo.content?.buttons?.[0]);
          //   setAlertInfo({
          //     title: noticeInfo.content?.text || '',
          //     buttonInfo: noticeInfo.content?.buttons?.[0] || {},
          //   });
          //   setShowAlert(true);
          //   return;
        }
        // if (noticeInfo.notice_type === NoticeType.StickyBottom) {
        //   setSheetsInfo({
        //     title: noticeInfo.content?.text || '',
        //     content: noticeInfo.content?.sub_text || '',
        //     buttons: noticeInfo.content?.buttons || [],
        //     picUrl: noticeInfo.content?.pic_url || '',
        //   });
        //   setShowSheets(true);
        // }
      }
    });
  }, [scene, handleClickNext]);

  return (
    <>
      {/* {showAlert && (
        <div className={styles.topAlert}>
          <div className={styles.icon}>
            <AlertInfoIcon />
          </div>
          <div className={styles.content}>
            {alertInfo.title}
            <span className={styles.button} onClick={handleClickNext}>
              {alertInfo.buttonInfo.button_text || ''}
            </span>
          </div>
          <div className={styles.close} onClick={handleCloseAlert}>
            <AlertCloseIcon color="#FF1C49" />
          </div>
        </div>
      )} */}
      <Sheets
        visible={showSheets}
        className={styles.panel}
        height={411}
        onDismiss={() => setShowSheets(false)}
        onClose={() => setShowSheets(false)}>
        <div
          className={styles.header}
          style={sheetsInfo.picUrl ? { backgroundImage: `url(${sheetsInfo.picUrl})` } : {}}
        />
        <div className={styles.title}>{sheetsInfo.title}</div>
        <div className={styles.content}>{sheetsInfo.content}</div>
        <div className={styles.buttons}>
          {(sheetsInfo.buttons || [])?.length > 0 ? (
            (sheetsInfo.buttons || []).map(item => {
              const isCancel = item.event_type === EventType.None;
              return (
                <Button
                  key={item.button_text}
                  className={cx(styles.button, { [styles.cancel]: isCancel })}
                  type={isCancel ? 'dashed' : 'primary'}
                  size="large"
                  text={item.button_text}
                  onClick={() => handleClickSheetsButton(item.event_type || EventType.None)}
                />
              );
            })
          ) : (
            <Button
              className={cx(styles.button, styles.cancel)}
              type="dashed"
              size="large"
              text="我知道了"
              onClick={() => setShowSheets(false)}
            />
          )}
        </div>
      </Sheets>
    </>
  );
};
