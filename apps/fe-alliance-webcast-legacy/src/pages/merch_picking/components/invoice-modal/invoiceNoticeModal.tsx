import React, { useEffect, useMemo, useState } from 'react';
import Modal from 'components/modal';
import { createPortal, render, unmountComponentAtNode } from 'react-dom';
import dayjs from 'dayjs';

import Confirm from 'components/modal/confirm';
import Checkbox from 'components/checkbox';
import './index.scss';

export enum CommissionTicketTipType {
  Normal = 1, // 正常添品
  ShopTicketPending = 2, // 商家发票逻辑待生效，提醒
  FreezeCommission = 3, // 带货冻结佣金提醒
}

interface IProps {
  isOpen?: boolean;
  type?: CommissionTicketTipType;
  effecte_time?: string;
  freeze_ratio?: number;
  closeModal?: () => void;
  confirmModal?: () => void;
}

// 渲染弹窗节点
let div_wrap: HTMLDivElement | null = null;

export const computeNeedInvoiceShow = () => {
  const lastNoticeShowTime = Number(localStorage.getItem('invoiceNoticeModalShowTime') || 0);
  const nowTime = new Date().valueOf();
  return nowTime - lastNoticeShowTime > 7 * 24 * 60 * 60 * 1000;
};

const InvoiceNoticeModal: React.FC<IProps> = props => {
  const { closeModal, confirmModal, isOpen = false, type, effecte_time, freeze_ratio } = props;
  const [localVisible, setLocalVisible] = useState(false);
  const [checked, setChecked] = useState(false);

  useEffect(() => {
    setLocalVisible(isOpen);
  }, [isOpen]);

  const handleShowTime = () => {
    if (checked) {
      localStorage.setItem('invoiceNoticeModalShowTime', new Date().valueOf().toString());
    }
  };

  const content = useMemo(() => {
    let body;
    const checkPanel = (
      <div style={{ display: 'flex', justifyContent: 'center' }}>
        <Checkbox
          label="七天内不再提示"
          isChecked={checked}
          onChange={value => {
            setChecked(value);
          }}
        />
      </div>
    );
    if (type === CommissionTicketTipType.ShopTicketPending) {
      const timeStr = dayjs(effecte_time).format('YYYY年MM月DD日');
      body = (
        <div style={{ padding: '0 10px', boxSizing: 'border-box' }}>
          <div>
            商家已开启「需要佣金发票」，该设置将于{timeStr}
            0点生效，生效后你推广该商家商品订单的{freeze_ratio}%佣金将被冻结，至你为商家开具发票后方可提现。
          </div>
          {checkPanel}
        </div>
      );
    } else {
      body = (
        <div style={{ padding: '0 10px', boxSizing: 'border-box' }}>
          <div>
            商家已开启「需要佣金发票」，你推广该商家的商品{freeze_ratio}
            %佣金将被冻结，至你为商家开具相应发票后才可提现。
          </div>
          {checkPanel}
        </div>
      );
    }
    return body;
  }, [checked, effecte_time, freeze_ratio, type]);

  const domEl = document.body;
  if (!domEl) {
    return null;
  }

  return createPortal(
    <Confirm
      isOpen={localVisible}
      body={content}
      title="推广提示"
      cancelText="暂不推广"
      confirmText="确认添加"
      onCancel={() => {
        handleShowTime();
        closeModal?.();
        setLocalVisible(false);
      }}
      onConfirm={() => {
        handleShowTime();
        confirmModal?.();
        setLocalVisible(false);
      }}
    />,
    domEl
  );
};

export const InvoiceModalShow = (props?: IProps) => {
  // 判断节点是否为空，如果不为空，先卸载掉之前的弹窗再重新创建节点，避免之前的状态残留
  if (div_wrap !== null) {
    unmountComponentAtNode(div_wrap);
    div_wrap && document.body.removeChild(div_wrap);
  }
  // 创建新的节点并添加到body
  div_wrap = document.createElement('div');
  document.body.appendChild(div_wrap);
  render(<InvoiceNoticeModal isOpen={true} {...props} />, div_wrap);
};

export default InvoiceNoticeModal;
