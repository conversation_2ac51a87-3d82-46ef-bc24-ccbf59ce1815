// 不写这个的话，Checkbox 的选中态就变成了，被覆盖成了8px*6px
.webcast-checkbox {
  border-radius: 50%;
  background-size: 100% auto;
  background-repeat: no-repeat;
}

.agreementWrapper {
  margin-top: 8px;
  color: rgba(22, 24, 35, 0.5);
  display: flex;
  align-items: center;
}

@keyframes shake {
  0% {
  }

  25% {
    transform: translate(10px, 0px);
  }

  50% {
    transform: translate(5px, 0px);
  }

  75% {
    transform: translate(-10px, 0px);
  }

  100% {
    transform: translate(0px, 0px);
  }
}

.shake-animate-class {
  animation: shake 0.2s linear 0s 3 alternate;
  -webkit-animation: shake 0.2s linear 0s 3 alternate;
}
