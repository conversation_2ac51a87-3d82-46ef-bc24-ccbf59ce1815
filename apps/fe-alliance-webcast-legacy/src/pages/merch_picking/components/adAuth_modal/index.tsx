import React, { <PERSON><PERSON>ventHand<PERSON>, useState, useCallback } from 'react';
import ModalConfirm from '../confirm';
import { genSchema } from '@src/lib/util/merch_picking';
import CheckBox from '../../../../components/webcast-checkbox';
import './index.scss';

import { isInAweme } from '@src/lib/env_detect';
interface Props {
  onCancel?: MouseEventHandler;
  onConfirm: MouseEventHandler;
  isOpen: boolean;
}

function ModalContent({
  isCheckedAgreement,
  setIsCheckedAgreement,
  shake,
}: {
  shake: boolean;
  isCheckedAgreement: boolean;
  setIsCheckedAgreement: React.Dispatch<React.SetStateAction<boolean>>;
}) {
  const handleCheckboxChange = () => {
    setIsCheckedAgreement(status => !status);
  };
  return (
    <div
      style={{
        padding: '20px 20px 32px 20px',
        color: 'rgba(22, 24, 35, 0.75)',
        fontSize: '14px',
        lineHeight: '20px',
        textAlign: 'left',
      }}>
      <div>请设置推广授权，以获取更多流量：</div>
      <div>
        <span style={{ color: '#161823', fontWeight: 500 }}>始终授权</span>
        ：授权其他抖音账号给我的电商直播、短视频及图文进行推广；
      </div>
      <div>
        <span style={{ color: '#161823', fontWeight: 500 }}>每次询问</span>
        ：其他抖音账号须经过我的单独授权同意后才可为我推广；
      </div>
      <div>如不选择，默认”每次询问“</div>
      <div className={shake ? 'agreementWrapper shake-animate-class' : 'agreementWrapper'}>
        <CheckBox isChecked={isCheckedAgreement} onChange={handleCheckboxChange} />
        <div style={{ marginLeft: '8px' }}>
          我已阅读并同意
          <span
            style={{ color: '#09367A', fontWeight: 500 }}
            onClick={() => {
              window.location.href = genSchema({
                url: 'https://lf6-fe.ecombdstatic.com/obj/ies-hotsoon-draft/Data-douecp/Authorization_Agreement.html',
                title: '授权协议',
                use_ui: 1,
                hide_more: 1,
                show_more_button: 1,
                copy_link_action: 0,
              });
            }}>
            《授权协议》
          </span>
        </div>
      </div>
    </div>
  );
}
const SHAKE_DURATION = 600;
export default React.memo((props: Props) => {
  if (!isInAweme) {
    return null;
  }
  const { onConfirm, onCancel, isOpen } = props;
  const [isCheckedAgreement, setIsCheckedAgreement] = useState<boolean>(false);
  const [shake, setShake] = useState(false);
  const handleConfirm = useCallback(
    e => {
      if (!isCheckedAgreement) {
        setShake(true);
        setTimeout(() => {
          setShake(false);
        }, SHAKE_DURATION);
        return;
      }
      onConfirm(e);
    },
    [isCheckedAgreement, onConfirm]
  );
  return (
    <ModalConfirm
      title={'设置推广授权'}
      cancelText={'每次询问'}
      confirmText={'始终授权'}
      onConfirm={handleConfirm}
      onCancel={onCancel}
      isOpen={isOpen}
      styleName={'confirm-live-modal__width'}>
      <ModalContent
        shake={shake}
        isCheckedAgreement={isCheckedAgreement}
        setIsCheckedAgreement={setIsCheckedAgreement}
      />
    </ModalConfirm>
  );
});
