import React from 'react';
import { dataRenderConfig } from '../../types';
import './index.scss';
import cs from 'classnames';
interface DataPartProps {
  config: dataRenderConfig[];
}

export const DataPart = (props: DataPartProps) => {
  const { config } = props;
  if (!config.length) return null;
  return (
    <div className="merch-card-data">
      {config.map(item => {
        const { titleNode, renderKey, dataNode, scale } = item;
        return (
          <div className={cs('data-item', `data-item_${item.renderKey}`)} key={renderKey} style={{ flexGrow: scale }}>
            {titleNode}
            {dataNode}
          </div>
        );
      })}
    </div>
  );
};
