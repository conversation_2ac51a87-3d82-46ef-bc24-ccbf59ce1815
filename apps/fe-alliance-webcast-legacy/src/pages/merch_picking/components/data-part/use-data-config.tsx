/* eslint-disable prefer-const */
import React, { useMemo, ReactNode } from 'react';
import cs from 'classnames';
import { px2vw } from '@alliance-mobile/utils/index';
import { Merch, dataRenderConfig, EDataRenderKey } from '../../types';
import { formatNumber, formatPriceNumber } from '../../services/utils';
import { isLiving } from '@src/lib/util/merch_picking';

interface IDataRenderItem {
  key: EDataRenderKey;
  scale?: number;
  title?: ReactNode;
  titleClassName?: string;
  dataClassName?: string;
  getCustomRender?: (item: IDataRenderItem) => JSX.Element | undefined;
}

interface IProps {
  keyRenderList: IDataRenderItem[];
  data: Merch;
}

export type TKolMaterialCardUseDataConfigProps = IProps;

/**
 * note-color表示特殊色，默认红色，可以通过class覆盖改色
 * @param props
 * @returns
 */

const useDataConfig = (props: IProps) => {
  const { keyRenderList = [], data } = props;
  let {
    price: priceInfo,
    price_text,
    cos_ratio,
    cos_fee,
    sales: salesInfo,
    stock_num_sum,
    cos_ratio_text,
    living_price,
  } = data ?? {};
  // 售价
  const price = useMemo(() => formatPriceNumber((priceInfo || 0) / 100), [priceInfo]);
  // 月售
  const sales = useMemo(() => formatNumber(salesInfo || 0, 0), [salesInfo]);
  const stock = useMemo(() => formatNumber(stock_num_sum, 0), [stock_num_sum]);
  // 普通佣金 佣金率处理 添品这块的接口不会返回阶梯佣金数据
  const cosRatio = useMemo(() => cos_ratio || '-', [cos_ratio]);
  const cosFeeObject = useMemo(() => formatPriceNumber((cos_fee || 0) / 100), [cos_fee]);

  // 直播中价格计算
  const livingPriceFormat = useMemo(() => formatPriceNumber((living_price?.price || 0) / 100), [living_price]);

  const configs: dataRenderConfig[] = useMemo(() => {
    const list: dataRenderConfig[] = [];
    keyRenderList.forEach(item => {
      const { key, scale = 0, title, getCustomRender, titleClassName = '', dataClassName = '' } = item;
      const customRender = getCustomRender?.(item);
      let tmpObj: dataRenderConfig = {
        renderKey: key,
        titleNode: <div className={cs('title', 'title-color')}>{title}</div>,
        dataNode: customRender,
        scale: 1,
      };
      if (key === EDataRenderKey.price) {
        const priceText = price_text || '';

        tmpObj = {
          renderKey: key,
          titleNode: <div className={cs('title', 'title-color', titleClassName)}>{title || priceText || '到手价'}</div>,
          dataNode: (
            <div className={cs('data-item-row', 'note-color', dataClassName)}>
              <span className="number-pre_suffix">￥</span>
              <span className="number-large triple-line-number">{price?.integer}</span>
              <span className="number-small">{price?.decimal}</span>
              <span className="unit">{price?.unit}</span>
              {!isLiving && Boolean(living_price?.price) && (
                <span className="living-price">
                  {living_price?.price_text || '直播中'}¥{livingPriceFormat?.integer}
                  {livingPriceFormat?.decimal}
                  {livingPriceFormat?.unit}
                </span>
              )}
            </div>
          ),
          scale,
        };
      }
      if (key === EDataRenderKey.cos) {
        cos_ratio_text = cos_ratio_text?.replace('赚 ¥', '');
        const isRed = Boolean(cos_ratio_text && !['佣金', '佣金率', '赚 ¥'].includes(cos_ratio_text));
        tmpObj = {
          renderKey: key,
          titleNode: (
            <div className={cs('title', 'title-color', titleClassName, { 'note-color': isRed })}>
              {cos_ratio_text || '佣金'}
            </div>
          ),
          dataNode: (
            <div className={cs('data-item-row', 'normal-color', dataClassName)}>
              <span className={cs('number-large', { 'note-color': isRed })}>{cosRatio}</span>
              {cos_ratio ? (
                <>
                  <span className={cs('unit', { 'note-color': isRed })}>%</span>
                  <span className={cs('text', { 'note-color': isRed })} style={{ marginLeft: px2vw(2) }}>
                    赚¥{cosFeeObject?.integer}
                    {cosFeeObject?.decimal}
                    {cosFeeObject?.unit}
                  </span>
                </>
              ) : null}
            </div>
          ),
          scale,
        };
      }
      if (key === EDataRenderKey.sales) {
        tmpObj = {
          renderKey: key,
          titleNode: <div className="title title-color">{title || '月销'}</div>,
          dataNode: (
            <div className="data-item-row">
              <span className="normal-color number-large">{sales?.stat}</span>
              <span className="normal-color unit">{sales?.suffix}</span>
            </div>
          ),
          scale,
        };
      }
      if (key === EDataRenderKey.stock) {
        tmpObj = {
          renderKey: key,
          titleNode: <div className="title title-color">{title || '库存'}</div>,
          dataNode: (
            <div className="data-item-row">
              <span className="normal-color number-large">{stock?.stat}</span>
              <span className="normal-color unit">{stock?.suffix}</span>
            </div>
          ),
          scale,
        };
      }
      tmpObj.dataNode = customRender ?? tmpObj.dataNode;
      list.push(tmpObj);
    });
    return list;
  }, [
    cosFeeObject?.decimal,
    cosFeeObject?.integer,
    cosFeeObject?.unit,
    cosRatio,
    keyRenderList,
    price?.decimal,
    price?.integer,
    price?.unit,
    price_text,
    sales?.stat,
    sales?.suffix,
    stock?.stat,
    stock?.suffix,
  ]);
  return [configs];
};

export default useDataConfig;
