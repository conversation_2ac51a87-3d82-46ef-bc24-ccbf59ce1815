@import "~common/styles/merch_picking/mixins";
@import "~common/styles/merch_picking/visual/text";

@mixin font-config($font-size, $color) {
  font-size: $font-size;
  color: $color;
}

@mixin append-btn($color, $border, $background-color, $font-size, $border-radius, $padding, $width) {
  color: $color;
  border: $border;
  background-color: $background-color;
  font-size: $font-size;
  border-radius: $border-radius;
  line-height: 28px;
  padding-left: $padding;
  padding-right: $padding;
  text-align: center;
  height: 28px;
  width: $width;
}

.merch-cover__img {
  width: 100%;
  height: 100%;
}

.merch-cover__mask {
  @include mask($position: absolute);
  @include background($size: cover, $position: center);
}

.merch-cover__tips {
  @include mask($position: absolute);
  top: auto;
  text-align: center;
  font-size: 11px;
  line-height: 15px;
  background: rgba(22, 24, 35, .5);
  padding: 2px;
  color: #fff;
  z-index: 1;
}

.merch {
  position: relative;
  padding: 12px 16px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  &__multi-check {
    display: flex;
    align-items: center;
    margin-right: 12px;
  }

  &__thumb {
    position: relative;
    margin-right: 8px;
    flex-shrink: 0;
    overflow: hidden;
    display: inline-block;
    @include skeleton($width: 96px, $height: 96px);
    @include background($size: cover, $position: center);

    .novice-shop-tag {
      position: absolute;
      left: 0;
      top: 0;
    }
  }

  &__info {
    padding: 0;
    position: relative;

    &--bottom {
      margin-top: auto;

      .not-support {
        display: inline-block;
        width: 60px;
        height: 18px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #737373;
        line-height: 18px;
        margin-left: auto;
        margin-top: 5px;
      }
    }

    &-data {
      position: relative;
      padding-top: 4px;
      width: 100%;

      &.blank-height {
        margin-top: auto;
      }
    }

    &-data_multi {
      
      margin-top: auto;
    }
  }

  &__title {
    max-width: 233px;
    font-size: 14px;
    line-height: 20px;
    color: #161823;
    word-break: break-all;
    height: 20px;
  }

  &__metas {
    display: flex;
    align-items: center;
    margin: 4px 0 0;
    font-size: 0;
  }

  &__meta {
    font-size: 12px;
    color: #9e9e9e;
    white-space: nowrap;

    & + & {
      position: relative;
      padding-left: 6px;

      &::before {
        content: "";
        position: absolute;
        left: 8px;
        top: 50%;
        width: 1px;
        height: 6px;
        transform: translateY(-50%) scaleX(.5);
        background-color: #e6e6e6;
      }
    }

    &-number {
      margin-left: 2px;
    }
  }

  &__tags {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-flow: row wrap;
    max-height: 16px;
    overflow-y: hidden;
    font-size: 0;
    min-height: 14px;
    margin-top: 4px;

    &__recommend {

      &-wrapper {
        display: flex;
        align-items: center;
        height: 16px;
        margin-right: 4px;
        margin-bottom: 2px;
        background: #fdf6e9;
      }

      &-icon {
        width: 16px;
        height: 16px;
        margin-right: 2px;
      }

      &-icon-avatar {
        border-radius: 50%;
      }

      &-reason {
        color: #b9873d;
        font-size: 11px;
        max-width: 190px;
        @include text-ellipsis($line: 1);
      }
    }

    &_chief {
      padding: 0 4px;
      font-size: 11px;
      line-height: 14px;
      color: #ff701d;

      &.hairline--a::after {
        border-radius: 2px;
        border-color: rgba(255, 112, 29, .34);
      }
    }

    &__tiered-commission {
      padding: 0 3px;
      font-size: 10px;
      line-height: 15px;
      color: #fe2c55;
      margin-right: 4px;
      margin-bottom: 1px;

      &.hairline--a::after {
        border: 1px solid rgba(254, 44, 85, .34);
        border-radius: 4px;
      }
    }

    &__privilege {
      padding: 0 3px;
      font-size: 10px;
      line-height: 15px;
      color: #ff701d;
      margin-right: 4px;
      margin-bottom: 1px;

      &.hairline--a::after {
        border: 1px solid rgba(255, 112, 29, .34);
        border-radius: 4px;
      }

      &__wrapper {
        display: flex;
        flex-flow: row wrap;
        margin-top: 6px;
        height: 16px;
        overflow-y: hidden;
      }
    }

    &_commission {
      padding: 0 4px;
      font-size: 11px;
      line-height: 14px;
      color: #1966ff;
      margin-right: 4px;

      &.hairline--a::after {
        border: 1px solid rgba(25, 102, 255, .34);
        border-radius: 4px;
      }
    }

    &_captain-coupon {
      display: flex;
      box-sizing: border-box;
      border-radius: 2px;
      font-size: 11px;
      margin-top: 4px;
      margin-right: 4px;
      line-height: 15px;
      color: #ff7b47;

      &.hairline--a::after {
        border: 1px solid rgba(255, 123, 71, .34);
        border-radius: 4px;
      }
    }

    &_captain-coupon-type {
      padding: 0 3px;
      background-color: rgba(255, 123, 71, .08);

      &.hairline--a::after {
        border-width: 0 1px 0 0;
        border-color: rgba(255, 123, 71, .34);
        border-style: dashed;
      }
    }

    &_captain-coupon-title {
      padding: 0 3px;
    }
  }

  &__tag {
    height: 14px;
    margin-right: 4px;
    position: relative;
    padding: 2px 4px 1px;
    top: -1px;
    display: inline-flex;
    justify-content: center;
    align-items: center;

    &.is-android {
      top: -2px;
    }
  }

  .full-discount-tags {
    margin-top: 4px;
    margin-bottom: -8px;
  }

  .discount-tag {
    display: inline-block;
    font-size: 11px;
    line-height: 15px;
    background: #fff8fc;
    color: #fe2c55;
    border: .5px solid rgba(255, 38, 74, .2);
    border-radius: 2px;

    &__left {
      padding: 0 3.5px 0 4px;
      border-right: .5px dashed rgba(255, 38, 74, .2);
      display: inline-block;
    }

    &__right {
      padding: 0 4px 0 3.5px;
    }
  }

  &__tag + .coupon {
    margin-left: 4px;
  }

  &__money {
    font-size: 12px;
    color: #757575;
    position: relative;

    &-value {
      font-size: 20px;
      color: #f00;
      font-weight: 500;
      position: relative;
      bottom: -1px;

      &::before {
        content: "￥";
        font-size: 12px;
      }

      &-float {
        position: relative;
        bottom: -1px;
        color: #f00;
        font-size: 12px;
      }

      &-tiered {
        padding: 0 3px;
        font-size: 10px;
        line-height: 15px;
        color: #fe2c55;
        margin-right: 4px;
        margin-bottom: 1px;
      }
    }
  }

  &__actions {
    margin-top: 4px;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    & > div {
      margin-left: 8px;
    }
  }

  &__action {
    font-weight: 600;
    font-family: PingFangSC-Medium, PingFang SC;
    display: flex;
    align-items: center;
    justify-content: center;

    &--remove {
      font-family: PingFangSC-Regular, PingFang SC;
    }

    &--disable {
      opacity: .34;
    }

    &--tips {
      color: #fe2c55;
      font-size: 10px;
      line-height: 14px;
      position: absolute;
      top: -18px;
      right: 0;
      left: 0;
      text-align: center;
      font-weight: normal;
      white-space: nowrap;
    }
  }

  &__selling-point {
    width: 100%;
    padding: 8px;
    margin: 8px 0 0;
    border-radius: 2px;
    font-size: 13px;
    color: rgba(0, 0, 0, .38);
    background-color: #f5f5f5;
    position: relative;
    line-height: 1.3;

    &::before {
      content: "";
      position: absolute;
      left: 57px;
      top: -5px;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 0 7px 6px;
      border-color: transparent;
      border-bottom-color: #f5f5f5;
    }
  }

  &-skeleton {

    &__thumb {
      margin-right: 12px;
      @include skeleton($width: 96px, $height: 96px);
    }

    &__title {
      margin-top: 0;
      @include skeleton($width: 212px, $height: 20px);
    }

    &__subtitle {
      margin-top: 12px;
      @include skeleton($width: 123px, $height: 20px);
    }

    &__price {
      margin-top: auto;
      @include skeleton($width: 100px, $height: 20px);
    }
  }

  &__verify {
    width: 100%;
    height: auto;
    background: rgba(250, 250, 250, 1);
    border-radius: 6px;
    box-sizing: border-box;
    padding: 12px;
    margin-top: 24px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #737373;

    &_link_box {
      margin-top: 16px;
    }

    &_link {
      color: #2681ff;
    }
  }

  &__verify.in-aweme {
    border-radius: 2px;

    .merch__verify_link {
      color: #04498d;
    }
  }

  &__verify_box {
    width: 100%;

    .merch__verify {
      margin-bottom: 8px;
    }

    .merch__verify_reason_detail {
      color: #a6a6a6;
      margin-top: 8px;
    }

    .merch__apply_verify {
      width: 100%;
      height: 44px;
      border-radius: 4px;
      border: 1px solid rgba(232, 232, 232, 1);
      text-align: center;
      line-height: 44px;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #222;
    }
  }

  .activity_tag {
    display: inline-block;
    margin-right: 4px;
    margin-top: 2px;
    height: 16px;
    position: relative;
    top: 1px;

    &.is-android {
      margin-top: -2px;
    }
  }

  .douin_tag,
  .teampk_tag,
  .anxingou_tag,
  .source_tag,
  .yewu_tag,
  .worriless_tag {
    display: inline-block;
    position: relative;
    top: 2px;
    margin-right: 6px;
    background-size: 100%;
    background-repeat: no-repeat;
    background-position: center center;
  }

  &__roof-wrapper {
    position: absolute;
    top: 6px;
    right: 6px;
    bottom: 6px;
    left: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 76px;
    background-color: rgba(0, 0, 0, .34);
    border-radius: 6px;
    z-index: 2;

    .roof-button {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 12px 0;
      width: 64px;
      height: 64px;
      box-sizing: border-box;
      border-radius: 50%;
      background: #fff;

      &:not(:first-of-type) {
        margin-left: 83px;
      }

      &-icon {
        margin-bottom: 2px;
        width: 24px;
        height: 24px;
        @include background($position: center, $size: contain);

        &.icon-block-merchant {
          background-image: url(~static/images/merch_picking/icon-block-merchant.png);
        }
      }

      &-text {
        font-size: 11px;
        line-height: 14px;
        color: #161823;
      }
    }
  }

  &__sub-infos {
    display: flex;
    margin: 4px 0 15px;
  }

  &__sub-info {
    color: rgba(22, 24, 35, .5);
    font-size: 11px;
    line-height: 16px;
  }
}

.hotsoon .merch {

  .lazy-img {
    border-radius: 8px;
  }
}

.link-search {

  .merch.in-toutiao {

    .merch__money-value,
    .merch__money-value-float {
      color: #ff264a;
    }
  }
}

.merch-vertical {
  width: 142px;
  margin-right: 8px;
  font-size: 0;

  &:first-of-type {
    margin-left: 12px;
  }

  &:last-of-type {
    margin-right: 12px;
  }

  &__thumb {
    width: 142px;
    height: 142px;
    border-radius: 4px;
    overflow: hidden;
  }

  &__title {
    margin: 8px 0 4px;
    font-size: 14px;
    line-height: 18px;
    color: #262626;
    font-weight: bold;
    @include text-ellipsis($line: 1);
  }

  &__fee {
    font-size: 12px;
    line-height: 16px;
    color: #737373;
    margin: 0 0 8px;
    max-height: 26px;
    overflow-y: hidden;

    &-left {
      display: inline-block;
    }

    &-unit {
      font-size: 12px;
      color: #f00;
      letter-spacing: -2px;
    }

    &-cos {
      font-size: 18px;
      line-height: 26px;
      color: #f00;
      margin-right: 4px;
      font-weight: 500;
    }
  }
}

.merch-v2 {

  .merch__thumb {
    width: 108px;
    height: 108px;
    border-radius: 12px;
    margin-right: 12px;
  }

  .merch__thumb-multi {
    width: 88px;
    height: 88px;
  }

  .merch__sub-infos {
    margin: 4px 0 0;
  }

  .merch__multi-check {
    height: 112px;
  }

  .merch__multi-check-v2 {
    height: 88px;
    display: flex;
    align-items: center;
    margin-right: 12px;
  }
}
