@import "./common";

.merch {

  &__thumb {

    .lazy-img__img {
      border-radius: 2px;
    }
  }

  &__action {
    @include append-btn(
      $color: #fff,
      $border: .5px solid #ff0000,
      $background-color: #ff0000,
      $font-size: 13px,
      $border-radius: 6px,
      $padding: 17px
    );

    &--remove {
      @include append-btn(
        $color: #000000,
        $border: .5px solid #f5f5f5,
        $background-color: #f5f5f5,
        $font-size: 13px,
        $border-radius: 6px,
        $padding: 17px
      );
      font-weight: 600;
    }
  }

  &__content {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    width: 100%;
  }

  &__cos-down-tip {
    display: flex;
    align-items: center;
    width: 100%;
    height: 32px;
    margin-top: 8px;
    padding: 0 9px;
    border-radius: 4px;
    font-weight: 400;
    font-size: 13px;
    color: rgba(255, 133, 29, .75);
    background-color: rgba(255, 133, 29, .04);

    &-icon {
      margin-right: 3px;
    }
  }

  &__risk-tip-bar {
    margin-top: 4px;
    padding: 5px 13px;
    font-weight: 400;
    font-size: 11px;
    line-height: 14px;
    color: #fe2c55;
    display: flex;
    background-color: #fff9fa;
    border-radius: 4px;
    width: 100%;

    &-text {
      margin-left: 9px;
    }
  }
}
