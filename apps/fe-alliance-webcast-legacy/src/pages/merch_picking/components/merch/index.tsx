/* eslint-disable @ecom/ecom-rule/avoid-img */
import React, { <PERSON><PERSON><PERSON><PERSON>, MouseEventHandler, useMemo, useEffect, useRef, useState, useCallback } from 'react';
import cx from 'classnames';
import { toUnitString, genSchema } from '@src/lib/util/merch_picking';
import { Checkbox } from '@ecom/auxo-mobile';
import showToast from 'components/toast/ecom_toast';
import { DEFAULT_MERCH_THUMB, MERCH_THUMB_MASK, BETTER_PRICE_PATH } from 'common/constants';
import SmallerImage from '@ecom/smaller-image';
import NoviceShopTag from 'components/novice-shop-tag';
import { NoviceShopTagParams } from 'components/novice-shop-tag/types';
import { useAppController } from '../../modules/controller';
import Bottom from './components/merch_bottom/common';
import { formatMoney, handleAddBlockMerchant } from './helper';
import { isInAweme, isInToutiao, isInXigua, isAndroid } from '@src/lib/env_detect';
import { intersectObserver } from '../../services/utils';
import {
  MachineAuditResponse,
  Merch,
  MerchEventHandler,
  ReasonInfoSubType,
  EDataRenderKey,
  PmtCard4Kol,
} from '../../types';
import { renderPicProductTags, renderTextProductTags } from '../label-configuration';
import './style/index';
import ModalConfirm from '../merch_item/components/modal';
import InfoIcon from './info-icon';
import { BetterPriceTip } from '@alliance-mobile/platform-merchant-common/src/components/better-price-tip';
import {
  getBetterPriceReplaceSchema,
  isSkuMode,
} from '@alliance-mobile/platform-merchant-common/src/utils/better-price-replace';
import { openSchema } from '@src/common/bridge';
import useDataConfig from '../data-part/use-data-config';
import { DataPart } from '../data-part';
import { query } from '@byted-flash/utils';
import { ObserverDiv } from '@alliance-mobile/platform-merchant-common/src/components/observer-div';
import { sendEvent } from '@alliance-mobile/event';

export interface Props {
  data: Merch;
  index: number;
  children?: ReactNode;
  cosFeeMode?: boolean;
  vertical?: boolean;
  onClick: MerchEventHandler;
  machineAudit?: MachineAuditResponse;
  noviceShopParams: NoviceShopTagParams;
  customerClass?: string;
  lazyImg?: boolean;
  isShouldHideApplyVerifyButton?: boolean;
  /** 是否是多选样式 */
  isMulti?: boolean;
  /** 是否已被选中 */
  isSelected?: boolean;
  /** 是否已经添加 */
  isAppended?: boolean;
  /** 添加商品数量已达上限，该商品不能再被添加 */
  cantSelect?: boolean;
  /** 商品是否需要被消重 */
  needImpression?: boolean;
  /** 是否是推荐页面，展示月销，不展示库存 */
  isShowSales?: boolean;
  onSelectChange?(merch: Merch, status: boolean): void;
  onMachineAuditPopupConfirm?(title: string): void;
  jumpMachineAuditRule?(title: string, url: string): void;
  onMachineAuditRuleClick?(title: string, url: string): void;
  showBlockMerchant?: boolean;
  refresh?: () => void;
  onMerchTitleClick?(merch: Merch, index: number): void;
  onMerchBetterPriceClick?(merch: Merch, index: number): void;
  isMerchV2?: boolean;
  showRecommend?: boolean;
  /** 是否有data展示区域 */
  showDataPart?: boolean;
  isLinkSearch?: boolean;
  /** 商品卡底部替代风险提示的推荐理由的位置 */
  renderCardBottom?: (merch: PmtCard4Kol) => ReactNode;
  logExtra?: Record<string, unknown>;
}

export default React.memo((props: Props) => {
  const {
    data,
    index,
    cosFeeMode = true,
    vertical = false,
    children,
    onClick,
    machineAudit = {},
    customerClass,
    lazyImg,
    onMachineAuditPopupConfirm,
    jumpMachineAuditRule,
    onMachineAuditRuleClick,
    isShouldHideApplyVerifyButton = false,
    noviceShopParams = {},
    isMulti = false,
    isShowSales,
    isAppended,
    isSelected = false,
    cantSelect = false,
    onSelectChange,
    showBlockMerchant = false,
    refresh,
    onMerchTitleClick,
    onMerchBetterPriceClick,
    isMerchV2,
    showRecommend,
    showDataPart = true,
    isLinkSearch,
    renderCardBottom,
    logExtra = {},
  } = props;
  // const {
  //   explanation,
  //   reject_type,
  //   reject_reason,
  //   rule_title = '',
  //   rule_url = '',
  //   questionnaire_url = '',
  //   questionnaire_title = '',
  // } = machineAudit;
  const {
    cover,
    title,
    cos_fee,
    price,
    // todo 历史一直是有问题的，目前看起来应该取shop_level_label字段，但是产品交接也不太清楚，先不改了
    shop_label: shopLabel,
    order_product,
    pending_cos_ratio,
    block_add_reason: blockAddReason,
    live_add_enum: liveAddEnum,
    order_product: orderProduct,
  } = data;

  // 同款替换 > 风险提示
  const riskWarning = data?.new_price_good_same_style || data?.price_good_same_style || {};
  // 无订单商品
  const { can_apply: canApply } = orderProduct || {};
  // 商品不可添加、选择数量超过限度、无订单的时候，多选框为不可选择状态
  const disableSelect = useMemo(
    () => isAppended || Boolean(blockAddReason) || Boolean(liveAddEnum) || cantSelect || canApply,
    [isAppended, blockAddReason, liveAddEnum, cantSelect, canApply]
  );
  const [selected, setSelected] = useState(false);
  const [isShowBlockMerchantModal, setShowBlockMerchantModal] = useState(false); // 是否展示屏蔽弹窗
  const [showBlockMerchantBtn, setShowBlockMerchantBtn] = useState(false);
  const TOUCH_TIMEOUT = 700; // 长按的时间
  const ROOF_DISAPPEAR_TIMEOUT = 500; // 蒙层消失的时间

  const renderThirdKey = isShowSales ? EDataRenderKey.sales : EDataRenderKey.stock;

  const domRef = useRef<Element>(null);

  const [dataConfigs] = useDataConfig({
    data,
    keyRenderList: [
      {
        key: EDataRenderKey.price,
        scale: 52,
      },
      {
        key: EDataRenderKey.cos,
        scale: 84,
      },
      {
        key: renderThirdKey,
        scale: 54,
      },
    ],
  });

  /** 同款添加点击事件回调 */
  const handleClickTipInfo = useCallback(() => {
    onMerchBetterPriceClick?.(data, index);
    openSchema({
      schema: isSkuMode()
        ? getBetterPriceReplaceSchema({
            productId: data?.product_id,
            promotionId: data?.promotion_id,
            productFrom: 'live-control',
          })
        : genSchema({
            url: BETTER_PRICE_PATH,
            hide_nav_bar: 1,
            should_full_screen: 1,
            loading_bgcolor: '#ffffff',
            status_font_dark: 1,
            productFrom: 'live-control',
            productId: data?.product_id,
          }),
    });
  }, [data?.product_id, data?.promotion_id]);

  const handleClick = () => onClick(data, index);

  const stopHandleClick: MouseEventHandler = event => event.stopPropagation();

  const applyVerify = () => {
    onMachineAuditPopupConfirm && onMachineAuditPopupConfirm(questionnaire_title);
    jumpMachineAuditRule && jumpMachineAuditRule('', questionnaire_url);
  };

  const viewVerifyRule: MouseEventHandler = event => {
    onMachineAuditRuleClick && onMachineAuditRuleClick(rule_title, rule_url);
    jumpMachineAuditRule && jumpMachineAuditRule(rule_title, rule_url);
  };

  const handleSelectChange = value => {
    if (value === selected) {
      return;
    }
    setSelected(value);
    onSelectChange?.(data, value);
  };

  const handleClickCheckbox = () => {
    if (!cantSelect) {
      return;
    }
    showToast('添加商品数已达上限');
  };

  useEffect(() => {
    setSelected(isAppended || isSelected);
  }, [isAppended, isSelected]);

  useEffect(() => {
    const logs = { ...data, commodity_location: index, pick_third_source: query?.pick_third_source, extra: logExtra };
    domRef.current?.setAttribute('tea-data', JSON.stringify(logs));
    if (props.needImpression) {
      domRef.current?.setAttribute('need-impression', 'true');
    }
    intersectObserver.observe(domRef.current!);
    return () => intersectObserver.unobserve(domRef.current!);
  }, []);

  const moneyArray = (data.price / 100).toString().split('.');
  const moneyInt = moneyArray[0];
  const moneyFloat = moneyArray[1];
  const renderPrice = useMemo(() => {
    const priceText = data?.price_text || '';
    let preNode: ReactNode = null;
    if (priceText && priceText !== '到手价') {
      preNode = (
        <span
          style={{
            color: '#fe2c55',
            fontSize: '11px',
          }}>
          {priceText}
        </span>
      );
    }
    return (
      <div className="merch__info--bottom frow f-ai-fe">
        <span className="merch__money">
          {preNode}
          <span className="merch__money-value">{moneyInt}</span>
          {moneyFloat ? <span className="merch__money-value-float">.{moneyFloat}</span> : null}
        </span>
      </div>
    );
  }, [data?.price_text, moneyFloat, moneyInt]);
  // if (vertical) {
  //   return (
  //     <div className={cx('merch-vertical', customerClass)} onClick={handleClick}>
  //       <LazyImg
  //         src={cover}
  //         className="merch-vertical__thumb"
  //         mask={MERCH_THUMB_MASK}
  //         failure={DEFAULT_MERCH_THUMB}
  //         lazy={lazyImg}
  //         tips={Boolean(order_product) && '无联盟商达订单'}
  //       />
  //       <p className="merch-vertical__title">{title}</p>
  //       <p className="merch-vertical__fee">
  //         <span className="merch-vertical__fee-left">
  //           <span>赚</span>
  //           <span className="merch-vertical__fee-unit">￥</span>
  //           <span className="merch-vertical__fee-cos">{formatMoney(cos_fee)}</span>
  //         </span>
  //         {formatMoney(price, true)}
  //       </p>
  //       {children}
  //     </div>
  //   );
  // }

  // const isShowRejectReason = useMemo(() => {
  //   return (reject_type || reject_type === 0) && reject_reason;
  // }, [props.machineAudit]);

  const handleClickRoofWrapper = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
    setShowBlockMerchantBtn(false);
  };

  /**
   * 商品卡片监听长按事件，并与滑动事件区分
   */
  useEffect(() => {
    let touchStartTimer: number | null = null;
    const handleTouchStart = () => {
      touchStartTimer = window.setTimeout(() => {
        if (showBlockMerchant) {
          setShowBlockMerchantBtn(true);
        }
      }, TOUCH_TIMEOUT);
    };
    const handleTouchEnd = () => {
      touchStartTimer && clearTimeout(touchStartTimer);
    };
    const handleTouchMove = () => {
      touchStartTimer && clearTimeout(touchStartTimer);
    };
    domRef?.current?.addEventListener('touchstart', handleTouchStart);
    domRef?.current?.addEventListener('touchend', handleTouchEnd);
    domRef?.current?.addEventListener('touchmove', handleTouchMove);
    return () => {
      domRef?.current?.removeEventListener('touchstart', handleTouchStart);
      domRef?.current?.removeEventListener('touchstart', handleTouchEnd);
      domRef?.current?.removeEventListener('touchmove', handleTouchMove);
    };
  }, []);

  /**
   * 关闭顶部蒙层
   */
  const handleCloseBlockMerchant = useCallback(() => {
    setTimeout(() => {
      setShowBlockMerchantBtn(false);
    }, ROOF_DISAPPEAR_TIMEOUT);
  }, []);

  const handleClickMerchTitle = useCallback(
    e => {
      // 某些tab下点击商品卡标题希望跳转决策页
      if (!onMerchTitleClick) return;
      e.stopPropagation();
      onMerchTitleClick(data, index);
    },
    [data, onMerchTitleClick]
  );

  useEffect(() => {
    if (showBlockMerchant) {
      document.addEventListener('touchstart', handleCloseBlockMerchant);
      return () => {
        document.removeEventListener('touchstart', handleCloseBlockMerchant);
      };
    }
  }, [showBlockMerchant]);

  // 推荐理由
  const renderRecommendReason = (cardInstance: Merch) => {
    const { reason, icon, sub_rec_type } = cardInstance?.recommend_info || {};

    if (reason && sub_rec_type !== ReasonInfoSubType.WANRENTUAN) {
      return (
        <div className="merch__tags__recommend-wrapper">
          {icon ? (
            <img
              className={cx(
                'merch__tags__recommend-icon',
                sub_rec_type === ReasonInfoSubType.TONGXIN_DAREN && 'merch__tags__recommend-icon-avatar'
              )}
              src={icon}
            />
          ) : null}
          <span className="merch__tags__recommend-reason">{reason}</span>
        </div>
      );
    }

    return null;
  };

  const renderRisk = () => {
    return (
      <>
        {/* 佣金下调 */}
        {Boolean(pending_cos_ratio) && (
          <div className="merch__cos-down-tip">
            <img
              className="merch__cos-down-tip-icon"
              src={require('static/images/merch_picking/warningcircle_outlined.svg')}
            />
            {`商家已下调佣金至${pending_cos_ratio}%，今晚0点生效`}
          </div>
        )}
        {/* 风险提示 */}
        {!Object.keys(riskWarning)?.length && data.risk_warning && data.risk_warning.length ? (
          <div
            className={cx('merch__risk-tip-bar', {
              'merch__risk-tip-bar-margin4': Boolean(pending_cos_ratio),
            })}>
            <i className="merch__risk-tip-bar-icon">
              <InfoIcon />
            </i>
            <span className="merch__risk-tip-bar-text">{data.risk_warning[0]}</span>
          </div>
        ) : null}
        {Object.keys(riskWarning)?.length && isInAweme ? (
          <div className="merch__risk-tip-better-bar">
            <ObserverDiv
              customStyle={{
                position: 'absolute',
              }}
              once
              onShow={() => {
                sendEvent('high_price_warning', {
                  product_id: data.product_id,
                  page_name: isLinkSearch ? '直播中控链接添加' : '直播中控',
                  content: riskWarning?.text_right?.text || '',
                  button_type: riskWarning?.type,
                });
              }}
            />
            <BetterPriceTip
              className="commodity-card-v2-better-price-tip"
              border={riskWarning?.border_color ? `0.5px solid ${riskWarning?.border_color}` : ''}
              background={riskWarning?.bg_color}
              label={{
                text: riskWarning?.text?.text,
                color: riskWarning?.text?.color,
                icon: riskWarning?.text?.pic,
              }}
              content={{
                text: riskWarning?.text_right?.text,
              }}
              firstProduct={
                !isLinkSearch && riskWarning?.first_prod_info
                  ? {
                      product: riskWarning?.first_prod_info?.prod_info,
                      renderButton: () => '',
                    }
                  : undefined
              }
              entry={
                isLinkSearch
                  ? undefined
                  : {
                      productCover: riskWarning?.cover,
                      text: (riskWarning?.first_prod_info || riskWarning)?.text_button?.text,
                      color: (riskWarning?.first_prod_info || riskWarning)?.text_button?.color,
                      icon: (riskWarning?.first_prod_info || riskWarning)?.text_button?.pic,
                      onClick: () => {
                        handleClickTipInfo();
                      },
                    }
              }
            />
          </div>
        ) : null}
      </>
    );
  };

  return (
    <>
      {/* 新版商品卡跟随端上的ab实验 */}
      <div
        className={cx(`merch ${isMerchV2 ? 'merch-v2' : ''} frow f-ai-s hairline--t`, customerClass, {
          'in-toutiao': isInToutiao,
        })}
        style={isMulti ? { flexWrap: 'nowrap' } : {}}
        onClick={handleClick}
        ref={domRef}>
        {isMulti && (
          <div className={isMerchV2 ? 'merch__multi-check-v2' : 'merch__multi-check'}>
            {/* 由于Checkbox在disable情况下不触发onChange方法，也未抛出onClick方法，所以得单独加一个div处理disable逻辑 */}
            <div onClick={handleClickCheckbox}>
              <Checkbox type="round" checked={selected} disable={disableSelect} onChange={handleSelectChange} />
            </div>
          </div>
        )}
        <div className="merch__content" style={isMulti ? { width: 'calc(100% - 32vw)' } : {}}>
          <span className={cx('merch__thumb', { 'in-video': isInXigua }, isMulti && 'merch__thumb-multi')}>
            <SmallerImage src={data.cover} wrapClassName="merch-cover__img" fallbackSrc={DEFAULT_MERCH_THUMB} />
            <span className="merch-cover__mask" style={{ backgroundImage: `url(${MERCH_THUMB_MASK})` }} />
            {shopLabel && (
              <NoviceShopTag
                shopInfo={shopLabel?.shop_info}
                tag={shopLabel?.tag}
                logParams={{
                  commodity_id: data.product_id,
                  ...noviceShopParams,
                }}
              />
            )}
            {Boolean(order_product) && <span className="merch-cover__tips">无联盟商达订单</span>}
          </span>
          <div className="merch__info f-1 fcol f-ai-s">
            <div className="merch__title text--elli" onClick={handleClickMerchTitle}>
              {renderPicProductTags(data)}
              {data.title}
            </div>
            {/* TODO: 满减券 目前线上暂无数据，可删除 */}
            {/* <div className="full-discount-tags">
              {data.full_discount &&
                data.full_discount.map((item, idx) => (
                  <div className="discount-tag" key={idx}>
                    <span className="discount-tag__left">{item.campaign_type}</span>
                    <span className="discount-tag__right">{item.title}</span>
                  </div>
                ))}
            </div> */}
            <div className="merch__tags">
              {/* 推荐理由 */}
              {!showRecommend && renderRecommendReason(data)}
              {renderTextProductTags(data)}
            </div>

            {/* 数据展示模块 */}
            <>
              {showDataPart ? (
                <div className={cx('merch__info-data', isMulti && 'merch__info-data_multi')}>
                  <DataPart config={dataConfigs} />
                </div>
              ) : (
                <div className={cx('merch__info-data', 'blank-height')} />
              )}
              {children}
            </>
          </div>

          {/* 自定义传入底部&风险预警模块 */}
          {renderCardBottom ? renderCardBottom(data) : renderRisk()}
        </div>
        {/* 不能动 */}
        {!data.selling_point ? null : (
          <div className="merch__selling-point">
            <span className="text--elli-2">{data.selling_point}</span>
          </div>
        )}
        {/* machineAudit无值 */}
        {/* {isShowRejectReason && !questionnaire_url && (
          <div
            className={cx('merch__verify', {
              'in-aweme': isInAweme,
            })}
            onClick={stopHandleClick}>
            <div className="merch__verify_reason">原因：{reject_reason}</div>
            {rule_title && (
              <div className="merch__verify_link_box">
                请查看
                <span className="merch__verify_link" onClick={viewVerifyRule}>
                  《{rule_title}》
                </span>
              </div>
            )}
          </div>
        )} */}
        {/* machineAudit无值 */}
        {/* {isShowRejectReason && questionnaire_url && (
          <div className="merch__verify_box" onClick={stopHandleClick}>
            <div
              className={cx('merch__verify', {
                'in-aweme': isInAweme,
              })}>
              <div className="merch__verify_reason">原因：{reject_reason}</div>
              <div className="merch__verify_reason_detail">{explanation}</div>
              {rule_title && (
                <div className="merch__verify_link_box">
                  请查看
                  <span className="merch__verify_link" onClick={viewVerifyRule}>
                    《{rule_title}》
                  </span>
                </div>
              )}
            </div>
            {!isShouldHideApplyVerifyButton && (
              <div className="merch__apply_verify" onClick={applyVerify}>
                {questionnaire_title}
              </div>
            )}
          </div>
        )} */}
        {/* 不能动 */}
        {showBlockMerchantBtn && showBlockMerchant && (
          <div className="merch__roof-wrapper" onClick={handleClickRoofWrapper}>
            <div
              className="roof-button"
              onTouchEnd={e => {
                e.preventDefault();
                e.stopPropagation();
                setShowBlockMerchantModal(true);
              }}>
              <div className="roof-button-icon icon-block-merchant" />
              <div className="roof-button-text">屏蔽商家</div>
            </div>
          </div>
        )}
      </div>
      <ModalConfirm
        title="确认屏蔽该商家？"
        content="屏蔽商家后，不再展示该商家和你绑定的阶梯计划、定向计划、专属计划商品"
        onConfirm={async e => {
          e?.stopPropagation();
          e?.nativeEvent.stopImmediatePropagation();
          await handleAddBlockMerchant({
            shop_ids: `${data.shop_id || ''}`,
            toastText: '屏蔽成功，到“选品广场-我的-已屏蔽商家”查看',
            refreshGoodsList: refresh,
          });
          setShowBlockMerchantModal(false);
        }}
        confirmText="确定"
        cancelText="取消"
        onCancel={e => {
          e?.stopPropagation();
          e?.nativeEvent.stopImmediatePropagation();
          setShowBlockMerchantModal(false);
        }}
        isOpen={isShowBlockMerchantModal}
      />
    </>
  );
});
