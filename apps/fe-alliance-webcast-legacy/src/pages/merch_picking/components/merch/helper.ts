import { showToast } from '@src/common/bridge';
import { addShopToBlockMerchant } from '../../services/api';
import { Merch } from '../../types';

export const formatMoney = (money: number, needUnit?: boolean) => {
  if (money === undefined || money === null) {
    return '--';
  }

  const result = parseFloat(String(money / 100));

  if (needUnit) {
    return `￥${result}`;
  }

  return result;
};

/** 商品是否可以被选中 */
export const canSelect = (merch: Merch) => !merch.block_add_reason && !merch.live_add_enum && !merch.order_product?.can_apply;

/** 商品是否已经被添加 */
export const isAppended = (appendedList, target) => (appendedList || []).find(m => m === target.promotion_id);

// 点击添加黑名单
export const handleAddBlockMerchant = async (params: {
  shop_ids: string;
  toastText: string;
  refreshGoodsList?: () => void;
}) => {
  const { shop_ids, toastText, refreshGoodsList } = params;
  await addShopToBlockMerchant({ shop_ids });
  showToast(toastText);
  setTimeout(() => {
    refreshGoodsList && refreshGoodsList();
  }, 200);
};
