import React, { FC } from 'react';

const InfoIcon: FC<{ color?: string; className?: string }> = props => {
  const { color = '#FF215D', className = '' } = props;

  return (
    <svg
      width="12"
      height="12"
      viewBox="0 0 12 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6 12C9.31371 12 12 9.31371 12 6C12 2.68629 9.31371 0 6 0C2.68629 0 0 2.68629 0 6C0 9.31371 2.68629 12 6 12ZM6 2.65715C5.52946 2.65715 5.15071 3.04367 5.16027 3.51412L5.21427 6.17294C5.22296 6.60067 5.57219 6.94287 6 6.94287C6.42782 6.94287 6.77704 6.60067 6.78573 6.17294L6.83974 3.51412C6.84929 3.04367 6.47055 2.65715 6 2.65715ZM6 9.34287C6.42605 9.34287 6.77143 8.99749 6.77143 8.57144C6.77143 8.14539 6.42605 7.80001 6 7.80001C5.57395 7.80001 5.22857 8.14539 5.22857 8.57144C5.22857 8.99749 5.57395 9.34287 6 9.34287Z"
        fill={color}
        fillOpacity="0.6"
      />
    </svg>
  );
};

export default InfoIcon;
