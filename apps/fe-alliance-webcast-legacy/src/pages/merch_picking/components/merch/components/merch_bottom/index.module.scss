.merchInfoBottom {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  flex-grow: 1;

  .merchStrongMoneyWrapper {
    display: flex;
    align-items: flex-end;
  }

  .merchStrongMoney {
    font-family: DIN Alternate;
    font-size: 12px;
    color: #757575;
    position: relative;

    &Value {
      font-size: 17px;
      color: #fe2c55;
      font-weight: 500;
      position: relative;
      bottom: -1px;

      &::before {
        content: "￥";
        font-size: 14px;
      }
    }

    &Float {
      position: relative;
      bottom: -1px;
      color: #fe2c55;
      font-size: 14px;
    }
  }

  .merchMetasWrapper {
    display: flex;
    margin-top: 6px;
  }

  .merchMetas {
    display: flex;
    align-items: center;
    padding: 1px 4px;
    font-size: 0;
    border-radius: 2px;
    background-image: linear-gradient(rgba(255, 0, 50, .1), rgba(254, 44, 85, .1));
  }

  .merchMeta {
    color: #fe2c55;
    font-size: 10px;
    white-space: nowrap;
    display: flex;
    align-items: center;

    &:not(:nth-last-child(1)) {
      position: relative;

      &::after {
        content: "";
        width: 1px;
        height: 12px;
        transform: scaleX(.5);
        margin: 0 4px;
        background-color: #fe2c55;
      }
    }

    &Num {
      margin-left: 2px;
    }
  }
}
