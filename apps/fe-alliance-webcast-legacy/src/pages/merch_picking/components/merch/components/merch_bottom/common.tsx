/*
 * @Author: <EMAIL>
 * @Date: 2023-06-25 01:15:40
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-07-13 12:39:27
 * @Description: file content
 */
import React, { ReactNode, useMemo } from 'react';
import { Merch } from '../../../../types';
import styles from './index.module.scss';

interface Props {
  data: Merch;
  children?: ReactNode;
  priceNode?: ReactNode;
}

export default React.memo((props: Props) => {
  const { data, children, priceNode } = props;
  const moneyArray = (data.price / 100)?.toString()?.split('.');
  const moneyInt = moneyArray?.[0] || '';
  const moneyFloat = moneyArray?.[1] || '';
  // 佣金率处理 添品这块的接口不会返回阶梯佣金数据
  const fmtCommissionRate = useMemo(() => {
    return `${data?.cos_ratio || 0}%`;
  }, [data]);
  return (
    <div className={styles.merchInfoBottom}>
      {/* 售价 */}
      <div>
        {priceNode || (
          <div className={styles.merchStrongMoneyWrapper}>
            <span className={styles.merchStrongMoney}>
              <span className={styles.merchStrongMoneyValue}>{moneyInt}</span>
              {moneyFloat ? <span className={styles.merchStrongMoneyFloat}>.{moneyFloat}</span> : null}
            </span>
          </div>
        )}
        <div className={styles.merchMetasWrapper}>
          <div className={styles.merchMetas}>
            <span className={styles.merchMeta}>
              佣金率<span className={styles.merchMetaNum}>{fmtCommissionRate}</span>
            </span>
            <span className={styles.merchMeta}>
              赚<span className={styles.merchMetaNum}>¥{(data.cos_fee || 0) / 100}</span>
            </span>
          </div>
        </div>
      </div>
      <div>{children}</div>
    </div>
  );
});
