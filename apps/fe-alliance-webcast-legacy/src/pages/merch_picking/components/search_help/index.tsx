import React, { isValidElement } from 'react';
import Modal from 'components/modal';
import PageHeader from '../page_header';
import { EXTERNAL_HELP_SCHEMA } from '../../constants';
import './index.scss';

interface Props {
  isOpen: boolean;
  onNavBack: () => void;
}

const jumpToExternalHelp = () => {
  window.location.href = EXTERNAL_HELP_SCHEMA;
};

export default React.memo((props: Props) => {
  const { isOpen, onNavBack } = props;

  const content = [
    {
      question: '一、提示【不支持「描述低于平均行业」的商品推广，请选择其他商品】？',
      answer: ['您添加的商品所属店铺DSR（描述、服务、物流）数据不达标，低于行业平均水平，或者低于4.7分。'],
    },
    {
      question: '二、提示【不支持该类目的商品推广，请选择其他商品】？',
      answer: ['您添加的商品所属类目目前不支持推广。'],
    },
    {
      question: '三、提示【不支持标题包含「非法词」的推广，请选择其他商品】？',
      answer: ['您添加的商品标题命中了非法词，请您仔细检查，并联系商家在淘宝修改，生效后再提交该商品。'],
    },
    {
      question: '四、提示【不支持店铺等级小于1钻的商品推广，请选择其他商品】?',
      answer: ['您添加的商品所属店铺等级小于1钻，等级未达标'],
    },
    {
      question: '五、提示【不支持图片不合规的商品推广，请选择其他商品】?',
      answer: ['您添加的商品图片不合规，请您仔细检查，并联系商家在淘宝修改，生效后再提交该商品'],
    },
    {
      question: '六、没有找到【搜索】按钮？',
      answer: ['点击键盘中【回车】/【换行】/【搜索】键即可'],
    },
  ].map(qa => {
    const answers = qa.answer.map((answer, index) => {
      if (isValidElement(answer)) {
        return React.cloneElement(answer, { key: index });
      }
      return (
        <p className="search-help__answer" key={index}>
          {answer}
        </p>
      );
    });
    return (
      <div className="search-help__qa" key={qa.question}>
        <h3 className="search-help__question">{qa.question}</h3>
        {answers}
      </div>
    );
  });

  return (
    <Modal isOpen={isOpen} overlayClassName="search-help" className="search-help__content">
      <PageHeader title="查看帮助" onNavBack={onNavBack} />
      <div className="search-help__content-wrapper">{content}</div>
    </Modal>
  );
});
