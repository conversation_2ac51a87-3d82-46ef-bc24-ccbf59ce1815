@import '~common/styles/merch_picking/mixins';
.search-help {
    @include mask($top: 0px);
    &.ReactModal__Overlay {
        z-index: 1000;
    }
    background-color: #ffffff;
    &__content {
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 0px 0px 40px;
        overflow: hidden;
        font-size: 13px;
        color: #646464;
        -webkit-overflow-scrolling: touch;
        &-wrapper {
            padding: 0px 12px;
            flex: 1;
            overflow: auto;
        }
    }
    &__question {
        margin: 8px 0;
        font-size: 14px;
        line-height: 1.62;
        color: #000000;
    }
    &__answer {
        margin: 0;
        padding: 0 3px;
        line-height: 1.62;
    }
    &__store {
        margin: 8px 0;
    }
    &__link {
        padding-left: 16px;
        margin-top: 6px;
        color: #406599;
        @include background(
            $size: 11px auto,
            $position: left 2px top 1px,
            $image:
                url("data:image/svg+xml,%3Csvg width='11' height='13' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23406599' fill-rule='nonzero'%3E%3Cpath d='M8.015.586l2.4 2.4A2 2 0 0 1 11 4.398V10a3 3 0 0 1-3 3H3a3 3 0 0 1-3-3V3a3 3 0 0 1 3-3h3.6a2 2 0 0 1 1.415.586zM3 1a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h5a2 2 0 0 0 2-2V4.814a2 2 0 0 0-.586-1.415L7.601 1.586A2 2 0 0 0 6.186 1H3z'/%3E%3Crect x='3' y='8' width='5' height='1' rx='.5'/%3E%3Crect x='3' y='5' width='5' height='1' rx='.5'/%3E%3C/g%3E%3C/svg%3E")
        );
    }
}
