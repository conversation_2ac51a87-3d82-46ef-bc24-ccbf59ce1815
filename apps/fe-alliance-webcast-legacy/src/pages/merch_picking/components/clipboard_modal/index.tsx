import React, { useImperativeHandle, useMemo, useState } from 'react';
import LazyImg from 'components/lazy_img';
import Tag from 'components/tag';
import Modal from 'components/modal/index';
import showToast from 'components/toast/ecom_toast';
import { toUnitString } from '@src/lib/util/merch_picking';
import { Merch, MerchEventHandler } from '../../types';
import { CLIPBOARD_MODAL_CLOSE_ICON } from '../../constants';
import { DEFAULT_MERCH_THUMB, MERCH_THUMB_MASK } from 'common/constants';
import { getStorage } from '@src/lib/util';
import { PairLimitCheckCallback } from '../pair_limit/types';
import cx from 'classnames';
import './style/index';

export interface Props {
  isVisible: boolean;
  data: Merch;
  isPicked: boolean;
  isAuditing: boolean;
  closeModal: () => void;
  resumeModal: () => void;
  pickMerch: MerchEventHandler;
  setIsOpen: (value: boolean) => void;
  onPairLimitCheck: PairLimitCheckCallback;
}

const modalOverlayStyle = {
  background: 'rgba(0,0,0,0.5)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
};

export default React.memo(
  React.forwardRef((props: Props, ref: any) => {
    const { isVisible, data, pickMerch, closeModal, resumeModal, isPicked, isAuditing, setIsOpen, onPairLimitCheck } =
      props;

    // 是否命中直播挂车拦截
    const [bypassList, setBypassList] = useState<string[]>([]);
    const isBlocking = !isPicked && !bypassList.includes(data.product_id) && Boolean(data.order_product);
    const isAddDisable = Boolean(data?.block_add_reason) || Boolean(data?.live_add_enum);
    const isAggr = data.tag_codes?.includes('retail_cross_aggr_product');
    const actionButton = () => {
      if (isPicked) {
        return <>取消</>;
      }
      if (isAuditing) {
        return (
          <div className="clipboard_modal__bottom-auditing">
            <span className="clipboard_modal__bottom-auditing-img" />
            <span className="clipboard_modal__bottom-auditing-txt">商品审核中</span>
          </div>
        );
      }
      return <>添加</>;
    };

    /**
     * 点击添加或者取消按钮之后的处理事件
     */
    const handleAdd = () => {
      if (data.block_add_reason) {
        showToast(data.block_add_reason);
        return;
      }
      if (data.live_add_enum) {
        showToast(data.live_add_enum);
        return;
      }
      // 设置非批量，设置从粘贴板来
      pickMerch(data, -1, false, true);
    };

    // 直播挂车拦截弹框提示
    const handleBlockAdd = () => {
      closeModal();

      onPairLimitCheck?.(data, 0, bypass => {
        if (bypass) {
          showToast('申请豁免成功');
          setBypassList([...bypassList, data.product_id]);
          resumeModal();
        }
      });
    };

    const handleClick = async () => {
      if (isBlocking) {
        return handleBlockAdd();
      }

      if (!isPicked && !isAuditing) {
        const now = new Date().getTime();
        const lastTime = await getStorage('PAYMENT_MINUS_INVENTORY');
        let isOverdue = true;
        if (lastTime && typeof lastTime === 'number') {
          isOverdue = now - Number(lastTime) > 86400000;
        }
        if (data.oversold_remind && isOverdue) {
          setIsOpen(true);
          return;
        }
      }

      handleAdd();
    };

    /**
     * 传给父组件
     */
    useImperativeHandle(ref, () => ({
      handleAdd,
    }));

    const renderPrice = useMemo(() => {
      const priceText = data?.price_text || '';
      let priceColor = '';
      if (priceText && priceText !== '到手价') {
        priceColor = '#fe2c55';
      }
      return (
        <span
          className="clipboard_modal__meta clipboard_modal__price"
          style={{
            color: priceColor,
          }}>
          {priceText || '到手价'}￥{data.price / 100}
        </span>
      );
    }, [data?.price, data?.price_text]);

    return (
      <Modal isOpen={isVisible} style={{ overlay: modalOverlayStyle }} className="modal-wrapper">
        <div className="clipboard_modal fcol f-ai-s ">
          <LazyImg
            src={data.cover}
            className="clipboard_modal__thumb"
            mask={MERCH_THUMB_MASK}
            failure={DEFAULT_MERCH_THUMB}
            lazy={false}
            tips={isBlocking && '无联盟商达订单'}
          />
          <img src={CLIPBOARD_MODAL_CLOSE_ICON} className="clipboard_modal__close_btn" onClick={closeModal} />
          <div className="clipboard_modal__info f-1 fcol f-ai-s">
            <div className="clipboard_modal__title text--elli-2">
              {data.privilege_info?.icon?.url_list?.[0] ? (
                <span
                  className="clipboard_modal__title__privilege"
                  style={{
                    backgroundImage: `url(${data.privilege_info?.icon?.url_list?.[0]})`,
                  }}
                />
              ) : null}
              <Tag className="clipboard_modal__tag">{data.platform_label}</Tag>
              {data.title}
            </div>
            {isAggr ? null : (
              <>
                <div className="clipboard_modal__metas">
                  {renderPrice}
                  {data.sales ? (
                    <span className="clipboard_modal__meta clipboard_modal__solds">
                      已售{toUnitString(data.sales, 10000, '万')}
                    </span>
                  ) : null}
                </div>
                <div className="clipboard_modal__info--bottom frow">
                  <span className="clipboard_modal__money">
                    每单赚
                    <span className="clipboard_modal__money-value">{data.cos_fee / 100}</span>
                  </span>
                </div>
              </>
            )}
            <div
              className={cx({
                'clipboard_modal__bottom-cancel': isPicked,
                'clipboard_modal__bottom-btn': !isPicked,
                'clipboard_modal__bottom-disable': !isPicked && (isBlocking || isAddDisable),
              })}
              onClick={handleClick}>
              {actionButton()}
            </div>
          </div>
        </div>
      </Modal>
    );
  })
);
