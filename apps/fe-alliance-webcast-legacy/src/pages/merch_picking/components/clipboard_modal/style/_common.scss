@import '~common/styles/merch_picking/mixins';
@import '~common/styles/merch_picking/visual/text';
@import '~common/styles/merch_picking/modules/keyframe';
@import '../../merch/style/common';

@mixin modal-content($width, $backgroundColor, $borderRadius) {
    width: $width;
    background-color: $backgroundColor;
    border-radius: $borderRadius;
    padding: 0;
    position: relative;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    border: none;
    overflow: hidden;
}

@mixin append-btn($width, $height, $color, $background-color, $border-radius, $font-size, $margin-top) {
    max-width: $width;
    height: $height;
    line-height: $height;
    color: $color;
    background-color: $background-color;
    border-radius: $border-radius;
    font-size: $font-size;
    margin-top: $margin-top;
    text-align: center;
}

@mixin close-btn($size, $position) {
    position: absolute;
    width: $size;
    height: $size;
    right: $position;
    top: $position;
    border-radius: $size / 2;
}


.clipboard_modal {
    position: relative;
    width: 100%;

    &__thumb {
        position: relative;
        // flex-shrink: 0;
        overflow: hidden;
        @include skeleton($width: 279px, $height: 188px, $border-radius: 0);
        @include background($size: cover, $position: center);
    }
    &__close_btn {
        @include close-btn(20px, 12px);
    }
    &__info {
        padding: 12px 16px 16px 16px;
        &--bottom {
            margin-top: 3px;
        }
    }
    &__title {
        max-width: 247px;
        line-height: 20px;
        @include font-config(14px, #161823);
        &__privilege {
            display: inline-block;
            width: 37px;
            height: 16px;
            background-repeat: no-repeat;
            background-position: center;
            background-size: 100%;
            margin-right: 6px;
            vertical-align: middle;
        }
    }
    &__metas {
        margin: 4px 0 0 0;
        font-size: 0;
    }
    &__meta {
        @include font-config(12px, rgba(22, 24, 35, 0.34));
        white-space: nowrap;
        & + & {
            position: relative;
            padding-left: 17px;
        }
    }
    &__tags {
        font-size: 0;
    }
    &__tag {
        margin-right: 4px;
    }
    &__money {
        @include font-config(12px, rgba(22, 24, 35, 0.5));
        &-value {
            @include font-config(18px, #fe2c55);
            font-weight: 500;
            line-height: 22px;
            &::before {
                content: '￥';
                font-size: 12px;
            }
        }
    }

    &__bottom {
        &-auditing {
            display: flex;
            justify-content: center;
            align-items: center;
            &-img {
                display: inline-block;
                width: 20px;
                height: 20px;
                @include background(
                    $size: cover,
                    $position: center,
                    $image:
                        url("data:image/svg+xml,%3Csvg width='22' height='22' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18.935 15.251a9 9 0 1 0-4.726 4.16' stroke='#ffffff' stroke-width='3' fill='none' fill-rule='evenodd' stroke-linecap='round'/%3E%3C/svg%3E")
                );
                animation: spin 1.2s linear infinite;
                margin-right: 7px;
            }
            &-txt {
                font-size: 15px;
                color: #fff;
                display: inline-block;
            }
        }
        &-btn.disable {
            opacity: 0.34;
        }
    }
}
