@import '~common/styles/merch_picking/mixins';
@import '~common/styles/merch_picking/visual/hairline';
@import '~common/styles/merch_picking/modules/keyframe';

.ReactModal {
  &__Overlay {
    z-index: 100;
    @include mask();
    &--after-open {
      animation: fade-in 0.2s ease-in-out forwards;
    }
    &--before-close {
      animation: fade-out 0.4s ease-in-out forwards;
    }
  }
  &__Content {
    &:focus {
      outline: none !important;
    }
  }
}

.confirm-live {
  &-modal {
    @include absolute-center();
    width: 300px;
    text-align: center;
    background-color: #ffffff;
    &__overlay {
      @include mask();
      z-index: 1000;
      background-color: rgba(0, 0, 0, 0.5);
    }
  }
  &-imgwrapper {
    width: 100%;
    height: 180px;
    &__img {
      width: 100%;
      height: 100%;
    }
  }

  &__title {
    margin: 24px 0 14px;
    color: #000;
    font-size: 17px;
    font-weight: normal;
    padding: 0 22px;
    &--lg {
      margin: 32px 0;
      color: #353535;
      font-size: 16px;
    }
  }
  &__body {
    padding: 0 24px;
    margin: 32px 0;
    font-size: 14px;
    line-height: 18px;
    color: rgba(22, 24, 35, 0.75);
  }
  &__footer {
    display: flex;
    flex-direction: row;
    margin-bottom: 24px;
    padding: 0 24px;
  }
}
