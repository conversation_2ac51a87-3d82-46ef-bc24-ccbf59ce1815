import React, { <PERSON>actN<PERSON>, <PERSON><PERSON>ventHandler } from 'react';
import Modal from 'components/modal/index';

import './style/index';

interface Props extends ReactModal.Props {
  img?: string;
  title?: ReactNode;
  content?: string;
  onCancel?: MouseEventHandler;
  onConfirm: MouseEventHandler;
  cancelText?: string;
  confirmText?: string;
}

export default React.memo((props: Props) => {
  const {
    img,
    title,
    content,
    onConfirm,
    onCancel,
    cancelText = '我知道了',
    confirmText = '去看看',
    ...modalProps
  } = props;

  return (
    <Modal
      {...modalProps}
      className='confirm-live-modal'
      overlayClassName='confirm-live-modal__overlay'
    >
      {!img ? null : <div className='confirm-live-imgwrapper'><img src={img} className='confirm-live-imgwrapper__img' /></div>}
      {content ? <p className='confirm-live__body'>{content}</p> : null}
      <div className='confirm-live__footer'>
        {cancelText && onCancel ? (
          <span className='confirm-live__cancel' onClick={onCancel}>
            {cancelText}
          </span>
        ) : null}
        <span className='confirm-live__confirm' onClick={onConfirm}>
          {confirmText}
        </span>
      </div>
    </Modal>
  );
});
