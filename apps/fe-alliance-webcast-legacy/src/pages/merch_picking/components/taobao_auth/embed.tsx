import React, { <PERSON><PERSON>ventHandler } from 'react';

import './index.scss';

interface Props {
    onActionClick: MouseEventHandler;
}

export default React.memo((props: Props) => {
    const { onActionClick } = props;
    return (
        <div className="taobao-auth--embed">
            <h3 className="taobao-auth--embed__title">您还未绑定淘宝客PID</h3>
            <p className="taobao-auth--embed__hint">需要绑定淘宝客PID才可以使用添加淘宝商品功能</p>
            <span className="taobao-auth--embed__action" onClick={onActionClick}>
                绑定PID
            </span>
        </div>
    );
});
