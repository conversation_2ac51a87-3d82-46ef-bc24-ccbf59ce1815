import React, { MouseEventHandler } from 'react';
import Confirm from 'components/modal/confirm';

interface Props {
    isOpen: boolean;
    onConfirm: MouseEventHandler;
    onCancel: MouseEventHandler;
}

export default React.memo((props: Props) => {
    return (
        <Confirm
            title="您还未绑定淘宝联盟PID"
            body="未绑定pid，推广商品无法获取佣金；
        立即前往绑定，成功后即可添加淘宝商品"
            confirmText="前往绑定"
            {...props}
        />
    );
});
