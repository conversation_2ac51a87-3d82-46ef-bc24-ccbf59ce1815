.header-bar {
  min-height: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 18px;

  .total {
    color: rgba(22, 24, 35, .75);
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    display: flex;
    align-items: center;

    .num {
      color: #161823;
      margin: 0 4px;
    }
  }

  .filter-btn {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: rgba(22, 24, 35, .75);
    display: flex;
    align-items: center;

    .icon {
      margin-left: 1px;
      transform: translateY(-2%);
    }

    &.filter-btn-active {
      color: #161823;
      font-weight: 500;
    }
  }
}


// 新版，实验推全 需要删代码的时候直接改为header-bar即可

.header-bar-v2 {
  min-height: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  padding: 12px 16px;

  .total {
    color: #161823;
    font-weight: 400;
    font-size: 14px;
    line-height: 16px;
    display: flex;
    align-items: center;
    font-family: "PingFang SC";

    .num {
      color: rgba(22, 24, 35, .5);
      margin: 0 4px;
    }
  }

  .filter-btn {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: rgba(22, 24, 35, .75);
    display: flex;
    align-items: center;

    .icon {
      margin-left: 1px;
      transform: translateY(-2%);
    }

    &.filter-btn-active {
      color: #161823;
      font-weight: 400;
    }
  }
}
