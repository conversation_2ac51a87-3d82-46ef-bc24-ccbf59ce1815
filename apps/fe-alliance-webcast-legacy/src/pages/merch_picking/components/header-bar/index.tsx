/* eslint-disable max-len */
import cx from 'classnames';
import React, { useState } from 'react';
import SearchFilter from '../search_filter';
import styles from './index.module.scss';
import { FilterSource } from '../../services/api';

type Props = {
  total: number;
  searchText?: string;
  onFilterSubmit?: (result: Record<string, unknown>) => void;
  isMerchV2?: boolean;
};

const HeaderBar: React.FC<Props> = ({ total, searchText, onFilterSubmit, isMerchV2 }) => {
  const [searchFilterVisible, setSearchFilterVisible] = useState(false);
  const [hasSelectedValue, setHasSelectedValue] = useState(false);

  const handleFilterSubmit = (result: Record<string, unknown>, _hasSelectedValue: boolean) => {
    setHasSelectedValue(_hasSelectedValue);
    onFilterSubmit?.(result);
  };

  const svgFillOpacity = isMerchV2 ? '0.5' : '0.75';

  return (
    <>
      <div
        className={cx(styles['header-bar'], {
          [styles['header-bar-v2']]: isMerchV2,
        })}>
        <div className={styles['total']}>共{total}件商品</div>
        <div
          className={cx(styles['filter-btn'], hasSelectedValue && styles['filter-btn-active'])}
          onClick={() => setSearchFilterVisible(true)}>
          {isMerchV2 ? (
            <svg
              className={styles['icon']}
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <path
                d="M8.42532 14.2276L8.23854 14.8502L8.42532 14.2276ZM7.42532 13.9276L7.23854 14.5502L7.23854 14.5502L7.42532 13.9276ZM3.14508 4.08549L3.67202 3.70492V3.70492L3.14508 4.08549ZM13.8549 4.08549L14.3819 4.46606V4.46606L13.8549 4.08549ZM5.52672 7.38315L4.99978 7.76372L5.52672 7.38315ZM11.65 12.3119V8.84682H10.35V12.3119H11.65ZM8.23854 14.8502C9.93882 15.3603 11.65 14.0871 11.65 12.3119H10.35C10.35 13.2163 9.47828 13.8649 8.6121 13.605L8.23854 14.8502ZM7.23854 14.5502L8.23854 14.8502L8.6121 13.605L7.6121 13.305L7.23854 14.5502ZM5.35002 12.0119C5.35002 13.1822 6.11764 14.2139 7.23854 14.5502L7.6121 13.305C7.04107 13.1337 6.65002 12.6081 6.65002 12.0119H5.35002ZM5.35002 8.84688V12.0119H6.65002V8.84688H5.35002ZM6.05366 7.00258L3.67202 3.70492L2.61813 4.46606L4.99978 7.76372L6.05366 7.00258ZM3.67202 3.70492C3.50485 3.47346 3.67023 3.15 3.95576 3.15V1.85C2.60974 1.85 1.83005 3.37487 2.61813 4.46606L3.67202 3.70492ZM3.95576 3.15H13.0442V1.85H3.95576V3.15ZM13.0442 3.15C13.3298 3.15 13.4951 3.47346 13.328 3.70492L14.3819 4.46606C15.1699 3.37487 14.3903 1.85 13.0442 1.85V3.15ZM13.328 3.70492L10.9464 7.00252L12.0003 7.76366L14.3819 4.46606L13.328 3.70492ZM6.65002 8.84688C6.65002 8.18471 6.44135 7.53938 6.05366 7.00258L4.99978 7.76372C5.22747 8.07898 5.35002 8.45799 5.35002 8.84688H6.65002ZM11.65 8.84682C11.65 8.45793 11.7726 8.07893 12.0003 7.76366L10.9464 7.00252C10.5587 7.53933 10.35 8.18465 10.35 8.84682H11.65Z"
                fill="#161823"
                fillOpacity={hasSelectedValue ? '1' : svgFillOpacity}
              />
            </svg>
          ) : (
            <>
              <span>筛选</span>
              <svg
                className={styles['icon']}
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="https://www.w3.org/2000/svg">
                <mask id="path-1-inside-1_175_136684" fill="white">
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M10.1976 8.05139C10.0693 8.22392 9.99996 8.43322 9.99996 8.64825V12.1652C9.99996 12.5264 9.8051 12.8596 9.49023 13.0367L7.49023 14.1617C6.82362 14.5367 5.99996 14.055 5.99996 13.2902V8.63691C5.99996 8.42188 5.93065 8.21258 5.80231 8.04006L2.49707 3.59686C2.00631 2.93714 2.47717 2 3.29941 2H12.709C13.5312 2 14.0021 2.93714 13.5113 3.59686L10.1976 8.05139Z"
                  />
                </mask>
                <path
                  d="M13.5113 3.59686L12.709 3L13.5113 3.59686ZM2.49707 3.59686L1.69472 4.19372L2.49707 3.59686ZM5.80231 8.04006L4.99996 8.63691L5.80231 8.04006ZM7.49023 14.1617L7.98049 15.0333L7.49023 14.1617ZM8.99996 8.64825V12.1652H11V8.64825H8.99996ZM8.99996 12.1652L6.99997 13.2902L7.98049 15.0333L9.98049 13.9083L8.99996 12.1652ZM6.99996 13.2902V8.63691H4.99996V13.2902H6.99996ZM6.60466 7.4432L3.29941 3L1.69472 4.19372L4.99996 8.63691L6.60466 7.4432ZM3.29941 3H12.709V1H3.29941V3ZM12.709 3L9.39527 7.45454L11 8.64825L14.3136 4.19371L12.709 3ZM12.709 3L12.709 3L14.3136 4.19371C15.2952 2.87428 14.3534 1 12.709 1V3ZM3.29941 3L3.29941 3V1C1.65493 1 0.713202 2.87427 1.69472 4.19372L3.29941 3ZM6.99996 8.63691C6.99996 8.20685 6.86134 7.78825 6.60466 7.4432L4.99996 8.63691H6.99996ZM6.99997 13.2902L6.99996 13.2902H4.99996C4.99996 14.8198 6.64728 15.7832 7.98049 15.0333L6.99997 13.2902ZM8.99996 12.1652V12.1652L9.98049 13.9083C10.6102 13.5541 11 12.8877 11 12.1652H8.99996ZM11 8.64825L9.39527 7.45454C9.13859 7.79959 8.99996 8.2182 8.99996 8.64825H11Z"
                  fill="#161823"
                  fillOpacity={hasSelectedValue ? '1' : svgFillOpacity}
                  mask="url(#path-1-inside-1_175_136684)"
                />
              </svg>
            </>
          )}
        </div>
      </div>
      <SearchFilter
        visible={searchFilterVisible}
        searchText={searchText}
        filterSource={FilterSource.CART}
        onVisibleChange={setSearchFilterVisible}
        onSubmit={handleFilterSubmit}
        isMerchV2={isMerchV2}
      />
    </>
  );
};

export default HeaderBar;
