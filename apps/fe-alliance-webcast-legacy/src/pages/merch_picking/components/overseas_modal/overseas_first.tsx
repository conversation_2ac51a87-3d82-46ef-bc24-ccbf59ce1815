import React, { MouseEventHandler } from 'react';
import Confirm from 'components/modal/confirm';

interface Props {
    isOpen: boolean;
    onConfirm: MouseEventHandler;
    onCancel?: MouseEventHandler;
}

export default React.memo((props: Props) => {
    return (
        <Confirm
            title="添加提示"
            body="您推广的商品为海淘商品，当前展示佣金仅为预估参考，最终佣金收入将根据打款时刻汇率折算，请以实际到账为准"
            confirmText="我知道了"
            {...props}
        />
    );
});
