import React, { MouseEventHandler } from 'react';
import Confirm from 'components/modal/confirm';

interface Props {
    isOpen: boolean;
    onConfirm: MouseEventHandler;
    onCancel: MouseEventHandler;
}

export default React.memo((props: Props) => {
    return (
        <Confirm
            title="开通海淘账户"
            body="推广海淘商品，需要达人账号和机构账号均开通海淘收款账户。选择立即开通，可前往账户开通流程"
            confirmText="立即开通"
            cancelText="暂不开通"
            {...props}
        />
    );
});
