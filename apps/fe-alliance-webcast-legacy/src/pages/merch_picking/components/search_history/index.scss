@import "~common/styles/merch_picking/mixins";

.search-history {
    margin: 0;
    padding-left: 16px;
    list-style: none;
    font-size: 0;
    overflow: hidden;
    flex-shrink: 0;
    &__header {
        padding: 12px 16px 0 0;
        font-size: 13px;
        color: #8a8b91;
    }
    &__clear {
        display: inline-block;
        width: 20px;
        height: 20px;
        @include background(
            $size: 20px auto,
            $position: center,
            $image: url("data:image/svg+xml,%3Csvg width='20' height='20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000' fill-rule='nonzero' opacity='.5'%3E%3Cpath d='M2.75 6.27h15a.5.5 0 1 0 0-1h-15a.5.5 0 1 0 0 1zM7.75 9.52v4.5a.5.5 0 0 0 1 0v-4.5a.5.5 0 1 0-1 0zM11.5 9.52v4.5a.5.5 0 1 0 1 0v-4.5a.5.5 0 1 0-1 0z'/%3E%3Cpath d='M15.25 6v8.846c0 1.175-1.107 2.154-2.5 2.154h-5c-1.393 0-2.5-.979-2.5-2.154V6a.5.5 0 0 0-1 0v8.846C4.25 16.603 5.83 18 7.75 18h5c1.92 0 3.5-1.397 3.5-3.154V6a.5.5 0 1 0-1 0z'/%3E%3Cpath d='M13 5.682V4.5c0-.56-.408-1-.893-1H8.393c-.485 0-.893.44-.893 1v1.182a.5.5 0 0 1-1 0V4.5c0-1.097.839-2 1.893-2h3.714c1.054 0 1.893.903 1.893 2v1.182a.5.5 0 1 1-1 0z'/%3E%3C/g%3E%3C/svg%3E")
        );
    }
    &__item {
        border-radius: 2px;
        max-width: 200px;
        padding: 0px 16px;
        height: 28px;
        line-height: 28px;
        overflow: hidden;
        float: left;
        font-size: 14px;
        color: #737373;
        margin-right: 12px;
        margin-bottom: 20px;
        border: 0.5px solid rgba(22, 24, 35, 0.12);
    }
}
