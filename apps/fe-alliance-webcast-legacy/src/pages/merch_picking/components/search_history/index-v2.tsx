/*
 * @Author: <EMAIL>
 * @Date: 2023-06-25 00:05:29
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-07-13 12:46:33
 * @Description: file content
 */
import React, { useState, useCallback } from 'react';
import Confirm from 'components/modal/confirm';

import './index-v2.scss';

interface Props {
  items: string[];
  onItemClick: Function;
  onClear: Function;
}

export default React.memo((props: Props) => {
  const { items, onItemClick, onClear } = props;
  const [isConfirmingClear, setIsConfirmingClear] = useState(false);
  const onClearConfirm = useCallback(() => {
    setIsConfirmingClear(false);
    onClear();
  }, []);
  const onClearCancel = useCallback(() => {
    setIsConfirmingClear(false);
  }, []);

  const onClearBtnClick = useCallback(() => {
    setIsConfirmingClear(true);
  }, []);

  if (!items || items?.length === 0) {
    return null;
  }

  return (
    <div className="search-history-v2">
      <div className="search-history-v2__header frow f-jc-sb f-ai-c">
        历史搜索
        <span className="search-history-v2__clear" onClick={onClearBtnClick} />
      </div>
      <ul className="search-history-v2__items">
        {items?.map(item => (
          <li key={item} className="search-history-v2__item text--elli" onClick={() => onItemClick(item)}>
            {item}
          </li>
        ))}
      </ul>
      <Confirm
        isOpen={isConfirmingClear}
        title="清除全部历史记录？"
        onConfirm={onClearConfirm}
        onCancel={onClearCancel}
      />
    </div>
  );
});
