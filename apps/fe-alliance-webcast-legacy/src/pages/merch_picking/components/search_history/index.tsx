import React, { useState, useCallback } from 'react';
import Confirm from 'components/modal/confirm';

import './index.scss';

interface Props {
    items: string[];
    onItemClick: Function;
    onClear: Function;
}

export default React.memo((props: Props) => {
    const { items, onItemClick, onClear } = props;
    const [isConfirmingClear, setIsConfirmingClear] = useState(false);
    const onClearConfirm = useCallback(() => {
        setIsConfirmingClear(false);
        onClear();
    }, []);
    const onClearCancel = useCallback(() => {
        setIsConfirmingClear(false);
    }, []);

    const onClearBtnClick = useCallback(() => {
        setIsConfirmingClear(true);
    }, []);

    if (!items || items.length === 0) {
        return null;
    }

    return (
        <div className="search-history">
            <h3 className="search-history__header frow f-jc-sb f-ai-c">
                搜索历史
                <span className="search-history__clear" onClick={onClearBtnClick} />
            </h3>
            <ul className="search-history__items">
                {items.map(item => (
                    <li
                        key={item}
                        className="search-history__item text--elli"
                        onClick={() => onItemClick(item)}
                    >
                        {item}
                    </li>
                ))}
            </ul>
            <Confirm
                isOpen={isConfirmingClear}
                title="清除全部历史记录？"
                onConfirm={onClearConfirm}
                onCancel={onClearCancel}
            />
        </div>
    );
});
