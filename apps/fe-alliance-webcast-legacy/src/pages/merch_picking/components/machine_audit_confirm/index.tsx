import React, { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from 'react';
import cx from 'classnames';
import Modal from 'components/modal';
import './index.scss';
import {
  isInAweme,
  isInHuoshan,
  isInToutiao,
  isInXigua,
  isAndroid
} from '@src/lib/env_detect';

export interface MachineAuditResponse {
  status_code?: boolean | number;
  status_msg?: string;
  reject_reason?: string;
  reject_type?: number;
  explanation?: string;
  rule_title?: string;
  rule_url?: string;
  questionnaire_title?: string;
  questionnaire_url?: string;
  i_get_it?: string;
}

interface Props {
  title?: string;
  isOpen: boolean;
  onCancel?: MouseEventHandler;
  machineAudit?: MachineAuditResponse;
  isShouldHideApplyVerifyButton?: boolean;
  onConfirm(): void;
  onMachineAuditRuleClick?(title: string, url: string): void;
  curJumpMachineAuditRule?(title: string, url: string): void;
  onMachineAuditPopupConfirm?(title: string): void;
}

export default React.memo((props: Props) => {
  const {
    isOpen,
    title = '添加失败',
    onConfirm,
    machineAudit = {},
    onCancel,
    onMachineAuditRuleClick,
    onMachineAuditPopupConfirm,
    isShouldHideApplyVerifyButton = false,
    curJumpMachineAuditRule
  } = props;
  const {
    reject_reason,
    explanation,
    rule_title = '',
    rule_url = '',
    questionnaire_title = '',
    questionnaire_url = '',
    i_get_it = '我知道了'
  } = machineAudit as MachineAuditResponse;

  const handleClickRule = () => {
    onMachineAuditRuleClick && onMachineAuditRuleClick(rule_title, rule_url);
    curJumpMachineAuditRule && curJumpMachineAuditRule(rule_title, rule_url);
  };
  const handleClickApply = () => {
    onMachineAuditPopupConfirm &&
      onMachineAuditPopupConfirm(questionnaire_title);
    curJumpMachineAuditRule && curJumpMachineAuditRule('', questionnaire_url);
  };
  const confirm = () => {
    onMachineAuditPopupConfirm && onMachineAuditPopupConfirm(i_get_it || '我知道了');
    onConfirm();
  };
  const preventMove = React.useCallback(event => {
    event.preventDefault();
  }, []);

  React.useLayoutEffect(() => {
    setTimeout(() => {
      const mask = document.querySelector('.verify-model__overlay');
      mask &&
        mask.addEventListener('touchmove', preventMove, { passive: false });
    }, 0);
    return () => {
      const mask = document.querySelector('.verify-model__overlay');
      mask && mask.removeEventListener('touchmove', preventMove);
    };
  }, [props.isOpen]);

  return (
    <Modal
      isOpen={isOpen}
      className={cx('verify-modal', {
        'in-hotsoon': isInHuoshan,
        'in-aweme': isInAweme,
        'in-toutiao': isInToutiao,
        'in-video': isInXigua,
        'in-android': isAndroid
      })}
      overlayClassName='verify-model__overlay'
      trackProps={{
        title,
        cancelText: i_get_it || '我知道了',
        confirmText: (!questionnaire_title || isShouldHideApplyVerifyButton) ? '' : questionnaire_title,
      }}
    >
      <img
        src={require('static/images/merch_picking/close.png')}
        className='close-icon'
        onClick={onCancel}
      />
      <h3 className='verify__title'>{title}</h3>
      <div
        className={cx('verify__content', {
          'in-aweme': isInAweme
        })}
      >
        {reject_reason && <div className='reason-type'>{reject_reason}</div>}
        {explanation && <div className='reason-description'>{explanation}</div>}
        {rule_title && (
          <div className='reason-rule'>
            请查看
            <span className='rule-link' onClick={handleClickRule}>
              《{rule_title}》
            </span>
          </div>
        )}
      </div>
      {!questionnaire_title || isShouldHideApplyVerifyButton ? (
        <div className='footer__button' onClick={confirm}>
          {i_get_it || '我知道了'}
        </div>
      ) : (
        <div className='footer__button-groups'>
          <div className='left-button' onClick={handleClickApply}>
            {questionnaire_title}
          </div>
          <div className='right-button' onClick={onConfirm}>
            {i_get_it || '我知道了'}
          </div>
        </div>
      )}
    </Modal>
  );
});
