.verify-model__overlay {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 1000;
    background-color: rgba(0, 0, 0, 0.5);

    .verify-modal {
        width: 80%;
        min-height: 162px;
        height: auto;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        background-color: #ffffff;
        border-radius: 6px;
        padding: 24px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        text-align: center;
        overflow: hidden;
        .close-icon {
            width: 20px;
            height: 20px;
            position: absolute;
            right: 12px;
            top: 12px;
        }
        .verify__title {
            font-family: PingFangSC-Medium, PingFang SC;
            height: 22px;
            font-size: 16px;
            font-weight: 500;
            color: #262626;
            line-height: 22px;
            margin: 0 0 8px;
        }
        .verify__content {
            line-height: 20px;
            color: #262626;
            .reason-description {
                color: #A6A6A6;
                margin-top: 4px;
            }
            .reason-rule {
                margin-top: 8px;
                .rule-link {
                    color: #2681FF;
                }
            }
        }
        .footer__button {
            margin-top: 24px;
            height: 40px;
            background: #ffffff;
            border-radius: 4px;
            border: 1px solid #f04142;
            font-weight: 500;
            color: #f04142;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: PingFangSC-Medium, PingFang SC;
        }
        .footer__button-groups {
            font-family: PingFangSC-Medium, PingFang SC;
            display: flex;
            margin-top: 24px;
            height: 40px;
            background: #ffffff;
            .left-button,
            .right-button {
                display: flex;
                align-items: center;
                justify-content: center;
                flex: 1;
                font-weight: 500;
                border-radius: 4px;
            }
            .left-button {
                border: 1px solid #e8e8e8;
                color: #737373;
            }
            .right-button {
                border: 1px solid #f04142;
                color: #f04142;
                margin-left: 8px;
            }
        }
        &.in-toutiao {
            $color: #f04142;
            .right-button {
                border: 1px solid $color;
                color: $color;
            }
        }
        &.in-super {
            $color: #FF6880;
            .footer__button,
            .left-button,
            .right-button {
                border-radius: 6px;
            }
            .left-button {
                border-color: #DBDBDB;
                color: #666666;
            }
            .right-button {
                border: 1px solid $color;
                color: $color;
            }
        }
        &.in-video,
        &.in-video.in-android {
            .footer__button,
            .right-button {
                background-color: #FF0000;
                border-radius: 6px;
                color: #ffffff;
            }
            .left-button {
                background: rgba(0,0,0,0.04);
                border-radius: 6px;
                color: #737373;
                border: none;
            }
        }
        &.in-aweme {
            padding: 32px 0 0;
            border-radius: 2px;
            .close-icon {
                display: none;
            }
            .reason-rule {
                margin-top: 0;
                .rule-link {
                    color: #04498D;
                }
            }
            .reason-description {
                margin-top: 0;
                color: rgba(22, 24, 35, 0.5);
            }
            .verify__content {
                margin: 0 20px;
                color: rgba(22, 24, 35, 0.75);
            }
            .footer__button {
                box-sizing: border-box;
                border: none;
                border-radius: 0;
                border-top: .5px solid rgba(22, 24, 35, 0.12);
                height: 48px;
                line-height: 48px;
                font-size: 15px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #161823;
            }
        }
        &.in-hotsoon {
            .footer__button {
                background: #F8F8F8;
                border-radius: 16px;
                color: #FF4E33;
                border: none;
            }
        }
    }
}

.xigua .verify-modal {
    .footer__button,
    .right-button {
        background-color: #FF0000;
        border-radius: 6px;
        color: #ffffff;
    }
    .left-button {
        background: rgba(0,0,0,0.04);
        border-radius: 6px;
        color: #737373;
        border: none;
    }
}
