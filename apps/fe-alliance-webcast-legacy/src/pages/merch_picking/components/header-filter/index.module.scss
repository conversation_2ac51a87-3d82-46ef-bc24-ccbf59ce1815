.header-bar {
  min-height: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 18px;

  .quick {
    height: 100%;
    width: calc(100% - 56px);
  }

  .filter-btn {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: rgba(22, 24, 35, .75);
    display: flex;
    align-items: center;

    .icon {
      margin-left: 1px;
      transform: translateY(-2%);
    }

    &.filter-btn-active {
      color: #161823;
      font-weight: 500;
    }
  }
}

// 新版，实验推全 需要删代码的时候直接改为header-bar即可

.header-bar-v2 {
  min-height: 24px;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;

  .quick {
    height: 100%;
    width: calc(100% - 34px);
  }

  .filter-btn {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: rgba(22, 24, 35, .75);
    display: flex;
    align-items: center;

    .icon {
      margin-left: 1px;
      transform: translateY(-2%);
    }

    &.filter-btn-active {
      color: #161823;
      font-weight: 400;
    }
  }
}
