import React, { ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import { Sheets } from '@ecom/auxo-mobile';
import { CustomSheets } from '@alliance-mobile/platform-merchant-common';
import LazyImg from '@src/components/lazy_img';
import { openSchema } from '@src/common/bridge';
import { genSchema } from '@src/lib/util/merch_picking';
import { PublishContinuePushStream } from '@src/lib/util/event';
import { BindResult, SubGuideInfo, batchResultModalInfo } from '../../types';
import { SchemaCommonParams } from '../../constants';
import IMAGE_GOODS from './img/img_default_large.png';
import via from '@ies/webcast-via';
import cx from 'classnames';
import './index.scss';
import showToast from '@src/components/toast';
import { isEmpty } from '@byted-flash/utils';
import { usePersistCallback } from '@byted/hooks';
import IconArrowDown from './img/arrow_down.svg';
import IconArrowUp from './img/arrow_up.svg';
import { sendEvent } from '@alliance-mobile/event';

interface Props {
  show: boolean;
  info: batchResultModalInfo;
  showOversold: boolean;
  onClose: () => void;
  isMerchV2?: boolean;
}
const FailReason = (reasonProps: {
  reason?: string;
  link?: string;
  linkText?: string;
  sub_guide_info?: SubGuideInfo[];
}) => {
  const { reason, link, linkText, sub_guide_info = [] } = reasonProps;
  const subReason = useMemo(() => {
    return sub_guide_info.map(i => `关联商品"${i.product_title}"${i.title}，${i.content}`);
  }, [sub_guide_info]);
  const hasSubReason = useMemo(() => !isEmpty(sub_guide_info) && Boolean(sub_guide_info?.[0]), [sub_guide_info]);

  useEffect(() => {
    sendEvent('fail_reason_show', { fail_reason: reason || '' });
  }, [reason]);
  const handleLink = useCallback(() => {
    if (!link) return;
    // 直播中 跳转全屏页需要主动发送 continue_push_stream 事件，否则直播画面会黑屏
    // PublishContinuePushStream里做了新版本和直播中状态的判断
    PublishContinuePushStream();
    openSchema({
      schema: genSchema({
        ...SchemaCommonParams,
        url: link,
      }),
    });
  }, [link]);
  const firstSubReason = subReason?.[0];
  const [isExpand, setIsExpand] = useState(false);

  const renderReason = useMemo(() => {
    if (hasSubReason && subReason.length > 1) {
      const renderReasons = isExpand ? subReason : [firstSubReason];
      return (
        <div className="detail-card-reason">
          <div className="detail-card-reason__main">
            <div className="detail-card-reason__title">失败:</div>
            <div
              className={cx('detail-card-reason__detail', {
                ellipsis: !isExpand,
              })}>
              {renderReasons.map((r, i) => {
                return (
                  <div className="detail-card-reason__detail__line" key={i}>
                    {r}
                  </div>
                );
              })}
            </div>
            <div
              className="detail-card-reason__expand-button"
              onClick={() => {
                setIsExpand(!isExpand);
              }}>
              {/* eslint-disable-next-line @ecom/ecom-rule/avoid-img */}
              <img width={16} src={isExpand ? IconArrowUp : IconArrowDown} />
            </div>
          </div>
        </div>
      );
    }
    return (
      <div className="detail-card-reason">
        <div className="detail-card-reason__main">
          <div className="detail-card-reason__title">失败:</div>
          <div className="detail-card-reason__detail">
            <div>{firstSubReason || reason}</div>
            {link && (
              <div className="detail-card-reason__link" onClick={handleLink}>
                {linkText || '去查看'}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }, [firstSubReason, handleLink, hasSubReason, isExpand, link, linkText, reason, subReason]);

  if (!reason && !hasSubReason) return null;
  return <>{renderReason}</>;
};

export default React.memo((props: Props) => {
  const { show, info, showOversold, onClose, isMerchV2 } = props;
  const { failureCount = 0, failureList = [], oversoldList = [], partialFailureList = [] } = info;
  const hasPartialFailure = useMemo(() => {
    return Boolean(partialFailureList?.length);
  }, [partialFailureList]);
  const Card = (cardProps: { children?: ReactNode; img: string; title: string; id: string }) => {
    const { children, img, title, id } = cardProps;
    return (
      <div className="detail-card">
        <div className="detail-card-main">
          <LazyImg lazy={false} src={img} className="detail-card-main__goods" failure={IMAGE_GOODS} />
          <div
            style={{
              width: '0',
              flex: '1',
            }}>
            <div className="detail-card-main__title">{title}</div>
            <div
              className="detail-card-main__id"
              onClick={() => {
                via.app.copyToClipboard({
                  content: id,
                });
                showToast('复制成功');
              }}>
              ID:{id}
            </div>
          </div>
        </div>
        {children}
      </div>
    );
  };
  const renderCard = usePersistCallback((item: BindResult) => {
    const subReason = item.guide_info?.sub_guide_info?.map(sub => sub.content || '').filter(Boolean) || [];
    const reason = item.bind_reason || item.guide_info?.content;
    const subIds = item.guide_info?.sub_guide_info?.map(i => i.product_id || '').filter(Boolean) || [];
    return (
      <Card
        key={item.product_id}
        title={item.title}
        id={isEmpty(subIds) ? item.product_id : subIds.join(',')}
        img={item.main_img || ''}>
        <FailReason
          reason={reason}
          sub_guide_info={item.guide_info?.sub_guide_info}
          link={item.guide_info?.guide_link}
          linkText={item.guide_info?.confirm_text}
        />
      </Card>
    );
  });

  const content = (
    <div className="result-detail">
      {showOversold && (
        <>
          <div className="add-result-oversold">
            <div className="result-detail-title add-result-oversold-title">{oversoldList.length}个商品存在超卖风险</div>
            <div className="add-result-oversold-sub-title">
              该类商品为付款减库存(用户支付后减库存)，下单数可能会超过实际库存，有超卖风险，请与商家沟通确认
            </div>
            {oversoldList.map(item => (
              <Card key={item.product_id} title={item.title} img={item.main_img} id={item.product_id} />
            ))}
          </div>
        </>
      )}
      {hasPartialFailure && (
        <>
          <div className="divider" />
          <div className={cx('add-result-failure')}>
            <div className={cx('result-detail-title', 'icon-title__partial_failure')}>
              以下{partialFailureList.length}个聚合商品，部分关联商品添加失败
            </div>
            {partialFailureList.map(renderCard)}
          </div>
        </>
      )}
      {Boolean(failureList?.length) && (
        <>
          <div className="divider" />
          <div className={cx('add-result-failure')}>
            <div className={cx('result-detail-title', 'icon-title__failure')}>以下{failureCount}个商品添加失败</div>
            {failureList.map(renderCard)}
          </div>
        </>
      )}
    </div>
  );
  // 区分新旧版本
  const sheetsComp = isMerchV2 ? (
    <div className={cx('result-detail__container', 'result-detail__container-v2')}>
      <CustomSheets height="100%" onClose={onClose} title="添加结果详情" visible={show} closeIconType="xmark">
        {content}
      </CustomSheets>
    </div>
  ) : (
    <div className="result-detail__container result-detail__container-v1">
      <Sheets visible={show} height="auto" title="添加商品详情" onClose={onClose} onDismiss={onClose}>
        {content}
      </Sheets>
    </div>
  );
  return <>{sheetsComp}</>;
});
