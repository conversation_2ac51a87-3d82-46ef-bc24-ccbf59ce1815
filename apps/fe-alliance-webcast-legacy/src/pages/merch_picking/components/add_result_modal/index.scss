@import "~common/styles/merch_picking/mixins";

// 两个弹窗公用的带icon的标题

.icon-title {

  &__warning,
  &__success,
  &__failure,
  &__partial_failure {
    display: flex;
    align-items: center;
    font-family: "PingFang SC";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: rgba(22, 24, 35, .75);

    &::before {
      content: "";
      display: inline-block;
      flex-shrink: 0;
      width: 16px;
      height: 16px;
      margin-right: 8px;
      background-image: url("./img/icon_success.svg");
    }
  }

  &__warning {

    &::before {
      align-self: baseline;
      margin-top: 1px;
      background-image: url("./img/icon_warning.svg");
    }
  }

  &__failure {

    &::before {
      background-image: url("./img/icon_failure.svg");
    }
  }

  &__partial_failure {

    &::before {
      background-image: url("./img/warn_icon.svg");
    }
  }
}

// 添加结果提示弹窗

.add-result {

  &-failure {
  }

  &-oversold {
    padding-top: 12px;

    &__tips {
      display: flex;
      align-items: center;
      margin-top: 16px;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      color: rgba(22, 24, 35, .6);

      .douyin-checkbox,
      .toutiao-checkbox,
      .xigua-checkbox,
      .huoshan-checkbox {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }
    }

    &.hairline--t::after {
      background-color: rgba(22, 24, 35, .12);
    }

  }

  &-img {
    display: flex;
    margin-top: 8px;
    padding-left: 24px;

    &__goods,
    &__more {
      width: 36px;
      height: 36px;
      border-radius: 4px;
    }

    &__goods {
      margin-right: 8px;
      background: url("./img/img_default_small.png") no-repeat;
      background-size: cover;

      .lazy-img__img {
        border-radius: 4px;
      }
    }

    &__more {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      color: rgba(22, 24, 35, .75);
      background-color: rgba(22, 24, 35, .04);
    }
  }
}

// 添加商品详情弹层样式

.result-detail {
  font-family: "PingFang SC";
  font-style: normal;
  // Sheets的样式补丁

  &__container {

    .douyin-sheets-wrapper,
    .toutiao-sheets-wrapper,
    .xigua-sheets-wrapper,
    .huoshan-sheets-wrapper {
      z-index: 101;

      .douyin-sheets,
      .toutiao-sheets,
      .xigua-sheets,
      .huoshan-sheets {
        max-height: 90%;
        min-height: 60%;
      }

      .douyin-sheets-content,
      .toutiao-sheets-content,
      .xigua-sheets-content,
      .huoshan-sheets-content {
        overflow-y: scroll;
        // 隐藏滚动条
        scrollbar-width: none; /* firefox */
        -ms-overflow-style: none; /* IE 10+ */

        &::-webkit-scrollbar {
          display: none; /* Chrome Safari */
        }
      }
    }
  }

  .icon-title {
  }

  &-title {
    display: flex;
    font-size: 14px;
    font-weight: 400;
    color: rgba(22, 24, 35, .9);
  }

  .detail-card {
    font-weight: 400;
    padding: 12px 0;

    &-main {
      display: flex;

      &__goods {
        flex-shrink: 0;
        width: 72px;
        height: 72px;
        margin-right: 8px;

        .lazy-img__img {
          border-radius: 4px;
        }
      }

      &__title {
        @include text-ellipsis(2);
        height: 40px;
        margin-top: 2px;
        font-size: 14px;
        line-height: 20px;
        word-break: break-all;
        color: #12141a;
      }

      &__id {
        margin-top: 12px;
        font-size: 12px;
        line-height: 16px;
        color: rgba(22, 24, 35, .5);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    &-reason {
      display: flex;
      padding: 4px 8px;
      margin-top: 12px;
      border-radius: 2px;
      font-size: 13px;
      line-height: 16px;
      color: rgba(22, 24, 35, .75);
      background: rgba(22, 24, 35, .03);
      position: relative;

      &__main {
        display: flex;
        position: relative;
        flex: 1;
      }

      &__title {
        flex-shrink: 0;
      }

      &__detail {
        flex: 1;
        margin-left: 4px;
        color: rgba(22, 24, 35, .5);
        position: relative;
        width: 0;

        &.ellipsis .detail-card-reason__detail__line {
          text-overflow: ellipsis;
          overflow: hidden;
          width: 100%;
          white-space: nowrap;
        }
      }

      &__expand-button {
        font-size: 0;
        line-height: 0;
        height: fit-content;
        padding: 4px 8px;
        margin: -4px -8px;
      }

      &__link {
        flex-shrink: 0;
        align-self: center;
        color: #fe2c55;
      }
    }
  }
}

// 新版ui 实验组 isMerchV2

.result-detail {

  .divider {
    background-color: rgba(22, 24, 35, .12);
    height: 1px;
    margin: 8px 0;
  }

  &__container-v2,
  &__container-v1 {

    .custom-sheets {
      max-height: 100%;

      &__header {
        padding-top: 22px;
        height: 56px;
      }

      &__body {
        height: calc(100% - 44px);
        overflow-y: auto;
      }
    }

    .result-detail {

      &-title {
        color: rgba(22, 24, 35, .9);
        font-size: 15px;
        font-weight: 400;
        line-height: 24px;
      }

      &__close-icon {
        width: 24px;
        height: 24px;
        background: url("~static/images/merch_picking/icon_close.svg") no-repeat;
      }
    }

    .add-result {

      &-oversold {
        padding-top: 0;

        &.hairline--t::after {
          background-color: rgba(22, 24, 35, .12);
        }

        &.hairline--b::after {
          background-color: rgba(22, 24, 35, .12);
        }

        &-title {
          color: rgba(22, 24, 35, .9);
          font-size: 15px;
          line-height: 24px;
          margin-bottom: 0;
        }

        &-sub-title {
          color: rgba(22, 24, 35, .5);
          font-size: 12px;
          line-height: 18px;
          padding: 8px 0 4px;
        }
      }

      &-failure {
      }
    }
  }
}

.add-result-dialog-body {

  .add-result-failure {

    .space {
      height: 12px;
    }
  }
}

// 覆写组件库样式。单页应用，不影响其他页面

.merch-picking__add-result-modal {

  .douyin-dialog-box-content {
    border-radius: 12px 12px 0 0;
  }

  .douyin-dialog-operation-wrapper {
    border-radius: 0 0 12px 12px;
  }

  .douyin-dialog-box-text-content {
    padding: 24px 20px;
  }

  .add-result-dialog {
    padding-top: 4px;
  }
}
