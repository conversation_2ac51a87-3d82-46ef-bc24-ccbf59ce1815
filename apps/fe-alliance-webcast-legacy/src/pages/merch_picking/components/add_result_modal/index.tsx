import React, { useState, useEffect, useMemo } from 'react';
import { Checkbox, Dialog } from '@ecom/auxo-mobile';
import webcast from '@bridge/webcast';
import LazyImg from '@src/components/lazy_img';
import IMAGE_GOODS from './img/img_default_small.png';
import ResultDetail from './detail';
import { batchResultModalInfo } from '../../types';
import './index.scss';
import { hookDialog } from '@alliance-mobile/event';
import { batchResultType } from '@src/pages/merch-picking-recommend/constant/types';
hookDialog(Dialog);
const NO_MORE_OVERSOLD_REMINDERS = 'NO_MORE_OVERSOLD_REMINDERS';
const MAX_PIC_COUNT = 4; // 最多显示商品图个数

interface Props {
  show: boolean;
  info: batchResultModalInfo;
  onClose(): void;
  setBatchResult: (batchResult: batchResultType) => void;
  isMerchV2?: boolean;
}
export default React.memo((props: Props) => {
  const { show, info = {} as batchResultModalInfo, onClose, isMerchV2, setBatchResult } = props;
  const { successCount = 0, failureCount = 0, failureList = [], oversoldList = [], partialFailureList = [] } = info;
  const hasPartialFailure = useMemo(() => {
    return Boolean(partialFailureList?.length);
  }, [partialFailureList]);
  const [oversoldCheck, setOversoldCheck] = useState(false);
  const [showDetail, setShowDetail] = useState(false);

  /** 是否展示超卖提醒 */
  const showOversold = useMemo(() => {
    if (!oversoldList.length) return false;
    const lastDate = localStorage.getItem(NO_MORE_OVERSOLD_REMINDERS);
    if (!lastDate) return true;
    return Date.now() - Number(lastDate) > 24 * 60 * 60 * 1000;
  }, [oversoldList?.length]);

  const noMoreOversoldReminders = () => {
    if (!oversoldCheck) return;
    localStorage.setItem(NO_MORE_OVERSOLD_REMINDERS, Date.now().toString());
  };

  /** 点击【我知道了】，处理超卖CheckBox逻辑，关闭当前弹窗 */
  const handleCancel = (params: { needCloseWebview?: boolean }) => {
    const { needCloseWebview } = params || {};
    noMoreOversoldReminders();
    // 清掉缓存
    onClose?.();
    // 关闭webview
    needCloseWebview && webcast.app.close();
  };

  /** 点击【查看详情】，处理超卖CheckBox逻辑，关闭当前弹窗，并呼起结果详情弹层 */
  const handleShowDetail = () => {
    handleCancel?.({
      needCloseWebview: false,
    });
    setShowDetail(true);
  };

  /** 超卖模块 */
  const Oversold = () => {
    if (!showOversold) return null;
    return (
      <div className="add-result-oversold hairline--t">
        <div className="icon-title__warning">
          {oversoldList.length}个商品为付款减库存商品(用户支付后减库存)，有超卖风险，请与商家沟通确认
        </div>
        <div className="add-result-img">
          {oversoldList?.map((item, index) => {
            if (index > MAX_PIC_COUNT - 1) return null;
            return (
              <LazyImg
                lazy={false}
                key={`${index}${item?.main_img}`}
                src={item?.main_img}
                className="add-result-img__goods"
                failure={IMAGE_GOODS}
              />
            );
          })}
          {oversoldList.length > MAX_PIC_COUNT && (
            <span className="add-result-img__more">+{oversoldList.length - MAX_PIC_COUNT}</span>
          )}
        </div>
      </div>
    );
  };
  return (
    <>
      <Dialog
        title="添加结果提示"
        show={show}
        onConfirm={handleShowDetail}
        onCancel={() => {
          handleCancel?.({
            needCloseWebview: true,
          });
        }}
        onDismiss={onClose}
        okText="查看详情"
        cancelText="我知道了"
        className="merch-add-result-dialog"
        renderBody={
          <div className="add-result-dialog-body">
            <div className="add-result-failure">
              <div className="icon-title__success">{successCount}个商品成功添加</div>
              <div className="space" />
              {hasPartialFailure && (
                <>
                  <div className="icon-title__partial_failure">
                    {partialFailureList.length || 0}个聚合品的部分关联商品添加失败
                  </div>
                  <div className="add-result-img">
                    {partialFailureList?.map((item, index) => {
                      if (index > MAX_PIC_COUNT - 1) return null;
                      return (
                        <LazyImg
                          key={index}
                          lazy={false}
                          src={item?.main_img || ''}
                          className="add-result-img__goods"
                          failure={IMAGE_GOODS}
                        />
                      );
                    })}
                    {partialFailureList.length > MAX_PIC_COUNT && (
                      <span className="add-result-img__more">+{partialFailureList.length - MAX_PIC_COUNT}</span>
                    )}
                  </div>
                  <div className="space" />
                </>
              )}
              {Boolean(failureCount) && (
                <>
                  <div className="icon-title__failure">{failureCount}个商品添加失败</div>
                  <div className="add-result-img">
                    {failureList?.map((item, index) => {
                      if (index > MAX_PIC_COUNT - 1) return null;
                      return (
                        <LazyImg
                          key={index}
                          lazy={false}
                          src={item?.main_img || ''}
                          className="add-result-img__goods"
                          failure={IMAGE_GOODS}
                        />
                      );
                    })}
                    {failureList.length > MAX_PIC_COUNT && (
                      <span className="add-result-img__more">+{failureList.length - MAX_PIC_COUNT}</span>
                    )}
                  </div>
                </>
              )}
            </div>

            <Oversold />
            {showOversold && (
              <div className="add-result-oversold__tips">
                <Checkbox type="round" checked={oversoldCheck} onChange={val => setOversoldCheck(val)} />
                24小时内不再提醒超卖风险
              </div>
            )}
          </div>
        }
      />
      <ResultDetail
        show={showDetail}
        info={info}
        showOversold={showOversold}
        isMerchV2={isMerchV2}
        onClose={() => {
          setShowDetail(false);
          webcast.app.close();
        }}
      />
    </>
  );
});
