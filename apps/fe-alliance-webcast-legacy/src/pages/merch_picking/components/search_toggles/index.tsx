import cx from 'classnames';
import React, { <PERSON><PERSON>ventHandler } from 'react';
import './index.scss';

interface Props {
  onComboToggleClick: MouseEventHandler;
}

export default React.memo((props: Props) => {
  const { onComboToggleClick } = props;
  const isComboSearchAvailable = Boolean(onComboToggleClick);

  return (
    <div className="search-toggles frow">
      {!isComboSearchAvailable ? null : (
        <span className="search-toggle--combo" onClick={onComboToggleClick}>
          搜索商品或店铺
        </span>
      )}
    </div>
  );
});
