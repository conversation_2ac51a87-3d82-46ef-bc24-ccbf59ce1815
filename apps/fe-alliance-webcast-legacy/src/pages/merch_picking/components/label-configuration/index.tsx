import React from 'react';
import cx from 'classnames';
import SafeImg from 'components/safe-img';
import { MainProductTag, Merch } from '../../types';
import './index.scss';

export enum HasLabelAction {
  CLICK = 'click',
  HOVER = 'hover',
  NO_ACTION = 'noAction',
}

// 大标签配置化，优先级后端控制
export const renderPicProductTags = (tags = {} as Merch) => {
  const { pic_product_tag = [] } = tags;

  if (!pic_product_tag?.length) {
    return null;
  }

  return pic_product_tag.map((item: MainProductTag) => {
    const { pic, height, width } = item || {};

    if (!pic) {
      return null;
    }

    return (
      <div key={pic} className="label-configuration-pic">
        <SafeImg url={pic} height={height} width={width} />
      </div>
    );
  });
};

// 小标签配置化，优先级后端控制 字体颜色、大小、边框颜色、背景颜色 都带一套常用样式作兜底
export const renderTextProductTags = (tags = {} as Merch) => {
  const { text_product_tag = [] } = tags;

  if (!text_product_tag?.length) {
    return null;
  }

  return text_product_tag?.map((item: MainProductTag) => {
    const { pic, text = {}, text_right = {}, height, border_color } = item || {};
    const { text: mainText, color, bg_color } = text;
    const { text: rightText, color: rightColor, bg_color: rightBgColor } = text_right;

    const keys = Object.keys(item);

    if (!keys?.length || !(mainText || rightText || pic)) {
      return null;
    }

    return (
      <div
        key={mainText}
        className="label-configuration-text label-configuration-mr label-configuration-hairline"
        style={{ '--color': border_color }}>
        <div
          className={cx('label-configuration-text_main', rightText && 'label-configuration-text_border')}
          style={{ color: color, backgroundColor: bg_color, display: 'flex', alignItems: 'center' }}>
          {pic && (
            <div style={{ height }} className="label-configuration-text_main-pic">
              <SafeImg url={pic} height={height} />
            </div>
          )}
          <span>{mainText}</span>
        </div>
        {/* 劵类型非hover展示 */}
        {rightText ? (
          <span
            className="label-configuration-text_right label-configuration-hairline"
            style={{ color: rightColor, backgroundColor: rightBgColor, '--color': border_color }}>
            {rightText}
          </span>
        ) : null}
      </div>
    );
  });
};
