.label-configuration {

  &-mr {
    margin-right: 4px;
  }

  &-pic {
    display: inline-flex;
    height: 15px;
    vertical-align: middle;
    margin-bottom: 2px;
  }

  &-hairline {
    position: relative;
  }

  &-hairline::after {
    border-radius: 4px;
    transform: scale(.5);
    border: 1px solid var(--color);
    width: 200%;
    height: 200%;
    transform-origin: left top;
    content: "";
    display: block;
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
  }

  &-text {
    display: inline-flex;
    align-items: center;
    font-size: 10px;
    line-height: 15px;
    color: #ff701d;
    margin-bottom: 3px;

    &_main {
      padding: 0 3px;
      border-radius: 2px;

      &-pic {
        height: 15px;
        padding-right: -4px;
        display: inline-flex;
        align-items: center;
      }
    }

    &_border {
      border-radius: 2px 0 0 2px;
    }

    &_right {
      padding: 0 3px;
      border-radius: 0 2px 2px 0;

      &.label-configuration-hairline::after {
        border-radius: 0;
        border: 0 dashed transparent;
        border-left: 1px dashed var(--color);
      }
    }
  }
}
