import React, { useRef, useState, useEffect, useMemo } from 'react';
import Confirm from 'components/modal/confirm';
import ModalConfirm from './components/confirm';
import ModalConfirmQualification from './components/confirm_modal'; // 资质升级弹窗 在弹窗里面优先级最高
import AnXinGou from './components/anxingou';
import { NoticeScene, searchMerchList, searchMerchListByLink } from './services/api';
import { appSmallName as appName, isInAweme, isInSAAS } from '@src/lib/env_detect';
import { useAppController } from './modules/controller';
import AppDelegate from './containers/app_delegate';
import PageHeader from './components/page_header';
import PageHeaderPopupMode from './components/page_header/popup_mode';
import MainView from './containers/portal_main';
import LinkSearchView from './containers/link_search';
import ComboSearchView from './containers/combo_search';
import HeaderLink from './containers/header_link';
import HeaderLinkSearch from './containers/header_link/search';
import HeaderLinkSelection from './containers/header_link/selection';
import ClipBoardModal from './components/clipboard_modal';
import SearchToggles from './components/search_toggles';
import ShoppingBag from './components/shopping_bag';
import FaceRecognitionModal from './components/face_recognition_modal';
import { AppContext as IAppContext, MAX_SELECT_COUNT, TOTAL_MAX_SELECT_COUNT, TipType } from './types';
import { TAB_KEYS, SchemaCommonParams, ALERT_CACHE_KEY, REFER_MERCH_CART_SHOPWINDOW_RETURN } from './constants';
import { openSchema } from '@src/common/bridge';
import AdAuthModal from './components/adAuth_modal';
import ConfirmModal from './components/merch_item/components/modal';
import { formatQueryString, getStorage, getStorageString, setStorage } from 'src/lib/util/index';
import { genSchema } from '@src/lib/util/merch_picking';
import { PublishContinuePushStream } from '@src/lib/util/event';
import Checkbox from 'components/checkbox';
import { PairLimitHint } from './components/pair_limit/pair_limit_hint';
import { PairLimitSheets } from './components/pair_limit/pair_limit_sheets';
import { PairLimitConfirmModal } from './components/pair_limit/pair_limit_confirm_modal';
import AddResultModal from './components/add_result_modal';
import uniq from 'lodash/uniq';
import CartMultiStepNotice from './components/cart-multi-step-notice';
import { AlertInfoIcon, AlertCloseIcon, AlterGoIcon } from './components/icon';
import { ErrorBoundary } from '@alliance-mobile/utils/lib/mera';
import via from '@bridge/webcast';
import { registerBtmPage } from '@alliance-mobile/event';
import { subscribeAndListenEvent } from '@alliance-mobile/utils/lib/event';
import toast from '@src/components/toast';
import { getStorageItemByKey, setStorageItem } from '@alliance-mobile/utils/lib/storage';
import { usePersistCallback } from '@byted/hooks';
import { ALL_PAGE_STATUS, PAGE_FETCHES, remewReport } from './perf';
import { initFeelGood } from '@src/common/feel-good';
import AggregatePromotionModal from './components/aggregate-promotion-modal';
import { Alert } from '@ecom/auxo-mobile';
import { isEmpty } from '@byted-flash/utils';
import { jsbEventBus } from '@alliance-mobile/platform-merchant-common/src/utils/event-bus';

registerBtmPage({
  btm: 'a55989.b88328',
});
remewReport.register({
  pageName: '添品',
  btmA: 'a55989',
  btmB: 'b88328',
});
remewReport.initEachPageFetches(PAGE_FETCHES);
remewReport.changePageStatus(ALL_PAGE_STATUS.MERCHED_LIVE_PAGE);

const themeConfigMap: Record<
  string,
  {
    icon: React.ReactNode;
    fontColor: string;
    backgroundColor: string;
    goIcon: React.ReactNode;
    closeIcon: React.ReactNode;
  }
> = {
  warn: {
    icon: <AlertInfoIcon color="#ff6f00" />,
    fontColor: '#ff6f00',
    backgroundColor: '#fff2e0',
    goIcon: <AlterGoIcon color="#ff6f00" />,
    closeIcon: <AlertCloseIcon color="#ff6f00" />,
  },
  default: {
    icon: <AlertInfoIcon color="rgba(22, 24, 35, 0.6)" />,
    fontColor: 'rgba(22, 24, 35, 1)',
    backgroundColor: 'rgba(22, 24, 35, 0.03)',
    goIcon: <AlterGoIcon color="#fe2c55" />,
    closeIcon: <AlertCloseIcon color="#161823" fillOpacity="0.6" />,
  },
};

const App = () => {
  const [isopen, setIsOpen] = useState(false); // 控制风险提醒弹框的显示隐藏
  const [isLazy, setIsLazy] = useState(false); // 控制用户是否勾选反疲劳选项
  const [userInfo, setUserInfo] = useState({ user_id: '' });
  const roomId: number = useMemo(() => {
    const { room_id } = formatQueryString();
    return Number(Array.isArray(room_id) ? room_id[0] : room_id);
  }, []);

  useEffect(() => {
    if (!roomId) {
      initFeelGood('replenish_questionnaire_trigger');
    }
  }, [roomId]);

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  const clipBoardModalRef = useRef({ handleAdd: () => {} });

  const [$app, appState, tabList, appendedList, isAB] = useAppController();

  useEffect(() => {
    via.app.getUserInfo().then(res => {
      setUserInfo(res || { user_id: '' });
    });
  }, []);

  const tabType = appState.activeTab.type;
  const context: IAppContext = {
    appendedMerchList: appendedList,
    appName: appName,
    tab_type: tabType,
  };
  const clipboardPromotionIsPicked =
    appState.clipBoardModalVisible &&
    appendedList.findIndex(id => id === appState.clipboardPromotionInfo.promotion_id) >= 0;
  const searchTabs = tabList.filter(
    tab =>
      tab.key === TAB_KEYS.SHOPWINDOW ||
      tab.key === TAB_KEYS.STORE ||
      tab.key === TAB_KEYS.STAR_ATLAS ||
      tab.key === TAB_KEYS.MAGPIE_BRIDGE ||
      tab.key === TAB_KEYS.PROTOCOL ||
      tab.key === TAB_KEYS.PICKING_CART
  );

  // 资质升级modal 优先级最高
  const showRemindModal = appState.isRemindApiBack && appState.isShowQualificationModal;
  // 除资质升级以外其他弹窗api接口都返回
  const isAllApiBack = appState.isCheckAdBack && appState.isCheckPaste && appState.isCheckUnion;

  // 次优先级弹窗
  const showOthers = appState.isRemindApiBack && !appState.isShowQualificationModal;

  const maxSelectCount = Math.min(Math.max(TOTAL_MAX_SELECT_COUNT - appendedList.length, 0), MAX_SELECT_COUNT);

  const closeAlert = async (tip_type?: TipType) => {
    const cache = (localStorage.getItem(ALERT_CACHE_KEY) ?? '').split(',');
    const tmp = String((await getStorageString(ALERT_CACHE_KEY).catch(() => '')) || '');
    const appCache = tmp.split(',');
    tip_type && cache.push(tip_type?.toString?.() as string);
    tip_type && appCache.push(tip_type?.toString?.() as string);
    localStorage.setItem(ALERT_CACHE_KEY, uniq(cache).join(','));
    await setStorage(ALERT_CACHE_KEY, uniq([...cache, ...appCache]).join(','));
    $app.checkAlert();
  };

  // 获取全局提示信息【顶侧横幅】
  const renderRemindMessage = () => {
    let message = appState.commonAlertInfo || {};
    message = {
      ...message,
    };

    if (!message?.text) return;
    const { text, icon, url, can_del, tip_type, confirm_text, theme = 'warn' } = message;

    const themeConfig = themeConfigMap[theme];
    return (
      <div
        className="header-hint"
        style={{
          color: themeConfig.fontColor,
          background: themeConfig.backgroundColor,
        }}>
        <div className="header-hint-left">
          {Boolean(icon) ? (
            <img className="header-hint__icon" src={icon} />
          ) : (
            <span className="header-hint__icon__default">{themeConfig.icon}</span>
          )}
        </div>
        <div className="header-hint-center">
          <span className="header-hint__text">{text}</span>
          {url && (
            <span
              className="header-hint-link"
              onClick={() => {
                if (!url) return;
                // 直播中 跳转全屏页需要主动发送 continue_push_stream 事件，否则直播画面会黑屏
                // PublishContinuePushStream里做了新版本和直播中状态的判断
                PublishContinuePushStream();
                openSchema({
                  schema: genSchema({
                    ...SchemaCommonParams,
                    url,
                  }),
                });
                return;
              }}>
              {confirm_text ? (
                <span>{confirm_text}</span>
              ) : (
                <span
                  style={{
                    display: 'inline-flex',
                    alignItems: 'center',
                    lineHeight: '1PX',
                  }}>
                  详情{themeConfig.goIcon}
                </span>
              )}
            </span>
          )}
        </div>
        {can_del && (
          <span
            className="header-hint-right"
            onClick={() => {
              closeAlert(tip_type);
            }}>
            <AlertCloseIcon />
          </span>
        )}
      </div>
    );
  };
  // 顶部提示消息
  const renderHeaderHint = () => {
    return (appState.commonAlertInfo && renderRemindMessage()) || <></>;
  };

  const handlePageVisible = usePersistCallback(() => {
    if (document.visibilityState === 'visible' && appState.commonAlertInfo?.tip_name === 'WindowTransfer') {
      getStorageItemByKey(REFER_MERCH_CART_SHOPWINDOW_RETURN).then(res => {
        if (res === 1) {
          setStorageItem({
            key: REFER_MERCH_CART_SHOPWINDOW_RETURN,
            data: '',
          });
          closeAlert(appState.commonAlertInfo?.tip_type);
        }
      });
    }
  });

  useEffect(() => {
    document.addEventListener('visibilitychange', handlePageVisible);
    return () => document.removeEventListener('visibilitychange', handlePageVisible);
  }, []);

  useEffect(() => {
    $app.checkAlert();
    if (isInSAAS) {
      return;
    }
    if (!isInAweme) {
      return;
    }
    // check资质
    $app.qualificationCheck();
    $app.onUpdateAnXinGoInfo();
  }, []);

  useEffect(() => {
    const unsubscribe = subscribeAndListenEvent('ecom.alliance.better_price_replace.live_control', res => {
      // 兼容新版和老版的同款替换商品结构体
      const product = res?.selectedProduct || res?.kolMaterialInfo;
      const betterInfo = product ? JSON.parse(product) : {};
      toast('已添加至直播商品列表');
      via.app.publishEvent({
        eventName: 'submit_webcast_merchpicking',
        params: {
          pickedPromotionIds: [betterInfo?.promotion_id],
        },
      });
    });
    return unsubscribe;
  }, []);

  useEffect(() => {
    const windowReLoad = () => {
      window.location.reload();
    };
    // 从tip提示或拦截会跳转小时达设置
    // 直播设置-小时达-成功后刷新选品车页面
    jsbEventBus.on('ec_live_anchor_instant_setting_change_success', windowReLoad);
    return () => {
      jsbEventBus.off('ec_live_anchor_instant_setting_change_success', windowReLoad);
    };
  }, []);

  /**
   * 超卖风险提醒弹框的确认点击事件
   */
  const handleAdd = () => {
    if (isLazy) {
      const now = new Date().getTime();
      setStorage('PAYMENT_MINUS_INVENTORY', now);
    }
    setIsOpen(false);
    clipBoardModalRef.current.handleAdd();
  };

  /**
   * 风险提醒弹框的反疲劳选项
   */
  const lazyJsx = (
    <div className="pay__reduce--lazy">
      <Checkbox
        label="24小时内不再提醒"
        isChecked={isLazy}
        onChange={value => {
          setIsLazy(value);
        }}
      />
    </div>
  );

  const headerRightList = [
    {
      key: 'selection',
      component: <HeaderLinkSelection onLinkToggleClick={$app.onClickJumpToMerchPickingPage} />,
    },
    {
      key: 'link',
      component: <HeaderLink onLinkToggleClick={$app.onLinkSearchToggleClick} isMerchV2={appState?.isMerchV2} />,
    },
    {
      key: 'search',
      component: <HeaderLinkSearch onLinkToggleClick={$app.onComboSearchToggleClick} />,
    },
  ];

  return (
    <AppDelegate appState={appState} controller={$app} context={context}>
      <div className="page fcol f-fw-nw f-ai-s">
        {/* 区分新旧添品页 */}
        {appState?.isMerchV2 ? (
          <PageHeaderPopupMode title="添加商品" onNavBack={$app.onNavBack}>
            {headerRightList.map(item => (
              <>
                {/* 非抖音端禁止跳转 */}
                {!isInAweme && item.key === 'selection' ? null : (
                  <div key={item.key} className="page_mode-item">
                    {item.component}
                  </div>
                )}
              </>
            ))}
          </PageHeaderPopupMode>
        ) : (
          <PageHeader title="添加商品" onNavBack={$app.onNavBack}>
            <HeaderLink onLinkToggleClick={$app.onLinkSearchToggleClick} />
          </PageHeader>
        )}

        <CartMultiStepNotice scene={NoticeScene.CartSyncLiveList} />
        {renderHeaderHint()}
        {/* 区分新旧添品页 新版添品页 不要此处的搜索条 搜索入口挪到页头里 */}
        {(isInAweme || isInSAAS) && !appState?.isMerchV2 ? (
          <SearchToggles onComboToggleClick={$app.onComboSearchToggleClick} />
        ) : null}

        <MainView
          isAB={isAB}
          tabList={tabList}
          activeTab={appState.activeTab}
          isAuthorityOutdated={appState.isAuthorityOutdated}
          showAddRecommend={appState.showAddRecommend}
          onTabClick={$app.onTabClick}
          onMerchPick={$app.onMerchPick}
          onMerchClick={$app.onMerchClick}
          onMerchTitleClick={$app.onMerchTitleClick}
          onMerchBetterPriceClick={$app.onMerchBetterPriceClick}
          onPairLimitCheck={$app.checkPairLimit}
          maxSelectCount={maxSelectCount}
          selectedList={appState.batchSelectedList}
          sendSelectAllLog={$app.sendSelectAllLog}
          onClearSelectList={$app.onClearSelectList}
          onBatchSelectChange={$app.onBatchSelectChange}
          onBatchAdd={$app.onBatchAdd}
          cancelSelectAllTab={appState.cancelSelectAllTab}
          isMerchV2={appState?.isMerchV2}
          autoSelectAll={appState?.autoSelectAll}
          changeAutoSelectAll={$app.changeAutoSelectAll}
          changeShowAddRecommend={$app.changeShowAddRecommend}
          mixController={$app.mixController}
        />
      </div>
      {/* 新版添品页不要购物袋 */}
      {appState?.isMerchV2 ? null : (
        <ShoppingBag numberOfGoods={context.appendedMerchList.length} onPress={$app.onPressShoppingBag} />
      )}

      <LinkSearchView
        isOpen={appState.isLinkSearchViewActive}
        onMerchPick={$app.onMerchPick}
        onMerchClick={$app.onMerchClick}
        onPairLimitCheck={$app.checkPairLimit}
        onHelpBtnClick={$app.openSearchHelpView}
        searchHandler={searchMerchListByLink}
        onNavBack={$app.onLinkSearchNavBack}
        headerHint={renderHeaderHint()}
        isMerchV2={appState?.isMerchV2}
        userId={userInfo.user_id}
        mixController={$app.mixController}
      />
      <ComboSearchView
        isOpen={appState.isComboSearchViewActive}
        onMerchPick={$app.onMerchPick}
        onMerchClick={$app.onMerchClick}
        onPairLimitCheck={$app.checkPairLimit}
        searchHandler={searchMerchList}
        onNavBack={$app.onSearchNavBack}
        activeTab={appState.activeSearchTab}
        tabList={searchTabs}
        onTabClick={$app.onClickSearchTab}
        searchConfirm={$app.comboSearchConfirm}
        headerHint={renderHeaderHint()}
        isMerchV2={appState?.isMerchV2}
      />
      <ModalConfirmQualification
        title="帐号资质升级"
        content={appState.qualificationMsg.remind_text}
        confirmText="立即升级"
        cancelText="暂不"
        onConfirm={$app.onQualificationModalConfirm}
        onCancel={$app.onQualificationModalCancel}
        isOpen={showRemindModal && isAllApiBack}
      />
      <ModalConfirmQualification
        title="帐号资质升级"
        content={appState.bindQualificationContent}
        confirmText="立即升级"
        cancelText="暂不"
        onConfirm={$app.onBindQualificationModalConfirm}
        onCancel={$app.onBindQualificationModalCancel}
        isOpen={appState.isShowBindQualificationModal}
      />
      <AdAuthModal
        onConfirm={() => {
          $app.onAdModalConfirm(true);
        }}
        onCancel={() => {
          $app.onAdModalConfirm(false);
        }}
        isOpen={showOthers && appState.isShowAdModal && isAllApiBack}
      />
      <Confirm
        title=""
        body={
          <div
            dangerouslySetInnerHTML={{
              __html: appState.merchPickForbiddenReason || '对不起，您没有该题材的带货权限',
            }}
          />
        }
        confirmText="返回"
        onConfirm={$app.onNavBack}
        isOpen={showOthers && appState.isMerchPickForbidden!}
      />
      <ModalConfirm
        content="检测到星图平台有相同商品，只有从星图平台添加，销售数据才会同步给星图客户，是否继续在此添加？"
        confirmText="添加"
        cancelText="取消"
        onConfirm={$app.addGoodNotUseStarAtlas}
        onCancel={$app.hideGoodInStarAtlasModal}
        isOpen={showOthers && appState.isGoodInStarAtlasModalShow}
      />
      <ModalConfirm
        title={appState.accountModalTextInfo?.title}
        content={appState.accountModalTextInfo?.content}
        confirmText={appState.accountModalTextInfo?.confirmText}
        cancelText={appState.accountModalTextInfo?.cancelText}
        onConfirm={$app.onAccountModalConfirm}
        onCancel={$app.onAccountModalCancel}
        isOpen={showOthers && appState.isAccountModalShow}
      />
      <ModalConfirm
        title={appState.commonModal?.title}
        content={appState.commonModal?.content}
        confirmText={appState.commonModal?.confirm_text}
        cancelText={appState.commonModal?.cancel_text}
        onConfirm={$app.onCommonModalConfirm}
        onCancel={$app.onCommonModalCancel}
        isOpen={appState.isShowCommonModal}
      />
      <ClipBoardModal
        isVisible={showOthers && appState.clipBoardModalVisible && !appState.isShowAdModal && isAllApiBack}
        closeModal={$app.onCloseClipboardModal}
        resumeModal={$app.onResumeClipboardModal}
        pickMerch={$app.onMerchPick}
        onPairLimitCheck={$app.checkPairLimit}
        data={appState.clipboardPromotionInfo}
        isPicked={clipboardPromotionIsPicked}
        isAuditing={appState.isClipboardModalAuditing}
        setIsOpen={setIsOpen}
        ref={clipBoardModalRef}
      />
      <ConfirmModal
        title="超卖风险提醒"
        content="此商品为付款减库存（用户支付完成后减库存），实际下单数可能超过当前库存，有超卖风险，请与商家沟通确认"
        confirmText="我知道了"
        onConfirm={handleAdd}
        isOpen={isopen}
        // eslint-disable-next-line react/no-children-prop
        children={lazyJsx}
      />
      <FaceRecognitionModal
        isShow={
          showOthers &&
          !appState?.isPopupLinkModalShow &&
          !appState.clipBoardModalVisible &&
          !appState.isShowAdModal &&
          isAllApiBack
        }
      />
      <ModalConfirm
        img={appState?.popupLinkResult?.contents?.img}
        onCancel={$app?.onClosePopupLinkModal}
        confirmText="去看看"
        cancelText="我知道了"
        content={appState?.popupLinkResult?.contents?.description ?? ''}
        onConfirm={$app.onConfirmPopupLinkModal}
        isOpen={
          showOthers &&
          appState?.isPopupLinkModalShow &&
          !appState.clipBoardModalVisible &&
          !appState.isShowAdModal &&
          isAllApiBack
        }
      />
      {showOthers && isAllApiBack && <AnXinGou ModalInfo={appState.remindMessage?.firstUseAnXinGouModal} />}
      {appState.pairLimitConfirmInfo && (
        <PairLimitConfirmModal $app={$app} info={appState.pairLimitConfirmInfo} onClose={$app.onPairLimitModalClose} />
      )}
      {appState.pairLimitSheetsInfo && (
        <PairLimitSheets
          info={appState.pairLimitSheetsInfo}
          onClose={$app.onPairLimitPopupClose}
          isMerchV2={appState?.isMerchV2}
        />
      )}
      <div className="merch-picking__add-result-modal">
        <AddResultModal
          show={appState.isShowBatchResultModal}
          info={appState.batchResultModal}
          onClose={$app.onHideBatchModal}
          isMerchV2={appState?.isMerchV2}
        />
      </div>
      <AggregatePromotionModal
        aggregatePromotionModalState={appState.aggregatePromotionModalState}
        closeAggrPromotion={$app.mixController.closeAggrPromotion}
      />
    </AppDelegate>
  );
};

export default () => (
  <ErrorBoundary>
    <App />
  </ErrorBoundary>
);
