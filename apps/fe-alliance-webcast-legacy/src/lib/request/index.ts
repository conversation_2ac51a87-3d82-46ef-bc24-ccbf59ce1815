import qs from 'qs';
import { isInApp } from '../env_detect/index';
import { fetch } from '../../common/bridge';
import { isAndroid } from '@src/lib/env_detect';
import { mera } from '@src/lib/util/mera';

export const HOST = 'https://aweme.snssdk.com';
export const API_PREFIX = `${HOST}/aweme`;

const TIME_OUT_SECOND = 20000;
const DEFAULT_STATUS_MESSAGE = '服务器正忙，请稍后重试';

let canUseFetch = fetch;

export interface RequestOptions {
  // 请求方法
  method?: 'post' | 'get';
  // 请求参数
  params?: object;
  data?: object;
  header?: object;
  // 重试次数
  retry?: number;
}

type AdapterData = {
  code?: number;
  status_code?: number;
  message?: string;
  status_msg?: string;
  statusMessage?: string;
  raw?: object;
  response?: object;
};

type AdapterResult = {
  status_code?: number;
  isSuccess?: boolean;
  status_msg?: string;
};

export type ApiWrapper = {
  status_code: number;
  status_message: string;
};

export async function mFetch(url: string, options?: RequestOptions): Promise<any> {
  const { params, method = 'GET', retry = 1 } = options || {};
  const dataAdapter = (data: AdapterData = {}): AdapterResult => {
    let target = data;
    // 抖音宿主容器
    if (target.response) {
      target = target.response;
    }
    // webcast容器
    if (target.raw) {
      target = target.raw;
    }
    const { code, status_code, msg, message, status_msg, statusMessage, ...restProps } = target;
    const result = restProps as AdapterResult & { [key: string]: unknown };
    result.status_code = typeof code !== 'undefined' ? code : status_code;
    result.status_msg = msg || message || status_msg || statusMessage;
    result.isSuccess = result.error === false;
    const apiFilterCode = new Set([-2, 0]);
    if (result.status_code !== undefined && apiFilterCode.has(result.status_code)) {
      result.isSuccess = true;
      // 上报接口事件（统计非0）
      mera?.sendAjaxStatus?.(result?.status_code, url, {
        msg: result?.status_msg,
        logId: result?.log_id || '',
        method,
      });
    } else {
      result.status_msg = result.status_msg || DEFAULT_STATUS_MESSAGE;
    }
    return result;
  };

  if (!isInApp) {
    // 使用window.fetch
    canUseFetch = ({ url, params, method = 'GET', header }) => {
      method = method.toLocaleUpperCase();
      const options = {} as any;
      const paramsString = qs.stringify(params);
      if (method === 'POST') {
        options.body = paramsString;
      } else if (paramsString) {
        const symbol = url.indexOf('?') > -1 ? '&' : '?';
        url = `${url}${symbol}${paramsString}`;
      }
      return window
        .fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            ...header,
          },
          ...options,
          credentials: 'include',
        })
        .then(res => res.json());
    };
  }
  let timeId;

  const resetTimer = () => {
    if (timeId) {
      clearTimeout(timeId);
    }
    const timeOutErrorMsg = `${url}接口超时`;
    return new Promise((resolve, reject) => {
      timeId = setTimeout(() => {
        reject({ status_code: -5, status_msg: timeOutErrorMsg });
      }, TIME_OUT_SECOND);
    });
  };

  const invoke = async () => {
    for (let i = 0; i < retry; i++) {
      try {
        let data = {};
        if (isAndroid && method.toUpperCase() === 'POST' && params) {
          data = params;
        }
        const resp = await canUseFetch({ url, params, method, data });
        const adaptedResult = dataAdapter(resp);
        if (adaptedResult.isSuccess) {
          timeId && clearTimeout(timeId);
          return adaptedResult;
        } else {
          throw adaptedResult;
        }
      } catch (error) {
        if (i !== retry - 1) {
          continue;
        }

        const adaptedResult = dataAdapter(error);
        if (timeId) {
          clearTimeout(timeId);
        }
        throw adaptedResult;
      }
    }
  };
  return await Promise.race([invoke(), resetTimer()]);
}
