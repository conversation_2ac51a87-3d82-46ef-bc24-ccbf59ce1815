// @ts-ignore
import via from '@ies/webcast-via';
import { ApiResponse } from 'types/request';
import { isAndroid } from '@src/lib/env_detect';
import { tecStatCollector, TEC_STAT_MODULE_TYPE } from '@src/lib/util';
import { mera } from '@src/lib/util/mera';

interface FetchParams {
  url: string;
  data?: object;
  params?: object;
  method?: string;
  newInterface?: boolean;
}

interface JsbFetchResponse<T> {
  raw?: T;
  response?: T;
}

const defaultResponse: ApiResponse = {
  status_code: -1,
  status_msg: '网络错误，请重试',
};

function fetchWithoutPackage<T extends ApiResponse>(data: FetchParams): Promise<JsbFetchResponse<T>> {
  return new Promise((resolve, reject) => {
    window.ToutiaoJSBridge &&
      window.ToutiaoJSBridge.call('fetch', data, (response: JsbFetchResponse<T>) => {
        if (response) {
          resolve(response);
        }
      });
  });
}

export default async function fetch<T extends ApiResponse>({
  url,
  params = {},
  method = 'GET',
}: FetchParams): Promise<T> {
  const fetchParams: FetchParams = { url, method };
  if (isAndroid && method.toUpperCase() === 'POST') {
    fetchParams.data = params;
  } else {
    fetchParams.params = params;
  }
  const initTimeStamp = new Date().getTime();
  const isUseViaFetch = initTimeStamp % 2 === 0;
  const mFetch = isUseViaFetch ? via.app.fetch : fetchWithoutPackage;

  const response: JsbFetchResponse<T> = await mFetch(fetchParams);
  const intervalTs = new Date().getTime() - initTimeStamp;
  tecStatCollector({
    interval: intervalTs,
    moduleName: TEC_STAT_MODULE_TYPE.API_INTERVAL,
    apiPath: url,
    extra: isUseViaFetch ? 'via' : 'window',
  });
  const adaptData = adaptResponse<T>(response);
  // 上报接口事件（统计非0）
  mera?.sendAjaxStatus?.(adaptData?.status_code, url, {
    msg: adaptData?.status_msg,
    logId: adaptData?.log_id || '',
  });
  mera?.sendCustom(
    'ajax',
    {
      ajax: intervalTs,
    },
    {
      url,
      method,
      msg: adaptData?.msg,
      code: adaptData?.status_code,
      http_code: adaptData?.code,
      logId: adaptData?.log_id || '',
      requestParams: fetchParams?.data || fetchParams?.params,
      responseData: adaptData?.data || adaptData || {},
    }
  );
  // @ecom/pine-logger插件网络事件打点
  mera?.slardar?.('pineRequestReport', {
    request: fetchParams,
    response: response,
    requestTime: initTimeStamp,
    responseTime: new Date().getTime(),
  });
  if (adaptData.status_code !== 0) {
    return Promise.reject(adaptData);
  }
  return Promise.resolve(adaptData);
}

function adaptResponse<T extends ApiResponse>(res: JsbFetchResponse<T>): T {
  const { raw: response = defaultResponse } = res;
  // 头条response的raw为空
  if (!res.raw && res.response) {
    let { response } = res;
    if (typeof response === 'string') {
      response = JSON.parse(response);
    }
    const { code, status_code, status_msg, msg } = response as T;

    const DEFAULT_MSG = '网络异常，请重试';
    const sc = code === undefined ? status_code : code;
    const sm = msg === undefined ? status_msg : msg || DEFAULT_MSG;
    response.status_code = sc;
    response.status_msg = sm;
    return response as T;
  }
  const { code, status_code, status_msg, msg } = response;
  const DEFAULT_MSG = '网络异常，请重试';
  const sc = code === undefined ? status_code : code;
  const sm = msg === undefined ? status_msg : msg || DEFAULT_MSG;
  response.status_code = sc;
  response.status_msg = sm;
  return response as T;
}
