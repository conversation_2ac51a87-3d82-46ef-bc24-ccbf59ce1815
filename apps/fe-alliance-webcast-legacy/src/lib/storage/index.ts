import { setNativeItem, getNativeItem } from '../../common/bridge';

class Store {
  set(key, val) {
    const value = JSON.stringify(val);
    return setNativeItem({ key, value });
  }

  get(key) {
    return new Promise((resolve, reject) => {
      getNativeItem({ key })
        .then(res => {
          const data = JSON.parse(res || '{}');
          resolve(data);
        })
        .catch(err => {
          resolve({});
        });
    });
  }

  // 置空
  remove(key) {
    return setNativeItem({ key, value: JSON.stringify({}) });
  }
}

export default new Store();