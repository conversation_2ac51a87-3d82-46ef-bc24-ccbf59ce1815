import Utilx, { APP_NAME_MAP } from '@byted/fe-utilx';
import { isInAweme, isInToutiao, isInToutiaoLite, isInXigua } from '@byted-flash/utils';

export const utilInstance = new Utilx({
  defaultLanguage: 'zh',
  ua: window.navigator.userAgent, // 内部的 UA 获取有问题，需要外部传入
});

export const {
  isRN,
  inApp,
  osName,
  osVersion,
  appName,
  queryParamsFromGlobal,
  appVersion,
  isAndroid,
  isIOS,
  isIPhoneX,
  currentLang,
  region,
  channel,
  isTestApp,
  osVersionCompareNumber,
  appSmallName = '',
} = utilInstance;

export const getQuery = <T>() => queryParamsFromGlobal as T;

export const isLynx = typeof lynx !== 'undefined';
export const isWeb = typeof window !== 'undefined';

export const globalProps = isLynx ? lynx.__globalProps : (window as any).__globalProps;

export const isInApp = inApp;

export const isInHuoshan = appSmallName === APP_NAME_MAP.hotsoon;

export { isInAweme, isInToutiao, isInToutiaoLite, isInXigua };

export const isAweme = isInAweme;

export const isInAwemeLite = appName === 'aweme_lite';

// 用于打点记录rn转web的手机系统的参数
export const osPlatform = isIOS ? 'ios' : isAndroid ? 'android' : 'web';

export const statusBarHeight = isRN ? utilInstance.statusBarHeight : 0;

const safeAreaValue = isIPhoneX ? 44 : 0;
export const safearea = {
  top: safeAreaValue,
  bottom: safeAreaValue,
};

export const footerHeight = isIPhoneX ? 17 : 0;

export function getAppnameFromEnv() {
  // auxo-mobile 配置
  // douyin、huoshan、pipixia、xigua、toutiao、doudian（部分端主题还在开发，使用前先沟通）
  if (isInToutiao || isInToutiaoLite) {
    return 'toutiao';
  }
  if (isInXigua) {
    return 'xigua';
  }
  if (isInHuoshan) {
    return 'huoshan'; // 修复火山样式问题
  }
  return 'douyin';
}

export function getUrlByEnv(prodUrl: string) {
  const boeSuffix = '.boe-gateway';
  const { hostname } = window.location;
  try {
    if (hostname.includes(boeSuffix)) {
      const url = new URL(prodUrl);
      url.protocol = 'http:';
      if (!url.hostname.includes(boeSuffix)) {
        const arr = hostname.split(boeSuffix).reverse().filter(Boolean);
        const suffix = boeSuffix + arr[0] || '';
        url.hostname = url.hostname + suffix;
      }
      return url.toString();
    }
  } catch {
    return prodUrl;
  }
  return prodUrl;
}

export function getCurrentPageInfo() {
  const boeSuffix = '.boe-gateway';
  const { hostname, href } = window.location;
  if (hostname.includes(boeSuffix)) {
    return `boe: ${href}`;
  } else {
    return `ppe: ${href}`;
  }
}

export function getAppNameString() {
  if (isInToutiao || isInToutiaoLite) {
    return '今日头条/西瓜视频';
  }

  if (isInXigua) {
    return '西瓜视频/今日头条';
  }
  return '抖音';
}

// 非抖火极+带了SaaS字段，才被认为是SaaS环境
// saas环境下user_id是加密过有字母的字符串
export const isInSAAS = !isInAweme && !isInHuoshan && !isInAwemeLite && /[a-zA-Z]/.test(queryParamsFromGlobal.user_id);
