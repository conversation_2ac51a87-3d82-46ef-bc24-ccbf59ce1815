// for fix error. https://slardar.bytedance.net/node/browser_detail/jserr/detail?bid=alliance_h5&issue_id=e5955ccbf55d3018169ba2e234009536
export function fixResizeObserverError() {
    // @ts-ignore
    if (window && window.ResizeObserver) {
        let div = document.querySelector('div');
        let observerStarted = true;

        // @ts-ignore
        let resizeObserver = new window.ResizeObserver(entries => {
            if (observerStarted) {
                observerStarted = false;
                return;
            }

            resizeObserver.disconnect();

            // [Operation that would cause resize here]
            observerStarted = true;
            requestAnimationFrame(() => resizeObserver.observe(div));
        });

        resizeObserver.observe(div);
    }
}
