interface EventListenerOptions {
    passive?: boolean;
    capture?: boolean;
}

export const isPassiveEventSupported = (() => {
    let isSupported = false;
    try {
        // @ts-ignore
        window.addEventListener('passive', null, {
            get passive() {
                // eslint-disable-next-line no-return-assign
                return (isSupported = true);
            }
        });
    } catch (err) {
        //
    }
    return () => isSupported;
})();

export function coerceEventOptions(options: EventListenerOptions = {}) {
    const passive = isPassiveEventSupported();
    return passive ? options : Boolean(options.capture);
}

export function on(
    el: HTMLElement,
    eventName: string,
    handler: EventListenerOrEventListenerObject,
    options?: EventListenerOptions
) {
    el.addEventListener(eventName, handler, coerceEventOptions(options));
}

export function off(
    el: HTMLElement,
    eventName: string,
    handler: EventListenerOrEventListenerObject,
    options?: EventListenerOptions
) {
    el.removeEventListener(eventName, handler, coerceEventOptions(options));
}

export function scrollableAxisOf(el: HTMLElement) {
    const negative = /visible|hidden/;
    const style = getComputedStyle(el);
    const x = negative.test(style.overflowX || '') ? '' : 'x';
    const y = negative.test(style.overflowY || '') ? '' : 'y';
    return `${x}${y}`;
}

export function findScrollContainer(el: HTMLElement | null, root?: HTMLElement, axis = /x|y/) {
    let node = el && el.parentElement;
    const html = document.documentElement;

    // @ts-ignore
    while (node && node !== window && node !== html) {
        if (axis.test(scrollableAxisOf(node))) {
            return node;
        }
        if (node === root) {
            break;
        }
        node = node.parentElement;
    }

    return null;
}

export function measureRect(rect: Partial<ClientRect>): ClientRect {
    const { left = 0, right = 0, top = 0, bottom = 0 } = rect;
    const width = right - left;
    const height = bottom - top;
    return { width, height, left, right, top, bottom };
}

export function expandRect(rect: ClientRect, margin: number[]) {
    const [top, right, bottom, left] = margin;
    return measureRect({
        top: rect.top + top,
        right: rect.right + right,
        bottom: rect.bottom + bottom,
        left: rect.left + left
    });
}

export function getBoundingClientRect(node: HTMLElement) {
    let rect;

    if (node && node.getBoundingClientRect) {
        rect = node.getBoundingClientRect();
        // @ts-ignore
    } else if (node === window) {
        const body = document.body;
        const html = document.documentElement;
        rect = {
            top: 0,
            left: 0,
            right: html.clientWidth || body.clientWidth,
            bottom: html.clientHeight || body.clientHeight
        };
    } else {
        rect = { top: 0, right: 0, bottom: 0, left: 0 };
    }

    return measureRect(rect);
}

export function computeIntersection(rect1: ClientRect | null, rect2: ClientRect | null) {
    if (!rect1 || !rect2) return null;

    const top = Math.max(rect1.top, rect2.top);
    const right = Math.min(rect1.right, rect2.right);
    const bottom = Math.min(rect1.bottom, rect2.bottom);
    const left = Math.max(rect1.left, rect2.left);

    if (right >= left && bottom >= top) {
        return measureRect({ top, right, left, bottom });
    }

    return null;
}

export function computeViewIntersection(
    root: HTMLElement,
    target: HTMLElement,
    targetRect: ClientRect
) {
    // display: none
    if (!target.offsetParent) return null;

    let intersection = null;
    let prevIntersection: ClientRect | null = targetRect;
    let parent = findScrollContainer(target, root);

    while (parent) {
        const parentRect = getBoundingClientRect(parent);
        intersection = computeIntersection(prevIntersection, parentRect);

        prevIntersection = intersection;
        parent = findScrollContainer(parent, root);
    }

    return intersection;
}
