import * as dom from '../dom';
import ScrollObserver from './observer';
import coerceOptions, { Options } from './options';

const registry = new WeakMap();

function findAncestors(el: HTMLElement | null) {
    const ancestors = [];
    // eslint-disable-next-line no-param-reassign
    while ((el = dom.findScrollContainer(el))) {
        ancestors.push(el);
    }
    return ancestors;
}

function observerOf(container: HTMLElement, throttle: number, register = false) {
    let observers = registry.get(container);
    if (register && (!observers || !observers.has(throttle))) {
        observers = observers || new Map();
        registry.set(container, observers);
        observers.set(throttle, new ScrollObserver(container, throttle));
    }
    return observers ? observers.get(throttle) : null;
}

/* eslint-disable prefer-template */
export function observe(el: HTMLElement, callback: Function, opts?: Options) {
    const options = coerceOptions(opts);
    const ancestors = findAncestors(el);

    const observers = ancestors.map(container => {
        const observer = observerOf(container, options.throttle, true);
        observer.observe(el, callback, options);
        return observer;
    });

    if (observers[0]) {
        observers[0].digest(el);
    }
}

export function unobserve(el: HTMLElement, callback: Function, opts?: Options) {
    const options = coerceOptions(opts);
    const ancestors = findAncestors(el);
    ancestors.forEach(container => {
        const observer = observerOf(container, options.throttle);
        if (observer) {
            observer.unobserve(el, callback);
        }
    });
}

export function subscribe(container: HTMLElement, callback: Function, opts = {}) {
    const options = coerceOptions(opts);
    const observer = observerOf(container, options.throttle, true);
    observer.subscribe(callback, options);
}

export function digestTarget(target: HTMLElement, opts = {}) {
    const options = coerceOptions(opts);
    const container = dom.findScrollContainer(target);
    const observer = container && observerOf(container, options.throttle);
    if (observer) {
        observer.digest(target);
    }
}
