import * as dom from '../dom';

type ListenerMap = Map<Function, any>;
type IntersectCallback = (bounding: ClientRect | null) => void;

interface IThrottleConfig {
    leading?: boolean;
}

export function throttle(fn: Function, ms: number, config?: IThrottleConfig) {
    let timer: number | null;
    const leadingEdge = !!(config && config.leading);

    return function wrapped() {
        if (!timer) {
            if (leadingEdge) {
                fn();
            }

            // @ts-ignore
            timer = setTimeout(() => {
                if (!leadingEdge) {
                    fn();
                }
                timer = null;
            }, ms);
        }
    };
}

export default class ScrollObserver {
    private root: HTMLElement;
    private targets: Map<HTMLElement, ListenerMap>;
    private subscriptions: ListenerMap;

    public constructor(root: HTMLElement, ms: number) {
        this.root = root;
        this.targets = new Map();
        this.subscriptions = new Map();
        const scrollHandler = throttle(() => this.digest(), ms);
        dom.on(this.root, 'scroll', scrollHandler, { passive: true });
    }

    public observe(el: HTMLElement, callback: IntersectCallback, options = {}) {
        const queue = this.targets.get(el) || new Map();
        this.targets.set(el, queue.set(callback, options));
    }

    public unobserve(el: HTMLElement, callback: IntersectCallback) {
        const queue = this.targets.get(el);
        if (queue) {
            queue.delete(callback);
            if (queue.size === 0) {
                this.targets.delete(el);
            }
        }
    }

    public subscribe(callback: Function, options: any) {
        this.subscriptions.set(callback, options);
    }

    public unsubscribe(callback: Function) {
        this.subscriptions.delete(callback);
    }

    public digest(target?: HTMLElement) {
        const { root } = this;

        const runTargetQueue = (queue: ListenerMap, el: HTMLElement) => {
            const elRect = dom.getBoundingClientRect(el);
            queue.forEach((options, callback) => {
                const expandedRect = dom.expandRect(elRect, options.margin);
                callback(dom.computeViewIntersection(root, el, expandedRect), this);
            });
        };

        if (!target) {
            this.targets.forEach(runTargetQueue);
            this.subscriptions.forEach((_, cb) => cb(this));
        } else if (this.targets.has(target)) {
            runTargetQueue(this.targets.get(target) as ListenerMap, target);
        }
    }
}
