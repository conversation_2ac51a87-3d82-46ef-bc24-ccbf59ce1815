## API List

### [index.js](./index.js)
#### `observe(el, callback[, options])`
> observe the scroll event of the scrolling container of el, with a callback

- `el`: HTMLElement
- `callback`: Function
- `options.throttle = 80`: millisecond for scroll event throttle
- `options.margin = [top, right = -top, bottom = -top, left: -right]`: spread margin relative to the scrolling container, applied to intersection calculation.

#### `unobserve(el, callback, throttle = 80)`
> unobserve an el
