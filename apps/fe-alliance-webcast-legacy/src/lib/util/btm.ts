import { createBtm<PERSON>hain } from '@alliance-mobile/event';
export interface JSBridgeResponse {
  code?: number; // 回调结果
  data?: Record<string, string>;
}

interface btm_params {
  btm: string;
  enter_new_page: boolean;
  chain_length?: number;
}

export const createBtmChainOrigin = (params: btm_params): Promise<JSBridgeResponse> | undefined => {
  try {
    return createBtmChain(params.btm, params.enter_new_page, {
      ...params,
      target_pages: [],
    });
  } catch (e) {}
};
