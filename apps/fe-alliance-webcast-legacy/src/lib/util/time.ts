/**
 * 格式化时间戳
 * @param {number} timeStamp 时间戳 单位是秒
 * @param {string} template example: 'yy-mon-dd   hh:min'
 * @return {string} example 2019-01-29 12:12
 */
export const formatTime = (timeStamp, template = 'yy-mm-dd hh:min') => {
  const formatNum = num => (num >= 10 ? num : `0${num}`);
  const formatTimestamp = new Date(timeStamp * 1000);
  const year = formatTimestamp.getFullYear();
  const month = formatNum(formatTimestamp.getMonth() + 1);
  const day = formatNum(formatTimestamp.getDate());
  const hour = formatNum(formatTimestamp.getHours());
  const minute = formatNum(formatTimestamp.getMinutes());
  // return `${year}-${month}-${day}   ${hour}:${minute}`
  return template
    .replace('yy', year.toString())
    .replace('mm', month)
    .replace('dd', day)
    .replace('hh', hour)
    .replace('min', minute);
};
