/* eslint-disable @typescript-eslint/ban-ts-comment */
import qs from 'qs';
import { isIOS, appSmallName, queryParamsFromGlobal as query } from '../env_detect';

import { SchemaProtocol, SchemaConfig, SwipeMode, SchemaBoolean } from 'types/schema';

type Params = Record<string, number | string | boolean>;

type WebviewParamsAdapters = Record<string, (params: SchemaConfig) => void>;

export function appendQuery(url: string, params: Params, override = true) {
  // 把params追加到url中作为参数
  const hash = (url.match(/#(.*)$/) || [''])[0];
  // @ts-ignore
  const [_, mainURL, _query] = url.match(/^([^?#]*)\??([^#]*)/);
  // eslint-disable-next-line @typescript-eslint/no-shadow
  const query = qs.parse(_query);
  Object.assign(query, params, override ? null : query);
  const _qs = qs.stringify(query);
  return `${mainURL}${_qs ? `?${_qs}` : ''}${hash}`;
}

// 不同端的参数命名可能不一致，需要做转换
const webviewParamsAdapters: WebviewParamsAdapters = {
  SUPER(params: SchemaConfig) {
    if (params.hide_back_button) {
      params.transparent_bg_back_icon = SchemaBoolean.NO;
    }
    if (isIOS) {
      params.need_safe_area = SchemaBoolean.YES;
    }
  },
};

export function genSchema(config: SchemaConfig): string {
  const APP_NAME = appSmallName.toUpperCase();
  const {
    url,
    title,
    hide_bar,
    hide_back_button = hide_bar,
    hide_back_close = hide_bar,
    hide_close_btn = 1,
    hide_status_bar,
    status_bar_color,
    status_font_dark,
    bounce_disable,
    nav_bar_color,
    title_color,
    loading_bgcolor,
    swipe_mode = SwipeMode.EDGE,
    isWebCast = false,
    ...params
  } = config;

  // 如果url不是https?协议则直接返回url
  if (!/^https?:/.test(url)) {
    return url;
  }

  // webview的schema参数
  const webviewParams: SchemaConfig = {
    url: appendQuery(url, params),
    title,
    hide_bar,
    hide_status_bar,
    status_bar_color,
    status_font_dark,
    hide_back_button,
    bounce_disable,
    hide_back_close,
    hide_close_btn,
    nav_bar_color,
    title_color,
    loading_bgcolor,
    swipe_mode,
    ...params,
  };

  if (webviewParamsAdapters[APP_NAME]) {
    webviewParamsAdapters[APP_NAME](webviewParams);
  }

  // @ts-ignore
  const schemaProtocol = SchemaProtocol[APP_NAME] || SchemaProtocol.XIGUA;
  const webviewContainer = isWebCast ? 'webcast_webview' : 'webview';
  return `${schemaProtocol}//${webviewContainer}?${qs.stringify(webviewParams)}`;
}

// (1234, 1000, 'km') => 1.2km
// (51000, 10000, '万', 2) => 5.1万
// (56789, 10000, '万', 2) => 5.68万
export function toUnitString(value: number, unit: number, unitSymbol: string, roundedDigits = 1): string {
  if (value < unit) {
    return String(value);
  }

  const fixed = (value / unit).toFixed(roundedDigits);

  return `${fixed.replace(/0+$/, '').replace(/\.$/, '')}${unitSymbol}`;
}

export function loadImg(src: string, attempts = 2) {
  return new Promise(function load(resolve, reject) {
    // eslint-disable-next-line no-param-reassign
    if (--attempts < 0) {
      reject(new Error(`load img: '${src}' failed.`));
    } else {
      const image = new Image();
      image.onload = () => resolve(src);
      image.onerror = () => load(resolve, reject);
      image.src = src;
    }
  });
}

export const getIsLiving = () => {
  const enterFrom = query.enter_from;
  switch (enterFrom) {
    case 'webcast_living':
      return true;
    case 'webcast_before_living':
    default:
      return false;
  }
};

interface GenParams {
  url?: string;
  options?: { [key: string]: string | number };
  params?: { [key: string]: string | number };
  isOpenLynxView?: boolean;
}

export const isLiving = getIsLiving();
