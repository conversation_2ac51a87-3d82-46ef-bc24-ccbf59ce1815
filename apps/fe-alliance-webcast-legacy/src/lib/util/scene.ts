/*
 * @Author: xuanxia<PERSON><EMAIL>
 * @Date: 2023-06-20 19:30:04
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-07-11 20:01:34
 * @Description: file content
 */
import { queryParamsFromGlobal as query } from '../env_detect';
import { PAGE_MODE } from '../../common/constants/page';
// 获取页面形式（全屏 99分屏 73分屏）
export const getPageMode = () => {
  const enterFrom = query?.enter_from;
  return PAGE_MODE[enterFrom];
};
