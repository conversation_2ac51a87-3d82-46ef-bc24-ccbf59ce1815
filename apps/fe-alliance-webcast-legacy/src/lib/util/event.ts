import webcast from '@bridge/webcast';
import { JSBridgeResponse } from 'src/types/jsb';
import { queryParamsFromGlobal as query, globalProps } from '@src/lib/env_detect';
import { MerchPickingEnterFromType } from '@src/types/common';

// 直播中 浮层形式的添品页 跳转其他全屏页需要主动发送 continue_push_stream 事件，否则直播画面会黑屏
export const PublishContinuePushStream = (): Promise<JSBridgeResponse> => {
  try {
    const isPopupType = globalProps?.queryItems?.type === 'popup';
    if (isPopupType && query?.enter_from === MerchPickingEnterFromType.WebcastLiving) {
      return webcast.app.publishEvent({
        eventName: 'ecom.anchor.continue_push_stream',
        params: {},
      });
    }
  } catch (error) {}
  return new Promise(resolve => {
    resolve({
      code: 0,
    });
  });
};
