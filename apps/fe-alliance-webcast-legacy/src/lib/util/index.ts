import qs from 'qs';
import via from '@ies/webcast-via';
import { TecStatCollectorParams } from './types';
import { setNativeItem, getNativeItem, removeNativeItem } from '../../common/bridge';

const defaultUnit = { '10^4': '万', '10^8': '亿' };
export const solveDataNumFunc = (number, unit = {}) => {
  unit = { ...defaultUnit, ...unit };

  if (!number || number <= 0 || isNaN(number) || typeof number !== 'number') {
    return 0;
  }
  if (number < 10000) {
    return number;
  }
  if (number < 100000000) {
    return `${(number / 10000).toFixed(1)}w`;
  }
  return `${(number / 100000000).toFixed(1)}亿`;
};

// schema中追加参数，只追加到顶层参数，不会追加内嵌参数，如
// aweme://webview?url=xxx 则只会给aweme加参数而不会给url的值追加
export function mergeSchemaParams(schema, params) {
  const queryIndex = schema.indexOf('?');
  const base = queryIndex === -1 ? schema : schema.slice(0, queryIndex);
  const query = queryIndex === -1 ? {} : qs.parse(schema.slice(queryIndex + 1));
  // eslint-disable-next-line @ies/eden/max-calls-in-template
  return `${base}?${qs.stringify(Object.assign({}, query, params))}`;
}

/**
 * 截断字符串
 * @param {string} str
 * @param {number} max
 * @param {string} type - 默认...
 * @return {string} 截断后的字符
 */
export function ellipsis(str: string, max: number, type = '...') {
  const len = str.length;
  if (len > max) {
    str = `${str.slice(0, max)}${type}`;
  }
  return str;
}

/**
 * 一维数组升2维
 * @param {array} arr
 * @param {number} num
 */
export const ascendingDimension = (arr: Array<any>[], num = 3) => {
  const newArr = [] as any[];
  arr.forEach((item, index) => {
    const target = Math.floor(index / num);
    if (!newArr[target]) {
      newArr[target] = [];
    }
    newArr[target].push(item);
  });
  return newArr;
};

/**
 * 获取类 url 字符串里面的参数
 * @param {string} 默认window.location.href
 * @return {object} {}
 */
export function formatQueryString(string: string = window.location.href) {
  if (!string) {
    return {};
  }
  let query = string;
  if (query.indexOf('?') > 0) {
    query = query.split('?')[1];
  }
  return qs.parse(query, {
    decoder: decodeURIComponent,
  });
}

/**
 * 监听页面可见性变化
 * @param url
 */
export const PageVisibilityChangeListener = class {
  stateName?: string;
  eventName?: string;
  handleShow?: Function;
  handleHide?: Function;
  handleRemove?: Function;

  constructor(props) {
    this.handleShow = props.handleShow;
    this.handleHide = props.handleHide;
    this.handleRemove = props.handleRemove;

    if (document.visibilityState || document.webkitVisibilityState) {
      const prefix = (document.visibilityState && '') || (document.webkitVisibilityState && 'webkit') || '';
      this.eventName = `${prefix}visibilitychange`;
      this.stateName = prefix ? `${prefix}VisibilityState` : 'visibilityState';
    } else {
      console.log('浏览器不支持visibilitychange特性！');
    }
  }

  changeEvent() {
    if (!this.stateName || !this.eventName) {
      return;
    }
    const state = document[this.stateName];
    if (state === 'visible' && typeof this.handleShow === 'function') {
      this.handleShow();
    } else if (state === 'hidden' && typeof this.handleHide === 'function') {
      this.handleHide();
    }
  }

  on() {
    if (!this.stateName || !this.eventName) {
      return;
    }
    if (document.visibilityState || document.webkitVisibilityState) {
      const prefix = (document.visibilityState && '') || (document.webkitVisibilityState && 'webkit') || '';
      const eventName = `${prefix}visibilitychange`;
      this.stateName = prefix ? `${prefix}VisibilityState` : 'visibilityState';
      document.addEventListener(eventName, this.changeEvent.bind(this));
    }
  }

  remove() {
    if (!this.stateName || !this.eventName) {
      return;
    }
    if (document.visibilityState || document.webkitVisibilityState) {
      const prefix = (document.visibilityState && '') || (document.webkitVisibilityState && 'webkit') || '';
      const eventName = `${prefix}visibilitychange`;
      this.stateName = prefix ? `${prefix}VisibilityState` : 'visibilityState';
      if (document[this.stateName] === 'visible' && typeof this.handleRemove === 'function') {
        this.handleRemove();
      }
      document.removeEventListener(eventName, this.changeEvent.bind(this));
    }
  }
};

// 性能埋点
export function tecStatCollector(data: TecStatCollectorParams) {
  const { interval, moduleName, apiPath, extra } = data;
  const params = {
    api_path: apiPath,
    page_name: moduleName,
    time_interval: interval,
    fe_flag: typeof extra === 'object' ? JSON.stringify(extra) : extra,
  };
  via.app.sendLogV3({ eventName: 'ecommerce_tect_stat', params });
}

export const TEC_STAT_MODULE_TYPE = {
  API_INTERVAL: 'api_interval',
  VIEW_INTERVAl: 'view_interval',
};

export function setStorage(key: string, val: any) {
  const value = JSON.stringify(val);
  return setNativeItem({ key, value });
}

export function getStorage(key: string) {
  return getNativeItem({ key }).then(({ value }) => {
    return JSON.parse(value || '{}');
  });
}

export function getStorageString(key: string) {
  return getNativeItem({ key }).then(({ value }) => {
    return value || '';
  });
}

export function removeStorage(key: string, type = 'one') {
  return removeNativeItem({ key, type });
}
