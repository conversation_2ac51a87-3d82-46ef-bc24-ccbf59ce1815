/**
 * 格式化毫秒为：x天x时x分
 * 最小单位为分
 * @param value 毫秒值
 * @returns {string}
 */
export const convertTimeByMillisecond = (value: number): string => {
    // 毫秒/天
    const DAY_UNIT = 86400000;
    // 毫秒/小时
    const HOUR_UNIT = 3600000;
    // 毫秒/分
    const MINUTE_UNIT = 60000;

    let rest = value;
    let day = '0';
    let hour = '0';
    let minute = '0';

    if (rest >= DAY_UNIT) {
        day = String(Number.parseInt(String(rest / DAY_UNIT), 10));
        rest = value % DAY_UNIT;
    }

    if (rest >= HOUR_UNIT) {
        hour = String(Number.parseInt(String(rest / HOUR_UNIT), 10));
        rest = value % HOUR_UNIT;
    }

    minute = String(Number.parseInt(String(rest / MINUTE_UNIT), 10));

    return `${day}天${hour}时${minute}分`;
};
