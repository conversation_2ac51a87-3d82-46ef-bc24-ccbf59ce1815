import qs from 'qs';
import { QualificationType, SETTLE_NEW_URL, SETTLE_COMPANY_URL, SETTLE_BUSINESS_URL, SETTLE_PERSION_URL } from '@common/constants';

/**
 * [通过参数名获取url中的参数值]
 * 示例URL:http://htmlJsTest/getrequest.html?uid=admin&rid=1&fid=2&name=小明
 * @param  {[string]} queryName [参数名]
 * @return {[string]}           [参数值]
 */
export const getSingleQueryValue = queryName => {
  var reg = new RegExp("(^|&)" + queryName + "=([^&]*)(&|$)", "i")
  var r = window.location.search.substr(1).match(reg)
  return r ? decodeURI(r[2]) : null
}

export const getCurrentUrl = (url, params) => {
  const urlHasQuery = url.includes('?');
  let querystring = qs.stringify(params, {encode: false});
  return url + (urlHasQuery ? '&' : '?') + querystring
}

const settleUrlMap = {
  [QualificationType.Enterprise]: SETTLE_COMPANY_URL,
  [QualificationType.IndividualBusiness]: SETTLE_BUSINESS_URL,
  [QualificationType.Personal]: SETTLE_PERSION_URL,
}
export function getSettleUrl(type: 'Enterprise' | 'IndividualBusiness' | 'Personal', isGray: boolean, isUpdate: boolean = false, extra: Record<string, string | number | boolean | undefined> = {}) {
  if (isGray) {
    return getCurrentUrl(SETTLE_NEW_URL, { vtype: QualificationType[type], daren_op: isUpdate ? 2 : 1, ...extra })
  }
  return getCurrentUrl(settleUrlMap[QualificationType[type]], { daren_op: isUpdate ? 2 : 1, ...extra })
}
