/* eslint-disable @typescript-eslint/no-explicit-any */
import { inject } from '@alliance-mobile/platform-merchant-common';
import { app } from '@bridge/webcast';
import { sendLog } from '../../pages/merch_picking/services/utils';
import fetch from '../request/fetch';
import { genSchema } from '../util/merch_picking';

inject({
  toast: ({ message }) => app.toast({ text: message, type: 'prompt' }),
  getStorage: key => app.getStorage({ key }),
  setStorage: (key, value) => app.setStorage({ key, value }),
  openSchema: (url, schemaParams) => app.openScheme({ url: genSchema({ url, ...schemaParams }) }),
  getUserInfo: async () => {
    const { code, is_login, user_id, sec_user_id, nickname, douyin_uid } = await app.getUserInfo();
    return {
      code,
      data: {
        user_id,
        sec_user_id,
        unique_id: String(douyin_uid),
        is_login: <PERSON><PERSON><PERSON>(is_login),
        success: code === 1,
        nickname,
      },
    };
  },
  sendEvent: (e, params) => sendLog(e, params),
  convertPixel: px => `${(px / 375) * 100}vw`,
  get: ({ url, params }) => fetch({ url, params }) as any,
  post: ({ url, data }) => fetch({ url, data, method: 'POST' }) as any,
});

export * from '@alliance-mobile/platform-merchant-common';
