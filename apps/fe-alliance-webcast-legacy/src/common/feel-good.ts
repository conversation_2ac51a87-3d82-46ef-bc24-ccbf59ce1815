// 初始化和调用分开
import Feelgood from '@ad/feelgood-sdk';
import { L_BASE } from '@alliance-mobile/constants';
import via from '@bridge/webcast';
import fetch from '../lib/request/fetch';

export const fetchAuthorLevelInfo = async () => {
  const url = `${L_BASE}/ies/v2/author/level/getAuthorLevelInfo`;
  const res = await fetch<{ data?: { author_level?: string } }>({ url });
  return res;
};

const { app } = via;

Feelgood.init({
  // appKey外的可选参数见 【完整初始化配置列表】
  appKey: '7ab457e8646ff0e471b7b4e59583a4ac2afeb46f',
  channel: 'cn',
});

interface CallbackOpts {
  /** 默认回调，在没有命中 feelgood 问卷时会被调用，在其他诸如 onFeelgoodSubmit 未设置时，也会调用此回调函数 */
  defaultCallback: () => void;
  /** 问卷弹出后，用户点击提交后的回调函数 */
  onFeelgoodSubmit?: () => void;
  /** 问卷弹出后，用户拒绝填写的回调 */
  onFeelgoodRefuse?: () => void;
  /** 问卷弹出后，用户关闭问卷的回调函数 */
  onFeelgoodClose?: () => void;
}

export function initFeelGood(eventName?: string, callbackOpts?: CallbackOpts) {
  const { defaultCallback, onFeelgoodClose, onFeelgoodRefuse, onFeelgoodSubmit } = callbackOpts || {};
  // 用户标识（可选），不传则不会收集提交者信息到FeelGood
  // 获取业务系统的用户信息
  Promise.all([app.getUserInfo(), fetchAuthorLevelInfo()])
    .then(res => {
      const userInfo = res && res[0];
      const userId = userInfo?.user_id;
      const userName = userInfo.nickname;
      const userLevelResult = res && res[1];
      const userLevel = userLevelResult?.data?.author_level?.toString?.();
      // 将获取到的系统用户信息传给FeelGood SDK, 并上报到FeelGood后端
      // 如何使用自定义用户标识：Feelgood 自定义用户标识
      Feelgood.setUserInfo({
        user_id: userId,
        user_name: userName,
        author_level: userLevel,
      });

      // 【必须调用】否则不会展示任何调研任务
      Feelgood.start();
      Feelgood.$on('close', () => {
        const onClose = onFeelgoodClose || defaultCallback;
        onClose?.();
      });
      Feelgood.$on('refuse', () => {
        const onRefuse = onFeelgoodRefuse || defaultCallback;
        onRefuse?.();
      });
      Feelgood.$on('submit-completed', () => {
        const onSubmit = onFeelgoodSubmit || defaultCallback;
        onSubmit?.();
      });
      if (eventName) {
        return Feelgood.triggerEvent(eventName);
      }
    })
    .then(feelGoodRes => {
      if (!feelGoodRes) {
        defaultCallback?.();
      }
    })
    .catch(e => {
      defaultCallback?.();
    });
}

export function triggerFeelgood(eventName: string) {
  eventName && Feelgood.triggerEvent(eventName);
}

export default Feelgood;
