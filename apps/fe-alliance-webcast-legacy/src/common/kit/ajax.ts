import {fetch} from './bridge';
import {isAndroid} from './env';
import Toast from '../../components/webcast_toast';
import {isObject} from 'lodash-es';

const DEFAULT_STATUS_MESSAGE = '服务器正忙，请稍后重试';
const BRIDGE_ERROR_MESSAGE = '容器请求调用失败';

interface RequestOptions {
    method?: 'post' | 'get' | 'POST' | 'GET';
    params?: object;
    data?: object;
}

interface AdapterData {
    raw?: object;
    code?: number;
    ret?: string;
    response?: object;
}

interface TargetData {
    data?: object;
    code?: number;
    msg?: string;
    st?: number;
    status_code?: number;
    message?: string;
    status_msg?: string;
    statusMessage?: string;
    error?: boolean
}

export interface AdapterResult {
    code: number;
    msg: string;
    isSuccess: boolean;
    data: object
}

export async function ajax(url: string, options: RequestOptions = {}): Promise<any> {
    const {params = {}, method = 'get'} = options;
    let data = {};
    if (isAndroid && method.toUpperCase() === 'POST' && params) {
        data = params;
    }
    const res = await fetch({url, params, method: method.toUpperCase(), data});
    const adaptedResult = dataAdapter(res);
    return adaptedResult;
}

// 调试阶段才给toast
function dataAdapter(data: AdapterData = {}): AdapterResult {
    if (data.code !== 1) {
        Toast.info({content: BRIDGE_ERROR_MESSAGE});
        return {code: -8, isSuccess: false, msg: BRIDGE_ERROR_MESSAGE, data: {}};
    }
    const targetDta = data.raw || data.response;
    if (targetDta) {
        const {data = {}, code, msg, st, status_code, message, status_msg, statusMessage, error, ...restData}: TargetData = targetDta;
        let result = restData as AdapterResult;
        result.code = [code, st, status_code].find(code => typeof code === 'number') ?? -8;
        result.isSuccess = result.code === 0 || result.code === -2 || error === false;
        result.msg = msg || message || status_msg || statusMessage || (
            result.isSuccess ? '请求成功' : DEFAULT_STATUS_MESSAGE
        );
        result.data = isObject(data) ? data : {};
        return result;
    } else {
        Toast.info({content: BRIDGE_ERROR_MESSAGE});
        return {code: -8, isSuccess: false, msg: BRIDGE_ERROR_MESSAGE, data: {}};
    }
}

export function filterAjaxData(res) {
    const {code, data, msg} = res;
    if (code !== 0) {
        Toast.info({content: msg});
        return Promise.reject({msg, code});
    } else {
        return data;
    }
}


