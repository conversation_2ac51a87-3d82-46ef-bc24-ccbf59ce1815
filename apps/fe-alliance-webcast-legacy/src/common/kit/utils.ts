import qs from 'qs';
import {isEmpty} from 'lodash-es';
import {
    appSmallName,
  } from '@src/lib/env_detect';
import { APP_NAME_MAP } from '@byted/fe-utilx';
import { isInAweme, isInToutiao, isInHuoshan, isInXigua } from '@src/lib/env_detect';
/**
 * 获取类 url 字符串里面的参数
 * @param {string} 默认window.location.href
 * @return {object} {}
 */
export function formatQueryString(url: string = window.location.href) {
    if (!url) {
        return {};
    }
    let query = url;
    if (url.indexOf('?') > 0) {
        query = query.split('?')[1];
    }
    return qs.parse(query, {
        decoder: decodeURIComponent,
    });
}

/**
 * 判断是否为空，0不算
 * @param {any} val
 * @return {boolean} boolean
 */
export function isNone(val: any): boolean {
    return !(typeof val === 'number') && isEmpty(val);
}

export const APP_ID = {
    toutiao: 13,
    aweme: 1128,
    xigua: 32,
    huoshan: 1112
};

// 财经侧需要参数
export function getFinanceParams(): {
    b_type : number,
    aid: number,
} {
    const aid = APP_ID[appSmallName] || APP_ID[APP_NAME_MAP.videoArticle];
    let b_type = 2;
    if(isInToutiao) {
        b_type = 3
    }
    if(isInHuoshan) {
        b_type = 1
    }
    if(isInXigua){
        b_type = 4
    }
    return {
        b_type,
        aid
    };
}