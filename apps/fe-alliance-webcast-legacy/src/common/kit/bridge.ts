import via from '@ies/webcast-via';
import qs from 'qs';
import { isAweme } from './env';

export interface OpenFullWebview {
  url: string;
  title?: string;
  hideNavBar?: boolean;
  hideStatusBar?: boolean;
}

/**
 * 打开全屏webview
 * @param {object} {}
 */
export function openFullWebview({ url, title, hideNavBar = true, hideStatusBar = false }: OpenFullWebview) {
  if (!url) {
    return;
  }
  via.app.openWebview({
    url,
    title,
    hide_nav_bar: Number(hideNavBar),
    hide_status_bar: Number(hideStatusBar),
  });
}

export interface OpenWebview {
  url: string;
  type?: string;
  webview?: 'webview' | 'webcast_webview';
  hideNavBar?: boolean;
  statusBarColor?: string;
  statusBarBgColor?: string;
  hideStatusBar?: boolean;
  webBgColor?: string;
  hideBackClose?: number; // 是否隐藏返回顶部按钮（头条）
  showBack?: boolean; // 是否显示返回按钮
  disable_bounces?: 0 | 1; // 是否禁用ios回弹效果
}

export enum WebviewType {
  webcast = 'webcast_webview',
  aweme = 'webview',
}

/**
 * 通用打开webview
 * @param {object} {webview,url,hideNavBar,hideStatusBar,statusBarColor,statusBarBgColor,webBgColor}
 */
export function openWebview({
  webview = WebviewType.webcast,
  url,
  hideNavBar = true,
  webBgColor = 'ffffff',
  showBack = false,
  disable_bounces = 0,
}: OpenWebview) {
  if (!url) {
    return;
  }
  let protocal = '';
  let params = {};
  let isWebcastWebview = false;
  let schema = '';
  const urlHasQuery = url.includes('?');
  let querystring = '';
  switch (webview) {
    case WebviewType.webcast:
      protocal = `sslocal://${webview}`;
      isWebcastWebview = true;
      params = {
        hide_nav_bar: Number(hideNavBar),
        web_bg_color: encodeURIComponent(`#${webBgColor}ff`),
        show_back: showBack,
      };
      break;
    case WebviewType.aweme:
      protocal = `${isAweme ? 'aweme' : 'sslocal'}://${webview}`;
      params = {
        hide_nav_bar: Number(hideNavBar),
        loading_bgcolor: webBgColor,
        hide_bar: Number(hideNavBar),
        hide_back_close: Number(hideNavBar),
        hide_back_button: Number(hideNavBar),
        disable_bounces: Number(disable_bounces),
      };
      break;
    default:
      protocal = `sslocal://${webview}`;
  }

  querystring = qs.stringify(params, { encode: false });
  schema = `${protocal}?url=${encodeURIComponent(url + (urlHasQuery ? '&' : '?') + querystring)}&${querystring}`;
  window.location.href = schema;
}

/**
 * 关闭当前webview
 * @param {object} {reactId: string} - 默认 {reactId: window.reactId}
 */
export function closeWebview(reactId = window.reactId) {
  via.app.close({
    reactId,
  });
}

/**
 * 监听页面返回
 * @param {function} callback
 */
export function onResume(callback: () => void) {
  via.on('H5_visibilityChange', (err, res) => {
    if (res.code === 1 && res.visible === 1) {
      callback();
    }
  });
}

/**
 * 客户端请求
 * @param {string} url
 * @param {object} params
 * @param {function} method
 */
export const { fetch } = via.app;
