/*
 * @Author: xuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-06-20 21:36:16
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-07-12 17:38:21
 * @Description: 页面模式：如99分屏 73分屏 全屏
 */
import { MerchPickingEnterFromType, PageMode } from '../../types/common';

// 页面来源和页面类型的映射
export const PAGE_MODE: Record<MerchPickingEnterFromType, PageMode> = {
  [MerchPickingEnterFromType.WebcastBeforeLiving]: PageMode.Popup99,
  [MerchPickingEnterFromType.WebcastLiving]: PageMode.Popup73,
  [MerchPickingEnterFromType.Livecontrol]: PageMode.Popup99,
};
