export const API_BASE = 'https://lianmengapi.snssdk.com';
export const L_API_BASE = 'https://lianmeng.snssdk.com';
export const SAAS_BASE = 'https://isaas.ecombdapi.com';

export const genTosBase = (target: number | string): string => `https://lf${target}-fe.ecombdstatic.com`;

// 默认商品主图
export const DEFAULT_MERCH_THUMB = 'https://p1.toutiaoimg.com/obj/7e6c00014964c1a68255';

// 默认商品主图的渐变遮盖
export const MERCH_THUMB_MASK = `${genTosBase(3)}/obj/temai/FsKCQ8hvV9F6N1K7UFRPWTfeYeX4www226-226`;

// 网络错误占位图
export const NETWORK_ERROR_IMG = `${genTosBase(1)}/obj/temai/FlBoS9r3TD38FSA4v9f3q35h8HXjwww448-176`;

// 数据为空的占位图
export const EMPTY_CONTENT_IMG = `${genTosBase(6)}/obj/temai/FovqrhG7EkuCA8MWDqm1LaVmYA4Swww672-267`;

const PATH_PREFIX = '/falcon/e_commerce';
export { PATH_PREFIX };

// 达人资质认证中心
// http://fxg.jinritemai.com.boe-gateway.byted.org/h5/governance/daren/certify?daren_op=Y&account_type=X&vtype=Z
// 参数说明
// daren_op =Y： Y=1(达人认证过程，可不传) Y=2 (达人升级，必传）
// account_type = X ：X表示资质复用类型，当当前认证流程为个人且为千川账户时，account_type=3为必传，其他认证类型时可不传account_type
// vtype = Z ：Z 表示 export enum VType {
//   企业 = 0,
//   个体 = 41,
//   个人 = 11
// }
export const SETTLE_NEW_URL = 'http://fxg.jinritemai.com/h5/governance/daren/certify';

export const SETTLE_COMPANY_URL = 'https://fxg.jinritemai.com/h5/shop/settle/company';
export const SETTLE_BUSINESS_URL = ' https://fxg.jinritemai.com/h5/shop/settle/business';
export const SETTLE_PERSION_URL = ' https://fxg.jinritemai.com/h5/shop/settle/person';

// 财经账户地址
export const SETTLE_OPEN_ACCOUNT = 'https://fxg.jinritemai.com/h5/_daren/account/account-list';
// 财经账户新地址
export const SETTLE_OPEN_ACCOUNT_NEW = 'https://lianmeng.snssdk.com/h5/_daren/account/account-list';

export enum QualificationType {
  Enterprise = 0, // 企业
  IndividualBusiness = 41, // 个体
  Personal = 11, // 个人
}

// 优价同款页面
export const BETTER_PRICE_PATH = 'https://alliance.jinritemai.com/pages/better-price-replace';
