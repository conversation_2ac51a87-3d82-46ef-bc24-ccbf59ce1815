enum OrganizationBindStatus {
  NoBind,
  OrganizationApplyBind,
  OrganizationApplyUnBind,
  ActorApplyBind,
  ActorApplyUnBind,
  Binding,
  UnBindOrganizationConfirm,
  UnBindActorConfirm,
  OrganizationRejectApply,
  ActorRejectApply,
  OrganizationApplyPast,
  ActorApplyPast,
  BindPast
}

export function isBind(status) {
  return (
      OrganizationBindStatus.Binding === status
  );
}

export function isApplyUnbind(status) {
  return (
      OrganizationBindStatus.ActorApplyUnBind === status
  );
}