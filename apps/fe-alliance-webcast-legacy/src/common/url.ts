import { getUrlByEnv } from "@src/lib/env_detect";
import { openWebview, OpenWebview } from "./kit/bridge";
import { isNone } from "./kit/utils";

interface IJumpPar extends OpenWebview {
  status?: number
}
export const INS_COOP_URL = 'https://ffh.jinritemai.com/falcon/e_commerce/webcast/ins_coop';
export const schema_INS_COOP_URL = getUrlByEnv('https://ffh.jinritemai.com/falcon/e_commerce/webcast/ins_coop');
export const INS_COOP_BIND_RECORDS_URL = 'https://ffh.jinritemai.com/falcon/e_commerce/webcast/account_bind_history';
export const schema_INS_COOP_BIND_RECORDS_URL = getUrlByEnv('https://ffh.jinritemai.com/falcon/e_commerce/webcast/account_bind_history');

export function jumpToUrl({url, hideNavBar}: IJumpPar) {
  openWebview({url, hideNavBar});
}

export const SCHEMA_account_bind_organization = getUrlByEnv('https://ffh.jinritemai.com/falcon/e_commerce/webcast/account_bind_organization');
export const SCHEMA_ins_coop = getUrlByEnv('https://ffh.jinritemai.com/falcon/e_commerce/webcast/ins_coop');
export const SCHEMA_ins_coop_auth_detail = getUrlByEnv('https://ffh.jinritemai.com/falcon/e_commerce/webcast/ins_coop_auth_detail');
export const SCHEMA_ins_coop_matrix_bind = getUrlByEnv('https://ffh.jinritemai.com/falcon/e_commerce/webcast/ins_coop_matrix_bind');
export const SCHEMA_account_bind_history = getUrlByEnv('https://ffh.jinritemai.com/falcon/e_commerce/webcast/account_bind_history');



// protocol
export const PROTOCOLAccountBind = 'https://school.jinritemai.com/doudian/web/article/aHKxaoN8GEZi';