import { sendEvent } from '@alliance-mobile/event';
import via from '@ies/webcast-via'; // 直播 webview
import { appName } from '@src/lib/env_detect';
const video = document.createElement('video');

/**
 * 关闭当前webview
 * @param {object} {reactId: string} - 默认 {reactId: window.reactId}
 */
const closeWebview = (props: { reactId: string } = { reactId: window.reactId }) => {
  const { reactId } = props;
  via.app.close({
    reactId,
  });
};

/**
 * 显示提示消息
 * @param {string} message
 */
const showToast = message => via.app.toast({ text: message });

/**
 * 打开新的 view， 包含半屏
 * @param {object} {schema: string}
 */
const openSchema = (props: Record<string, any> & { schema: string }) => {
  return via.app.openScheme({
    url: props.schema,
  });
};

// TODO 感觉有BUG
/**
 * 播放video
 * @param props
 */
let playVideo = (props: object & { schema: string; reactId?: string }) => {
  video.src = props.schema;
  video.play();
};
if (appName === 'aweme') {
  // 打开抖音端上视频
  playVideo = (props: object & { schema: string; reactId?: string }) => {
    window.ToutiaoJSBridge &&
      window.ToutiaoJSBridge.call('openSchema', { schema: props.schema, reactId: window.reactId }, () => null, {
        namespace: 'host',
      });
  };
}

// 现在IOS还不支持
/**
 * 页面通信
 * @param props
 */
const sendMessage = (props: { eventName: string; data: object }) => {
  console.log('sendMessage', props);
  via.app.webcastBroadcast({
    eventName: props.eventName,
    data: props.data,
  });
};

// IOS不支持
const onMessage = (props: object & { eventName: string; callback?: Function }) => {
  via.on('H5_webcastNotification', (error, res: { eventName: string; data: unknown }) => {
    const { callback } = props;
    if (res.eventName === props.eventName && callback && typeof callback === 'function') {
      callback(res.data);
    }
  });
};

/**
 * 页面通信
 * @param {string} url
 * @param {object} params
 * @param {function} method
 */
const { fetch } = via.app;

/**
 * 储存写入
 * @param {string} key
 * @param {string} value
 */
const setNativeItem = via.app.setStorage;

/**
 * 储存读取
 * @param {string} key
 * @return {Promise} {key: string}
 */
const getNativeItem = via.app.getStorage;

const removeNativeItem = via.app.removeStorage;

const openFeedbackInput = (options?: object) => null;

const host = 'webcast';

/**
 * 使用 Applog 3.0发送 数据埋点
 * @param {string} eventName
 * @return {object} params
 */
const sendlogV3 = (params: Record<string, unknown> = {}) => {
  typeof params.eventName === 'string' && sendEvent(params.eventName, params.params);
};

const { getUserInfo } = via.app;

interface OpenPopup {
  url: string;
  width?: number;
  height?: number;
  close_by_mask?: string;
  gravity?: string;
}
export function openPopupWebview({ url, width, height, close_by_mask = '0', gravity = 'bottom' }: OpenPopup) {
  via.app.openPopup({
    url,
    width,
    height,
    close_by_mask,
    gravity,
  });
}

interface OpenFullWebview {
  url: string;
  title?: string;
  hideNavBar?: 0 | 1;
  hideStatusBar?: 0 | 1;
}

export function openFullWebview({ url, title, hideNavBar = 1, hideStatusBar = 1 }: OpenFullWebview) {
  via.app.openWebview({
    url,
    title,
    hide_nav_bar: hideNavBar,
    hide_status_bar: hideStatusBar,
  });
}

export function openCustomWebview(url: string) {
  via.app.openScheme({ url });
}

export function jumpLiveRoom(rommId: string) {
  via.app.openRoom({
    room_id: rommId,
  });
}

interface ShowModal {
  title?: string;
  confirmText?: string;
  cancelText?: string;
  showCancel?: boolean;
  content: string;
}
export function showModal({
  title,
  confirmText = '确定',
  cancelText = '取消',
  showCancel = true,
  content,
}: ShowModal): Promise<any> {
  return via.business
    .appShowModal({
      title,
      confirmText,
      cancelText,
      showCancel,
      content,
    })
    .then(res => {
      if (res.code === 1) {
        return res.data;
      } else {
        return Promise.reject();
      }
    });
}

export {
  closeWebview,
  showToast,
  openSchema,
  playVideo,
  sendMessage,
  onMessage,
  fetch,
  setNativeItem,
  getNativeItem,
  removeNativeItem,
  openFeedbackInput,
  host,
  sendlogV3,
  getUserInfo,
};
