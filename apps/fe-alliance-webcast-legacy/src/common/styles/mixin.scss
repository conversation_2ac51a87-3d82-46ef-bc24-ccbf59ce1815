@mixin border-bottom-1($color) {
  position: relative;
  @media screen and (-webkit-min-device-pixel-ratio: 2) {
    &:after {
      content: '';
      pointer-events: none; /* 防止点击触发 */
      box-sizing: border-box;
      position: absolute;
      width: 200%;
      height: 200%;
      left: 0;
      top: 0;
      border-bottom: 1px solid $color;
      transform: scale(0.5);
      transform-origin: 0 0;
    }
  }
  @media screen and (-webkit-min-device-pixel-ratio: 3) {
    &:after {
      content: '';
      pointer-events: none; /* 防止点击触发 */
      box-sizing: border-box;
      position: absolute;
      width: 300%;
      height: 300%;
      left: 0;
      top: 0;
      border-bottom: 1px solid $color;
      transform: scale(0.3333333333333);
      transform-origin: 0 0;
    }
  }
}

@mixin line-clamp($line, $line-height: default) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: $line;
  -webkit-box-orient: vertical;
};

@mixin safearea ($inset, $prop, $value: '0rem') {
  #{$prop}: #{$value};
  #{$prop}: calc(constant(safe-area-inset-#{$inset}) + #{$value});
  #{$prop}: calc(env(safe-area-inset-#{$inset}) + #{$value});
}
