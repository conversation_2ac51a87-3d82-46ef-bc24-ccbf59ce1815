$hairline-variants: (
    a: (left: 0, top: 0),
    t: (top: 0, left: 0, width: 100%, height: 1px),
    r: (right: 0, top: 0, width: 1px, height: 100%),
    b: (bottom: 0, left: 0, width: 100%, height: 1px),
    l: (left: 0, top: 0, width: 1px, height: 100%)
);

@each $type, $params in $hairline-variants {
    .hairline--#{$type} {
        position: relative;
        &::after {
            content: "";
            display: block;
            position: absolute;
            z-index: 1;
            top: map-get($params, top);
            right: map-get($params, right);
            bottom: map-get($params, bottom);
            left: map-get($params, left);
            width: map-get($params, width);
            height: map-get($params, height);
        }
    }
    .hairline {
        &--t::after, &--b::after {
            transform: scaleY(0.5);
        }
        &--r::after, &--l::after {
            transform: scaleX(0.5);
        }
        &--a::after {
            width: percentage(2);
            height: percentage(2);
            transform-origin: left top;
            transform: scale(0.5);
            border: 1px solid;
            background-color: transparent;
        }
    }
}
