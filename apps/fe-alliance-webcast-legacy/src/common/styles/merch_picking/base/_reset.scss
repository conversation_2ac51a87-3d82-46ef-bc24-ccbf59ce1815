body {
    user-select: none;
    font-family: PingFangSC-Regular, 'Droid Sans Fallback', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    /* stylelint-disable-next-line property-no-unknown */
    line-height: 1.25;
    -webkit-font-smoothing: antialiased;
}

* {
    box-sizing: border-box;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    box-sizing: border-box;

    &::after,
    &::before {
        box-sizing: border-box;
    }
}

a {
    &,
    &:hover,
    &:focus {
        color: inherit;
        text-decoration: none;
        -webkit-touch-callout: none;
    }
}

ul {
    padding-left: 0;
    list-style: none;
}

::placeholder,
::-webkit-input-placeholder {
    line-height:normal!important;
    color: rgba(14, 15, 26, 0.34);
}
