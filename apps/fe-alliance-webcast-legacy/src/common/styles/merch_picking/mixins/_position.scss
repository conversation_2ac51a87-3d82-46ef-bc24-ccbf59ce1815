@mixin absolute-center ($align: null, $position: absolute) {
    position: $position;
    @if $align == 'x' {
        left: 50%;
        transform: translate(-50%, 0);
    } @else if $align == 'y' {
        top: 50%;
        transform: translate(0, -50%);
    } @else {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
    @content;
}

@mixin mask(
    $left: 0,
    $top: 0,
    $right: 0,
    $bottom: 0,
    $position: fixed
) {
    position: $position;
    left: $left;
    right: $right;
    top: $top;
    bottom: $bottom;
}