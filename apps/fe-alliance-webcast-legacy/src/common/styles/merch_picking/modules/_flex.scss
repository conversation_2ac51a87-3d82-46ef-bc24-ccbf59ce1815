@import '../mixins/flex';

$flex-rules: (
    'fs': flex-start, 'fe': flex-end,
    'sb': space-between, 'sa': space-around,
    'w': wrap, 'nw': nowrap, 'wr': wrap-reverse,
    'b': baseline, 's': stretch, 'c':  center, 'a': auto
);

@each $selector, $level, $flex-flow in
    ('.frow', flex, row wrap),
    ('.fcol', flex, column wrap),
    ('.ifrow', inline-flex, row wrap),
    ('.ifcol', inline-flex, column wrap) {
    #{$selector} {
        @include flex($level, $flex-flow);
    }
}

/* Atomic-ish styling for flexbox */
// f-ai-s  => align-items: stretch
// f-jc-sb => justify-content: space-between
@each $selector, $rule, $vals in
    ('jc', justify-content, ('fs', 'fe', 'c', 'sb', 'sa')),
    ('ai', align-items, ('fs', 'fe', 'c', 'b', 's')),
    ('ac', align-content, ('fs', 'fe', 'c', 'sb', 'sa', 's')),
    ('as', align-self, ('fs', 'fe', 'c', 'b', 's', 'a')),
    ('fw', flex-wrap, ('w', 'nw', 'wr')) {
    @each $val in $vals {
        .f-#{$selector}-#{$val} {
            #{$rule}: map-get($flex-rules, $val);
        }
    }
}

@for $i from 1 through 3 {
    .f-#{$i} {
        flex: $i;
    }
}
