import React, { MemoExoticComponent } from 'react';
import _ from 'lodash';
type TPropsAreEqual = (prevProps: Readonly<unknown>, nextProps: Readonly<unknown>) => boolean;
type TComponent = () => JSX.Element;
export const deepMemo: (
  Component: TComponent,
  propsAreEqual?: TPropsAreEqual | undefined
) => MemoExoticComponent<() => JSX.Element> = (Component: TComponent, propsAreEqual = _.isEqual) => {
  return React.memo(Component, propsAreEqual);
};
