export default {
  urlRewrite: {
    'https?://demo.jinritemai.com/chunk/([^?]*)': 'http://localhost:4000/chunk/$1',
    'https?://demo.jinritemai.com/pages/([^?]*)': 'http://localhost:4000/pages/$1',
    'https?://demo.jinritemai.com/': 'http://localhost:4000/pages/merch_picking/',
    'https?://lf-ecom-gr-sourcecdn.bytegecko.com/chunk/([^?]*)': 'http://localhost:4000/chunk/$1',
    'https?://lf-ecom-gr-sourcecdn.bytegecko.com/pages/([^?]*)': 'http://localhost:4000/pages/$1',
    'https?://lf-ecom-gr-sourcecdn.bytegecko.com/obj/byte-gurd-source-gr/ecom/alliance_mobile/webcast/alliance_webcast_legacy/html/pages/merch_picking/':
      'http://localhost:4000/pages/merch_picking/',
  },
  enableSystemProxy: false, // 不想开eden全局代理就false
};
