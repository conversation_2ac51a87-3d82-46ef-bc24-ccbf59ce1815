```
# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Commands
- `npm start`: Start development server with hot reloading
- `npm run build`: Build production assets
- `npm run prod`: Build for production environment
- `npm run proxy`: Start proxy server using eden.proxy.ts configuration
- `npm run gen_icon`: Generate icon components from scripts/gen_icon.js

## Architecture Overview
This is a multi-platform (Douyin, Huoshan, Xigua, Toutiao) live e-commerce application built with React and TypeScript.

Key directories:
- `src/pages`: Page entry points organized by business domain
- `src/components`: Reusable UI components with scoped styles
- `src/common`: Shared constants, styles, and utility functions
- `src/lib`: External library integrations and custom utilities
- `src/types`: TypeScript interfaces and type definitions
- `static`: Static assets including images and global styles

The application uses a component-based architecture with shared utilities and platform-specific adaptations handled through styling variations.
```